from .escritorio import db
from sqlalchemy.sql import func

class ImportacaoXML(db.Model):
    __tablename__ = 'importacao_xml'
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON>ey('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.Integer, db.ForeignKey('escritorio.id'), nullable=False)
    usuario_id = db.Column(db.Integer, db.ForeignKey('usuario.id'), nullable=False)
    arquivo_nome = db.Column(db.String(255), nullable=False)
    chave_nf = db.Column(db.String(50))
    numero_nf = db.Column(db.String(20))
    data_emissao = db.Column(db.Date)
    cnpj_emitente = db.Column(db.String(18))
    razao_social_emitente = db.Column(db.String(255))
    data_importacao = db.Column(db.DateTime, server_default=func.now())
    status = db.Column(db.String(20), default='concluido')
    mensagem = db.Column(db.Text)

    # Novos campos para controle de entrada/saída
    tipo_nota = db.Column(db.String(1), default='0')  # 0=entrada, 1=saída
    data_entrada = db.Column(db.Date)  # Data de entrada modificável
    data_emissao_original = db.Column(db.Date)  # Backup da data original
    status_validacao = db.Column(db.String(20), default='pendente')  # pendente, validado, cancelado
    observacoes_validacao = db.Column(db.Text)

    # Totais da nota
    valor_total_nf = db.Column(db.Numeric(15, 2))  # Valor total (vNF) do XML

    # Campos para Simples Nacional (notas de entrada)
    csosn = db.Column(db.String(3))  # CSOSN do Simples Nacional
    crt_emitente = db.Column(db.String(1))  # CRT do emitente (1=Simples Nacional)
    
    def __repr__(self):
        return f"<ImportacaoXML {self.id} - {self.arquivo_nome}>"
    
    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'usuario_id': self.usuario_id,
            'arquivo_nome': self.arquivo_nome,
            'chave_nf': self.chave_nf,
            'numero_nf': self.numero_nf,
            'data_emissao': self.data_emissao.isoformat() if self.data_emissao else None,
            'cnpj_emitente': self.cnpj_emitente,
            'razao_social_emitente': self.razao_social_emitente,
            'data_importacao': self.data_importacao.isoformat() if self.data_importacao else None,
            'status': self.status,
            'mensagem': self.mensagem,
            'tipo_nota': self.tipo_nota,
            'data_entrada': self.data_entrada.isoformat() if self.data_entrada else None,
            'data_emissao_original': self.data_emissao_original.isoformat() if self.data_emissao_original else None,
            'status_validacao': self.status_validacao,
            'observacoes_validacao': self.observacoes_validacao,
            'valor_total_nf': float(self.valor_total_nf) if self.valor_total_nf is not None else None,
            'csosn': self.csosn,
            'crt_emitente': self.crt_emitente
        }
