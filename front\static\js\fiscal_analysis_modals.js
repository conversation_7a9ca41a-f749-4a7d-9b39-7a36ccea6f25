/**
 * Modais de Análise Fiscal - Detalhes das Violações
 * Sistema para exibir informações detalhadas sobre as regras violadas
 */

/**
 * Mostra modal com detalhes das violações de uma análise específica
 */
function showAnalysisDetailsModal(analysisType, auditorias) {
    if (!window.FiscalAnalysisRules) {
        console.warn('Regras de análise fiscal não carregadas');
        return;
    }

    const rules = window.FiscalAnalysisRules;
    const config = rules.ANALYSIS_CARDS_CONFIG[analysisType];
    
    if (!config) {
        console.warn('Configuração não encontrada para análise:', analysisType);
        return;
    }

    // Analisar violações
    const violationDetails = analyzeViolations(analysisType, auditorias);
    
    // Criar modal
    const modalHtml = createAnalysisDetailsModalHtml(config, violationDetails);
    
    // Remover modal existente se houver
    const existingModal = document.getElementById('analysisDetailsModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Adicionar modal ao DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Mostrar modal
    const modal = new bootstrap.Modal(document.getElementById('analysisDetailsModal'));
    modal.show();
}

/**
 * Analisa as violações para um tipo de análise específico
 */
function analyzeViolations(analysisType, auditorias) {
    const rules = window.FiscalAnalysisRules;
    const violations = {
        total: 0,
        byRule: {},
        items: []
    };

    // Obter tributo atual
    const currentTributo = auditoriaComparativaData.currentTributo || 'icms';

    auditorias.forEach(auditoria => {
        let hasViolation = false;
        let violationReason = '';

        switch (analysisType) {
            case 'cfop':
                hasViolation = rules.isCfopInconsistent(auditoria.sped_cfop, auditoria.xml_cfop, auditoria.tipo_produto);
                if (hasViolation) {
                    violationReason = rules.getRuleViolationDescription('cfop', auditoria);
                }
                break;

            case 'cfop_cst':
                const regimeParceiro = auditoria.regime_parceiro;
                if (regimeParceiro !== 'Simples Nacional' && !auditoria.parceiro_simples_nacional) {
                    hasViolation = rules.isCfopCstInconsistent(
                        auditoria.sped_cfop,
                        auditoria.tributos?.icms?.cst,
                        auditoria.xml_cst || auditoria.xml_data?.cst,
                        auditoria.tipo_produto,
                        auditoria.xml_cfop
                    );
                    if (hasViolation) {
                        violationReason = rules.getRuleViolationDescription('cfop_cst', auditoria);
                    }
                    if (!hasViolation && currentTributo === 'ipi') {
                        const ipiReason = rules.getIpiRuleViolationByAnalysis('cfop_cst', auditoria);
                        if (ipiReason) {
                            hasViolation = true;
                            violationReason = ipiReason;
                        }
                    }
                }
                break;

            case 'product_type':
                hasViolation = rules.isProductTypeInconsistent(
                    auditoria.sped_cfop,
                    auditoria.tributos?.icms?.cst,
                    auditoria.tipo_produto
                );
                if (hasViolation) {
                    violationReason = rules.getRuleViolationDescription('product_type', auditoria);
                }
                break;

            case 'origin':
                hasViolation = rules.isOriginInconsistent(
                    auditoria.sped_origem,
                    auditoria.xml_origem || auditoria.xml_data?.origem
                );
                if (hasViolation) {
                    violationReason = rules.getRuleViolationDescription('origin', auditoria);
                }
                break;

            case 'aliquota':
                // Obter alíquotas baseado no tributo atual (mesma lógica do performFiscalAnalysis)
                let spedAliquota, xmlAliquota;
                switch (currentTributo) {
                    case 'icms':
                        spedAliquota = auditoria.tributos?.icms?.aliquota;
                        xmlAliquota = auditoria.xml_data?.icms_aliquota || auditoria.xml_icms_aliquota;
                        break;
                    case 'icms_st':
                        spedAliquota = auditoria.tributos?.icms_st?.aliquota;
                        xmlAliquota = auditoria.xml_data?.icms_st_aliquota || auditoria.xml_icms_st_aliquota;
                        break;
                    case 'ipi':
                        spedAliquota = auditoria.tributos?.ipi?.aliquota;
                        xmlAliquota = auditoria.xml_data?.ipi_aliquota || auditoria.xml_ipi_aliquota;
                        break;
                    case 'pis':
                        spedAliquota = auditoria.tributos?.pis?.aliquota;
                        xmlAliquota = auditoria.xml_data?.pis_aliquota || auditoria.xml_pis_aliquota;
                        break;
                    case 'cofins':
                        spedAliquota = auditoria.tributos?.cofins?.aliquota;
                        xmlAliquota = auditoria.xml_data?.cofins_aliquota || auditoria.xml_cofins_aliquota;
                        break;
                    case 'pis_cofins':
                        spedAliquota = auditoria.tributos?.pis?.aliquota;
                        xmlAliquota = auditoria.xml_data?.pis_aliquota || auditoria.xml_pis_aliquota;
                        break;
                }

                hasViolation = rules.isAliquotaInconsistent(spedAliquota, xmlAliquota, auditoria.tipo_produto, auditoria.sped_cfop);
                if (hasViolation) {
                    violationReason = rules.getRuleViolationDescription('aliquota', auditoria);
                } else if (currentTributo === 'ipi') {
                    const ipiAliqReason = rules.getIpiRuleViolationByAnalysis('aliquota', auditoria);
                    if (ipiAliqReason) {
                        hasViolation = true;
                        violationReason = ipiAliqReason;
                    }
                }
                break;

            case 'ipi':
                const ipiReason = rules.getIpiRuleViolation(auditoria);
                if (ipiReason) {
                    hasViolation = true;
                    violationReason = ipiReason;
                }
                break;
            case 'pis_cofins':
                if (rules.hasPisCofinsViolation(auditoria)) {
                    hasViolation = true;
                    violationReason = rules.getPisCofinsViolations(auditoria).join(' | ');
                }
                break;
        }

        if (hasViolation) {
            violations.total++;

            // Contar por regra
            if (!violations.byRule[violationReason]) {
                violations.byRule[violationReason] = {
                    count: 0,
                    items: []
                };
            }
            violations.byRule[violationReason].count++;
            violations.byRule[violationReason].items.push(auditoria);

            // Adicionar item
            violations.items.push({
                auditoria: auditoria,
                reason: violationReason
            });
        }
    });

    return violations;
}

/**
 * Cria o HTML do modal de detalhes da análise
 */
function createAnalysisDetailsModalHtml(config, violations) {
    const rulesHtml = Object.keys(violations.byRule).map(rule => {
        const ruleData = violations.byRule[rule];
        return `
            <div class="rule-violation-item mb-3">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1 text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${rule}
                        </h6>
                        <p class="mb-2 text-muted small">
                            <strong>${ruleData.count}</strong> ${ruleData.count === 1 ? 'violação encontrada' : 'violações encontradas'}
                        </p>
                    </div>
                    <button class="btn btn-sm btn-outline-primary" onclick="showRuleViolationDetails('${rule.replace(/'/g, "\\'")}', '${config.title}')">
                        <i class="fas fa-eye me-1"></i>Ver Detalhes
                    </button>
                </div>
                <div class="progress" style="height: 6px;">
                    <div class="progress-bar bg-danger" style="width: ${(ruleData.count / violations.total) * 100}%"></div>
                </div>
            </div>
        `;
    }).join('');

    return `
        <div class="modal fade" id="analysisDetailsModal" tabindex="-1" aria-labelledby="analysisDetailsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="analysisDetailsModalLabel">
                            <i class="${config.icon} me-2"></i>
                            Detalhes da ${config.title}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle fa-2x me-3"></i>
                                <div>
                                    <h6 class="mb-1">Resumo da Análise</h6>
                                    <p class="mb-0">
                                        <strong>${violations.total}</strong> ${violations.total === 1 ? 'violação encontrada' : 'violações encontradas'} 
                                        em <strong>${Object.keys(violations.byRule).length}</strong> 
                                        ${Object.keys(violations.byRule).length === 1 ? 'tipo de regra' : 'tipos de regras'}
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <h6 class="mb-3">
                            <i class="fas fa-list me-2"></i>
                            Regras Violadas
                        </h6>
                        
                        ${rulesHtml || '<p class="text-muted">Nenhuma violação encontrada.</p>'}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Fechar
                        </button>
                        <button type="button" class="btn btn-primary" onclick="exportViolationReport('${config.title}')">
                            <i class="fas fa-download me-1"></i>Exportar Relatório
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Mostra detalhes específicos de uma regra violada
 */
function showRuleViolationDetails(rule, analysisTitle) {
    if (!auditoriaComparativaData || !auditoriaComparativaData.auditorias) {
        showMessage('Dados não disponíveis', 'warning');
        return;
    }

    const auditorias = auditoriaComparativaData.auditorias;
    const violatedItems = [];

    // Encontrar itens que violaram esta regra específica
    auditorias.forEach(auditoria => {
        const violationReason = getRuleViolationForItem(auditoria);
        if (violationReason === rule) {
            violatedItems.push(auditoria);
        }
    });

    if (violatedItems.length === 0) {
        showMessage('Nenhum item encontrado para esta regra', 'info');
        return;
    }

    // Criar modal secundário
    const modalHtml = createRuleDetailsModalHtml(rule, analysisTitle, violatedItems);

    // Remover modal existente se houver
    const existingModal = document.getElementById('ruleDetailsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Adicionar modal ao DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Mostrar modal
    const modal = new bootstrap.Modal(document.getElementById('ruleDetailsModal'));
    modal.show();
}

/**
 * Obtém a descrição da violação para um item específico
 */
function getRuleViolationForItem(auditoria) {
    if (!window.FiscalAnalysisRules) return '';

    const rules = window.FiscalAnalysisRules;

    // Verificar cada tipo de análise
    if (rules.isCfopInconsistent(auditoria.sped_cfop, auditoria.xml_cfop, auditoria.tipo_produto)) {
        return rules.getRuleViolationDescription('cfop', auditoria);
    }

    if (rules.isCfopCstInconsistent(
        auditoria.sped_cfop,
        auditoria.tributos?.icms?.cst,
        auditoria.xml_cst || auditoria.xml_data?.cst,
        auditoria.tipo_produto,
        auditoria.xml_cfop
    )) {
        return rules.getRuleViolationDescription('cfop_cst', auditoria);
    }

    if (auditoriaComparativaData.currentTributo === 'ipi') {
        const cfopCstReason = rules.getIpiRuleViolationByAnalysis('cfop_cst', auditoria);
        if (cfopCstReason) return cfopCstReason;
    }

    if (rules.isProductTypeInconsistent(
        auditoria.sped_cfop,
        auditoria.tributos?.icms?.cst,
        auditoria.tipo_produto
    )) {
        return rules.getRuleViolationDescription('product_type', auditoria);
    }

    if (rules.isOriginInconsistent(
        auditoria.sped_origem,
        auditoria.xml_origem || auditoria.xml_data?.origem
    )) {
        return rules.getRuleViolationDescription('origin', auditoria);
    }

    // Verificar alíquota baseado no tributo atual (mesma lógica do analyzeViolations)
    const currentTributo = auditoriaComparativaData.currentTributo || 'icms';
    let spedAliquota, xmlAliquota;

    switch (currentTributo) {
        case 'icms':
            spedAliquota = auditoria.tributos?.icms?.aliquota;
            xmlAliquota = auditoria.xml_data?.icms_aliquota || auditoria.xml_icms_aliquota;
            break;
        case 'icms_st':
            spedAliquota = auditoria.tributos?.icms_st?.aliquota;
            xmlAliquota = auditoria.xml_data?.icms_st_aliquota || auditoria.xml_icms_st_aliquota;
            break;
        case 'ipi':
            spedAliquota = auditoria.tributos?.ipi?.aliquota;
            xmlAliquota = auditoria.xml_data?.ipi_aliquota || auditoria.xml_ipi_aliquota;
            break;
        case 'pis':
            spedAliquota = auditoria.tributos?.pis?.aliquota;
            xmlAliquota = auditoria.xml_data?.pis_aliquota || auditoria.xml_pis_aliquota;
            break;
        case 'cofins':
            spedAliquota = auditoria.tributos?.cofins?.aliquota;
            xmlAliquota = auditoria.xml_data?.cofins_aliquota || auditoria.xml_cofins_aliquota;
            break;
        case 'pis_cofins':
            spedAliquota = auditoria.tributos?.pis?.aliquota;
            xmlAliquota = auditoria.xml_data?.pis_aliquota || auditoria.xml_pis_aliquota;
            break;
    }

    if (rules.isAliquotaInconsistent(spedAliquota, xmlAliquota, auditoria.tipo_produto, auditoria.sped_cfop)) {
        return rules.getRuleViolationDescription('aliquota', auditoria);
    }

    if (currentTributo === 'ipi') {
        const aliqReason = rules.getIpiRuleViolationByAnalysis('aliquota', auditoria);
        if (aliqReason) return aliqReason;

        const ipiReason = rules.getIpiRuleViolation(auditoria);
        if (ipiReason) return ipiReason;
    }

    if (['pis', 'cofins', 'pis_cofins'].includes(currentTributo)) {
        if (rules.hasPisCofinsViolation(auditoria)) {
            return rules.getPisCofinsViolations(auditoria).join(' | ');
        }
    }

    return '';
}

/**
 * Cria o HTML do modal de detalhes da regra específica
 */
function createRuleDetailsModalHtml(rule, analysisTitle, violatedItems) {
    const itemsHtml = violatedItems.slice(0, 50).map((item, index) => {
        // Obter alíquotas baseado no tributo atual
        const currentTributo = auditoriaComparativaData.currentTributo || 'icms';
        let spedAliquota = '-', xmlAliquota = '-';

        switch (currentTributo) {
            case 'icms':
                spedAliquota = item.tributos?.icms?.aliquota ? `${item.tributos.icms.aliquota}%` : '-';
                xmlAliquota = item.xml_data?.icms_aliquota ? `${item.xml_data.icms_aliquota}%` :
                             (item.xml_icms_aliquota ? `${item.xml_icms_aliquota}%` : '-');
                break;
            case 'icms_st':
                spedAliquota = item.tributos?.icms_st?.aliquota ? `${item.tributos.icms_st.aliquota}%` : '-';
                xmlAliquota = item.xml_data?.icms_st_aliquota ? `${item.xml_data.icms_st_aliquota}%` :
                             (item.xml_icms_st_aliquota ? `${item.xml_icms_st_aliquota}%` : '-');
                break;
            case 'ipi':
                spedAliquota = item.tributos?.ipi?.aliquota ? `${item.tributos.ipi.aliquota}%` : '-';
                xmlAliquota = item.xml_data?.ipi_aliquota ? `${item.xml_data.ipi_aliquota}%` :
                             (item.xml_ipi_aliquota ? `${item.xml_ipi_aliquota}%` : '-');
                break;
            case 'pis':
                spedAliquota = item.tributos?.pis?.aliquota ? `${item.tributos.pis.aliquota}%` : '-';
                xmlAliquota = item.xml_data?.pis_aliquota ? `${item.xml_data.pis_aliquota}%` :
                             (item.xml_pis_aliquota ? `${item.xml_pis_aliquota}%` : '-');
                break;
            case 'cofins':
                spedAliquota = item.tributos?.cofins?.aliquota ? `${item.tributos.cofins.aliquota}%` : '-';
                xmlAliquota = item.xml_data?.cofins_aliquota ? `${item.xml_data.cofins_aliquota}%` :
                             (item.xml_cofins_aliquota ? `${item.xml_cofins_aliquota}%` : '-');
                break;
            case 'pis_cofins':
                // Para PIS/COFINS, mostrar ambos
                const pisSpedAliq = item.tributos?.pis?.aliquota ? `${item.tributos.pis.aliquota}%` : '-';
                const pisXmlAliq = item.xml_data?.pis_aliquota ? `${item.xml_data.pis_aliquota}%` :
                                  (item.xml_pis_aliquota ? `${item.xml_pis_aliquota}%` : '-');
                const cofinsSpedAliq = item.tributos?.cofins?.aliquota ? `${item.tributos.cofins.aliquota}%` : '-';
                const cofinsXmlAliq = item.xml_data?.cofins_aliquota ? `${item.xml_data.cofins_aliquota}%` :
                                     (item.xml_cofins_aliquota ? `${item.xml_cofins_aliquota}%` : '-');

                spedAliquota = `PIS: ${pisSpedAliq} | COFINS: ${cofinsSpedAliq}`;
                xmlAliquota = `PIS: ${pisXmlAliq} | COFINS: ${cofinsXmlAliq}`;
                break;
        }

        // Obter dados de origem
        const spedOrigem = item.sped_origem || item.sped_icms_origem || '-';
        const xmlOrigem = item.xml_origem || item.xml_data?.origem || item.xml_icms_origem || '-';

        return `
            <tr>
                <td>${index + 1}</td>
                <td><small>${item.numero_nf || item.numero_nota || item.nota_fiscal || '-'}</small></td>
                <td><small>${item.parceiro_razao_social || item.parceiro_nome || item.xml_data?.emitente_nome || '-'}</small></td>
                <td><small>${item.produto_descricao || item.xml_data?.produto_descricao || item.sped_produto_descricao || '-'}</small></td>
                <td><small class="text-danger">${item.sped_cfop || '-'}</small></td>
                <td><small>${item.xml_cfop || '-'}</small></td>
                <td><small class="text-danger">${item.tributos?.icms?.cst || '-'}</small></td>
                <td><small>${item.xml_cst || item.xml_data?.cst || '-'}</small></td>
                <td><small class="text-danger">${spedOrigem}</small></td>
                <td><small>${xmlOrigem}</small></td>
                <td><small class="text-danger">${spedAliquota}</small></td>
                <td><small>${xmlAliquota}</small></td>
            </tr>
        `;
    }).join('');

    return `
        <div class="modal fade" id="ruleDetailsModal" tabindex="-1" aria-labelledby="ruleDetailsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="ruleDetailsModalLabel">
                            <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                            Detalhes da Regra Violada
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <h6 class="mb-2">
                                <i class="fas fa-info-circle me-2"></i>
                                ${analysisTitle}
                            </h6>
                            <p class="mb-1"><strong>Regra violada:</strong> ${rule}</p>
                            <p class="mb-0"><strong>Total de violações:</strong> ${violatedItems.length}</p>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Nota Fiscal</th>
                                        <th>Parceiro</th>
                                        <th>Produto</th>
                                        <th>CFOP SPED</th>
                                        <th>CFOP XML</th>
                                        <th>CST SPED</th>
                                        <th>CST XML</th>
                                        <th>Origem SPED</th>
                                        <th>Origem XML</th>
                                        <th>Alíq. SPED %</th>
                                        <th>Alíq. XML %</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${itemsHtml}
                                </tbody>
                            </table>
                        </div>

                        ${violatedItems.length > 50 ? `
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-info-circle me-2"></i>
                                Mostrando os primeiros 50 itens de ${violatedItems.length} total.
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Fechar
                        </button>
                        <button type="button" class="btn btn-warning" onclick="FiscalAnalysisModals.applyRuleFilter('${rule.replace(/'/g, "\\'")}', '${analysisTitle}')">
                            <i class="fas fa-filter me-1"></i>Filtrar na Tabela
                        </button>
                        <button type="button" class="btn btn-primary" onclick="exportRuleViolationReport('${rule.replace(/'/g, "\\'")}', ${violatedItems.length})">
                            <i class="fas fa-download me-1"></i>Exportar Lista
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Exporta relatório específico de uma regra
 */
function exportRuleViolationReport(rule, totalItems) {
    console.log('Exportar relatório da regra:', rule, 'Total:', totalItems);
    showMessage('Funcionalidade de exportação será implementada em breve', 'info');
}

/**
 * Exporta relatório de violações
 */
function exportViolationReport(analysisTitle) {
    // Implementar exportação de relatório
    console.log('Exportar relatório para:', analysisTitle);
    showMessage('Funcionalidade de exportação será implementada em breve', 'info');
}

/**
 * Aplica filtro na tabela exibindo apenas os registros que violaram a regra informada
 */
function applyRuleFilter(rule, analysisTitle) {
    if (!auditoriaComparativaTable) return;

    const auditorias = auditoriaComparativaData.auditorias || [];
    const itemsToShow = [];

    auditorias.forEach(auditoria => {
        const violation = getRuleViolationForItem(auditoria);
        if (violation === rule) {
            itemsToShow.push(auditoria.id);
        }
    });

    if (itemsToShow.length === 0) {
        showMessage('Nenhum registro encontrado para esta regra', 'info');
        return;
    }

    auditoriaComparativaTable.rows().every(function() {
        const row = this.node();
        const auditoriaId = parseInt(row.getAttribute('data-auditoria-id'));

        if (itemsToShow.includes(auditoriaId)) {
            $(row).show();
        } else {
            $(row).hide();
        }
    });

    const filtroInfo = document.getElementById('filtros-ativos-info');
    if (filtroInfo) {
        filtroInfo.innerHTML = `<span class="badge bg-warning">${analysisTitle}: ${itemsToShow.length} registros</span><br><small>${rule}</small>`;
    }

    const ruleModalEl = document.getElementById('ruleDetailsModal');
    if (ruleModalEl) {
        const modalInstance = bootstrap.Modal.getInstance(ruleModalEl);
        if (modalInstance) modalInstance.hide();
    }
    const analysisModalEl = document.getElementById('analysisDetailsModal');
    if (analysisModalEl) {
        const modalInstance = bootstrap.Modal.getInstance(analysisModalEl);
        if (modalInstance) modalInstance.hide();
    }

    showMessage(`Filtro aplicado: ${analysisTitle} - ${itemsToShow.length} registros`, 'info');
}

/**
 * Adiciona botão de detalhes aos cards de análise
 */
function addDetailsButtonToAnalysisCard(cardElement, analysisType) {
    // Verificar se já existe botão
    if (cardElement.querySelector('.details-btn')) return;

    // Criar botão de detalhes
    const detailsButton = document.createElement('button');
    detailsButton.className = 'btn details-btn';
    detailsButton.innerHTML = '<i class="fas fa-info-circle"></i>';
    detailsButton.title = 'Ver detalhes da análise';
    detailsButton.onclick = function(e) {
        e.stopPropagation(); // Evitar trigger do filtro
        const auditorias = auditoriaComparativaData.auditorias || [];
        showAnalysisDetailsModal(analysisType, auditorias);
    };

    // Adicionar botão no canto superior direito do card
    cardElement.appendChild(detailsButton);
}

// Exportar para uso global
if (typeof window !== 'undefined') {
    window.FiscalAnalysisModals = {
        showAnalysisDetailsModal,
        analyzeViolations,
        addDetailsButtonToAnalysisCard,
        showRuleViolationDetails,
        exportViolationReport,
        getRuleViolationForItem,
        createRuleDetailsModalHtml,
        exportRuleViolationReport,
        applyRuleFilter
    };
}
