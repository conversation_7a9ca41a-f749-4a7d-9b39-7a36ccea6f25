/**
 * Common-UI.js - Auditoria Fiscal
 * Funções comuns para interface de usuário
 */

/**
 * Força o fechamento de todos os dropdowns quando a sidebar estiver colapsada
 */
function ensureDropdownsClosedWhenCollapsed() {
  const container = document.querySelector('.dashboard-container');
  const dropdowns = document.querySelectorAll('.nav-dropdown');

  if (container && container.classList.contains('sidebar-collapsed')) {
    dropdowns.forEach((dropdown) => {
      dropdown.classList.remove('open');
    });
  }
}

/**
 * Configura os links do dropdown do perfil do usuário
 */
function setupProfileDropdownLinks() {
  const empresasLink = document.getElementById('menu-empresas');
  const usuariosLink = document.getElementById('menu-usuarios');
  const escritoriosLink = document.getElementById('menu-escritorios');

  // Função auxiliar para clonar e substituir um elemento para remover event listeners
  function resetElement(element) {
    if (!element) return null;
    const newElement = element.cloneNode(true);
    element.parentNode.replaceChild(newElement, element);
    return newElement;
  }

  // Resetar e configurar link de empresas
  if (empresasLink) {
    const newEmpresasLink = resetElement(empresasLink);
    newEmpresasLink.addEventListener('click', function (e) {
      e.preventDefault();
      window.location.href = '/empresas';
    });
  }

  // Resetar e configurar link de usuários
  if (usuariosLink) {
    const newUsuariosLink = resetElement(usuariosLink);
    newUsuariosLink.addEventListener('click', function (e) {
      e.preventDefault();
      window.location.href = '/usuarios';
    });
  }

  // Resetar e configurar link de escritórios
  if (escritoriosLink) {
    const newEscritoriosLink = resetElement(escritoriosLink);

    // Mostrar o link de escritórios apenas para usuários admin
    if (
      currentUser &&
      (currentUser.is_admin || currentUser.tipo_usuario === 'admin')
    ) {
      newEscritoriosLink.style.display = 'block';

      newEscritoriosLink.addEventListener('click', function (e) {
        e.preventDefault();
        window.location.href = '/escritorios';
      });
    } else {
      newEscritoriosLink.style.display = 'none';
    }
  }
}

/**
 * Configura o toggle da sidebar
 */
function setupSidebarToggle() {
  const toggleBtn = document.getElementById('toggle-sidebar');
  if (!toggleBtn) return;

  // Remover event listeners anteriores (se possível)
  const newToggleBtn = toggleBtn.cloneNode(true);
  toggleBtn.parentNode.replaceChild(newToggleBtn, toggleBtn);

  const container = document.querySelector('.dashboard-container');
  const dropdowns = document.querySelectorAll('.nav-dropdown');

  // Armazenar referências às funções de handler para poder removê-las depois
  const mouseEnterHandlers = new Map();
  const mouseLeaveHandlers = new Map();

  // Função para adicionar event listeners de hover
  function addHoverListeners() {
    dropdowns.forEach((dropdown, index) => {
      const dropdownMenu = dropdown.querySelector('.nav-dropdown-menu');
      const dropdownToggle = dropdown.querySelector('.nav-link');

      // Criar funções de handler e armazená-las para remoção posterior
      const mouseEnterHandler = function () {
        if (container.classList.contains('sidebar-collapsed')) {
          // Posicionar o menu dropdown ao lado do item
          const rect = dropdownToggle.getBoundingClientRect();
          dropdownMenu.style.top = rect.top + 'px';
          dropdown.classList.add('open');
        }
      };

      const mouseLeaveHandler = function (event) {
        if (container.classList.contains('sidebar-collapsed')) {
          // Verificar se o mouse está saindo para o dropdown
          const relatedTarget = event.relatedTarget;
          if (!dropdown.contains(relatedTarget)) {
            // Adicionar um pequeno delay para permitir que o usuário mova o mouse para o dropdown
            setTimeout(() => {
              if (!dropdown.matches(':hover')) {
                dropdown.classList.remove('open');
              }
            }, 100);
          }
        }
      };

      // Armazenar referências
      mouseEnterHandlers.set(dropdown, mouseEnterHandler);
      mouseLeaveHandlers.set(dropdown, mouseLeaveHandler);

      // Adicionar event listeners
      dropdown.addEventListener('mouseenter', mouseEnterHandler);
      dropdown.addEventListener('mouseleave', mouseLeaveHandler);
    });
  }

  // Função para remover event listeners de hover
  function removeHoverListeners() {
    dropdowns.forEach((dropdown) => {
      const mouseEnterHandler = mouseEnterHandlers.get(dropdown);
      const mouseLeaveHandler = mouseLeaveHandlers.get(dropdown);

      if (mouseEnterHandler) {
        dropdown.removeEventListener('mouseenter', mouseEnterHandler);
      }

      if (mouseLeaveHandler) {
        dropdown.removeEventListener('mouseleave', mouseLeaveHandler);
      }
    });

    // Limpar os maps
    mouseEnterHandlers.clear();
    mouseLeaveHandlers.clear();
  }

  // Configurar estado inicial com base no tamanho da tela e no localStorage
  const storedSidebarState = localStorage.getItem('sidebarCollapsed');
  const isMobile = window.innerWidth <= 992;

  if (!isMobile) {
    if (storedSidebarState === 'true') {
      container.classList.add('sidebar-collapsed');
      dropdowns.forEach((dropdown) => {
        dropdown.classList.remove('open');
      });
      addHoverListeners();
    } else if (storedSidebarState === 'false') {
      container.classList.remove('sidebar-collapsed');
      removeHoverListeners();
    } else if (container.classList.contains('sidebar-collapsed')) {
      dropdowns.forEach((dropdown) => {
        dropdown.classList.remove('open');
      });
      addHoverListeners();
    }
  }

  newToggleBtn.addEventListener('click', function () {
    // Fechar todos os dropdowns antes de alterar o estado da sidebar
    dropdowns.forEach((dropdown) => {
      dropdown.classList.remove('open');
    });

    const isMobile = window.innerWidth <= 992;

    if (isMobile) {
      // Em telas menores apenas mostramos/ocultamos a sidebar sobre o conteúdo
      container.classList.toggle('sidebar-visible');
    } else {
      // Toggle da classe sidebar-collapsed
      container.classList.toggle('sidebar-collapsed');

      // Salvar estado no localStorage somente para telas grandes
      const isCollapsed = container.classList.contains('sidebar-collapsed');
      localStorage.setItem('sidebarCollapsed', isCollapsed);

      // Adicionar ou remover event listeners com base no estado da sidebar
      if (isCollapsed) {
        addHoverListeners();
      } else {
        removeHoverListeners();
      }
    }

    // Disparar evento personalizado para notificar outros componentes
    const event = new CustomEvent('sidebar-toggle', {
      detail: { collapsed: container.classList.contains('sidebar-collapsed') },
    });
    document.dispatchEvent(event);
    console.log('Evento sidebar-toggle disparado');
  });
}

/**
 * Configura o seletor de empresa
 */
function setupCompanySelector() {
  const dropdown = document.querySelector('.company-selector');
  const companySelect = document.getElementById('company-select');
  if (!companySelect) return;

  // Remover event listeners anteriores (se possível)
  const newCompanySelect = companySelect.cloneNode(true);
  companySelect.parentNode.replaceChild(newCompanySelect, companySelect);

  // Recuperar empresa selecionada do localStorage
  const storedCompany = localStorage.getItem('selectedCompany');
  if (storedCompany) {
    selectedCompany = parseInt(storedCompany);
    newCompanySelect.value = selectedCompany;
  }

  // Configurar evento de mudança
  newCompanySelect.addEventListener('change', function () {
    const empresaId = parseInt(this.value);
    selectedCompany = empresaId;
    localStorage.setItem('selectedCompany', empresaId);

    // Recarregar a página atual para refletir a mudança
    window.location.reload();
  });
  
  // Se estiver usando o componente de dropdown customizado, reinicializar
  if (
    dropdown &&
    dropdown.classList.contains('custom-dropdown') &&
    window.initCustomDropdown
  ) {
    dropdown
      .querySelectorAll('.custom-dropdown-toggle, .custom-dropdown-menu')
      .forEach((el) => el.remove());
    window.initCustomDropdown(dropdown);
  }
}

/**
 * Configura o seletor de ano
 */
function setupYearSelector() {
  const dropdown = document.querySelector('.year-selector');
  const yearSelect = document.getElementById('year-select');
  if (!yearSelect) return;

  // Remover event listeners anteriores (se possível)
  const newYearSelect = yearSelect.cloneNode(false); // false para não clonar as opções
  yearSelect.parentNode.replaceChild(newYearSelect, yearSelect);

  // Preencher opções de ano (atual e 5 anos anteriores)
  const currentYear = new Date().getFullYear();
  for (let year = currentYear; year >= currentYear - 5; year--) {
    const option = document.createElement('option');
    option.value = year;
    option.textContent = year;
    newYearSelect.appendChild(option);
  }

  // Recuperar ano selecionado do localStorage
  const storedYear = localStorage.getItem('selectedYear');
  if (storedYear) {
    newYearSelect.value = storedYear;
    selectedYear = parseInt(storedYear);
  } else {
    newYearSelect.value = currentYear;
    selectedYear = currentYear;
  }

  // Configurar evento de mudança
  newYearSelect.addEventListener('change', function () {
    selectedYear = parseInt(this.value);
    localStorage.setItem('selectedYear', selectedYear);

    // Recarregar a página atual para refletir a mudança
    window.location.reload();
  });
  
  // Reinicializar o dropdown customizado se necessário
  if (
    dropdown &&
    dropdown.classList.contains('custom-dropdown') &&
    window.initCustomDropdown
  ) {
    dropdown
      .querySelectorAll('.custom-dropdown-toggle, .custom-dropdown-menu')
      .forEach((el) => el.remove());
    window.initCustomDropdown(dropdown);
  }
}

/**
 * Configura o seletor de mês
 */
function setupMonthSelector() {
  const dropdown = document.querySelector('.month-selector');
  const monthSelect = document.getElementById('month-select');
  if (!monthSelect) return;

  // Remover event listeners anteriores (se possível)
  const newMonthSelect = monthSelect.cloneNode(true); // true para clonar as opções
  monthSelect.parentNode.replaceChild(newMonthSelect, monthSelect);

  // Recuperar mês selecionado do localStorage
  const storedMonth = localStorage.getItem('selectedMonth');
  if (storedMonth) {
    newMonthSelect.value = storedMonth;
    selectedMonth = parseInt(storedMonth);
  } else {
    // Usar o mês atual como padrão
    const currentMonth = new Date().getMonth() + 1; // getMonth() retorna 0-11
    newMonthSelect.value = currentMonth;
    selectedMonth = currentMonth;
    localStorage.setItem('selectedMonth', currentMonth);
  }

  // Configurar evento de mudança
  newMonthSelect.addEventListener('change', function () {
    selectedMonth = parseInt(this.value);
    localStorage.setItem('selectedMonth', selectedMonth);

    // Recarregar a página atual para refletir a mudança
    window.location.reload();
  });
  
  // Reinicializar o dropdown customizado APÓS definir o valor
  if (
    dropdown &&
    dropdown.classList.contains('custom-dropdown') &&
    window.initCustomDropdown
  ) {
    dropdown
      .querySelectorAll('.custom-dropdown-toggle, .custom-dropdown-menu')
      .forEach((el) => el.remove());
    window.initCustomDropdown(dropdown);
  }
}

/**
 * Carrega todas as empresas para o seletor
 */
function loadAllCompanies() {
  console.log('Loading all companies for admin user');

  console.log('[loadAllCompanies] Antes de chamar fetch para /fiscal/api/empresas');
  fetch('/fiscal/api/empresas', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => {
      console.log('Companies API response status:', response.status);
      console.log('[loadAllCompanies] Resposta completa da API:', response);
      if (!response.ok) {
        console.error(
          '[loadAllCompanies] Resposta da API não foi OK:',
          response.status,
          response.statusText,
        );
        response
          .text()
          .then((text) =>
            console.error(
              '[loadAllCompanies] Corpo da resposta de erro:',
              text,
            ),
          );
      }
      return response.json();
    })
    .then((data) => {
      console.log('Companies data received:', data);
      if (data.empresas && data.empresas.length > 0) {
        console.log(`Found ${data.empresas.length} companies`);
        populateCompanySelector(data.empresas);
      } else {
        console.warn('No companies found in response');
        // Adicionar um log para o caso de não haver empresas, mas a requisição ter sido bem-sucedida
        if (data && data.empresas) {
          console.log(
            '[loadAllCompanies] A API retornou uma lista vazia de empresas.',
          );
        } else {
          console.warn(
            '[loadAllCompanies] A estrutura de dados retornada pela API não contém "empresas":',
            data,
          );
        }
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar empresas:', error);
    });
}

/**
 * Carrega empresas do escritório para o seletor
 * @param {number} escritorioId - ID do escritório
 */
function loadEscritorioCompanies(escritorioId) {
  console.log(`Loading companies for escritorio ID: ${escritorioId}`);

  fetch('/fiscal/api/empresas', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => {
      console.log('Escritorio companies API response status:', response.status);
      return response.json();
    })
    .then((data) => {
      console.log('Escritorio companies data received:', data);
      // Filtrar empresas do escritório (já deve vir filtrado do backend)
      if (data.empresas && data.empresas.length > 0) {
        console.log(
          `Found ${data.empresas.length} companies for escritorio ${escritorioId}`,
        );
        populateCompanySelector(data.empresas);
      } else {
        console.warn(`No companies found for escritorio ${escritorioId}`);
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar empresas do escritório:', error);
    });
}

/**
 * Carrega empresas permitidas para o usuário
 * @param {Array} empresasIds - IDs das empresas permitidas
 */
function loadUserCompanies(empresasIds) {
  console.log(
    `Loading companies for user with permitted IDs: ${JSON.stringify(
      empresasIds,
    )}`,
  );

  fetch('/fiscal/api/empresas', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => {
      console.log('User companies API response status:', response.status);
      return response.json();
    })
    .then((data) => {
      console.log('User companies data received:', data);
      // Filtrar empresas permitidas (já deve vir filtrado do backend)
      if (data.empresas && data.empresas.length > 0) {
        console.log(`Found ${data.empresas.length} companies for user`);
        populateCompanySelector(data.empresas);
      } else {
        console.warn('No companies found for user');
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar empresas do usuário:', error);
    });
}

/**
 * Preenche o seletor de empresas
 * @param {Array} empresas - Lista de empresas
 */
function populateCompanySelector(empresas) {
  console.log('Populating company selector with', empresas.length, 'companies');

  const companySelect = document.getElementById('company-select');
  if (!companySelect) {
    console.error('Company select element not found in the DOM');
    return;
  }

  // Limpar opções existentes, exceto a primeira (placeholder)
  while (companySelect.options.length > 1) {
    companySelect.remove(1);
  }

  // Adicionar opções de empresas
  empresas.forEach((empresa) => {
    const option = document.createElement('option');
    option.value = empresa.id;
    // Usar razao_social se nome não estiver disponível
    option.textContent = empresa.nome_fantasia || empresa.razao_social;
    companySelect.appendChild(option);
    console.log(
      `Added company option: ${option.textContent} (ID: ${option.value})`,
    );
  });

  // Selecionar empresa salva no localStorage
  const storedCompany = localStorage.getItem('selectedCompany');
  console.log('Stored company ID from localStorage:', storedCompany);

  if (storedCompany) {
    // Verificar se a empresa armazenada existe nas opções
    const exists = Array.from(companySelect.options).some(
      (opt) => opt.value === storedCompany,
    );
    if (exists) {
      companySelect.value = storedCompany;
      console.log(`Selected stored company ID: ${storedCompany}`);
    } else {
      console.warn(`Stored company ID ${storedCompany} not found in options`);
      if (empresas.length > 0) {
        companySelect.value = empresas[0].id;
        localStorage.setItem('selectedCompany', empresas[0].id);
        console.log(`Selected first company instead: ${empresas[0].id}`);
      }
    }
  } else if (empresas.length > 0) {
    // Se não houver empresa salva, selecionar a primeira
    companySelect.value = empresas[0].id;
    localStorage.setItem('selectedCompany', empresas[0].id);
    console.log(`No stored company, selected first company: ${empresas[0].id}`);
  }

  // Atualizar a variável global
  selectedCompany = parseInt(companySelect.value);
  console.log(`Updated global selectedCompany to: ${selectedCompany}`);
}

/**
 * Configura o botão de logout
 */
function setupLogoutButton() {
  const logoutBtn = document.getElementById('logout-btn');
  if (!logoutBtn) return;

  logoutBtn.addEventListener('click', function (e) {
    e.preventDefault();
    performLogout();
  });
}

/**
 * Realiza o logout do usuário
 */
function performLogout() {
  // Remover token e dados salvos
  localStorage.removeItem('token');
  localStorage.removeItem('selectedCompany');
  localStorage.removeItem('currentUser');

  // Remover cookie de autenticação do portal
  document.cookie = 'token=; Max-Age=0; path=/';

  // Redirecionar para a página de login
  window.location.href = '/web';
}
