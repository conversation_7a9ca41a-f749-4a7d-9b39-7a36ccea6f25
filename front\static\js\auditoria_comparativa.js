/**
 * Auditoria Comparativa de Impostos
 * Sistema de matching inteligente entre XML e SPED integrado à página existente
 */

// Estado global da aplicação
let auditoriaComparativaData = {
    chaveNF: null,
    auditorias: [],
    matchingDetails: null,
    currentTributo: 'icms',
    filtrosAtivos: {},
    itensSelecionados: new Set(),
    pageLength: 200
};

// WebSocket para auditoria
let auditoriaSocket = null;

// Instância DataTable da tabela de auditoria comparativa
let auditoriaComparativaTable = null;

// Configurações
const TRIBUTOS_CONFIG = {
    'icms': { label: 'ICMS', color: 'primary' },
    'icms_st': { label: 'ICMS-ST', color: 'warning' },
    'ipi': { label: 'IPI', color: 'info' },
    'pis': { label: 'PIS', color: 'success' },
    'cofins': { label: 'COFINS', color: 'secondary' }
};

/**
 * Inicializa funcionalidades da auditoria comparativa na página existente
 */
function initAuditoriaComparativaIntegrada() {
    setupAuditoriaComparativaEventListeners();
    setupTributoSwitching();
}

/**
 * Configura event listeners para auditoria comparativa
 */
function setupAuditoriaComparativaEventListeners() {
    // Botão para buscar nota fiscal
    const btnBuscar = document.getElementById('btn-buscar-nota');
    if (btnBuscar) {
        btnBuscar.addEventListener('click', buscarNotaFiscal);
    }

    // Campo de busca por chave NF
    const inputChaveNF = document.getElementById('input-chave-nf');
    if (inputChaveNF) {
        inputChaveNF.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                buscarNotaFiscal();
            }
        });
    }

    // Botão único de geração de auditoria
    const btnGerarAuditoria = document.getElementById('btn-gerar-auditoria-tributo');
    if (btnGerarAuditoria) {
        btnGerarAuditoria.addEventListener('click', () => gerarAuditoriaComparativa());
    }
}

/**
 * Configura switching entre tributos
 */
function setupTributoSwitching() {
    const tributoButtons = document.querySelectorAll('#tributos-nav button[data-tributo]');

    tributoButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tributo = this.getAttribute('data-tributo');

            // Controlar visibilidade dos elementos baseado no tributo
            toggleAuditoriaComparativaElements(tributo);

            // Não mexer na escrituração
            if (tributo === 'escrituracao') {
                return;
            }

            // Atualizar tributo atual
            auditoriaComparativaData.currentTributo = tributo;

            // Mostrar loading imediato ao trocar de tab
            showLoading('Carregando...');

            // NOVA LÓGICA: Carregar dados do tributo automaticamente baseado no período
            carregarAuditoriaComparativaTributo(tributo);
        });
    });

    // Configurar visibilidade inicial
    const activeButton = document.querySelector('#tributos-nav button.active');
    if (activeButton) {
        const tributo = activeButton.getAttribute('data-tributo');
        toggleAuditoriaComparativaElements(tributo);

        // NOVA LÓGICA: Carregar dados automaticamente se não for escrituração
        if (tributo !== 'escrituracao') {
            auditoriaComparativaData.currentTributo = tributo;
            showLoading('Carregando...');
            carregarAuditoriaComparativaTributo(tributo);
        }
    }
}

/**
 * Controla visibilidade dos elementos de auditoria comparativa
 */
function toggleAuditoriaComparativaElements(tributo) {
    // Buscar o card que contém o input de chave NF
    const buscaNotaCard = document.querySelector('#input-chave-nf')?.closest('.card');
    const btnGerarAuditoria = document.getElementById('btn-gerar-auditoria-tributo');
    const btnAprovar = document.getElementById('btn-aprovar-auditoria');

    if (tributo === 'escrituracao') {
        // Esconder elementos na aba de escrituração
        if (buscaNotaCard) buscaNotaCard.style.display = 'none';
        if (btnGerarAuditoria) btnGerarAuditoria.style.display = 'none';
        if (btnAprovar) btnAprovar.textContent = 'Aprovar Selecionados'; // Texto original
    } else {
        // NOVA LÓGICA: Esconder elementos de busca por chave NF nas tabs de tributos
        // Agora as tabs de tributos carregam automaticamente baseado no período
        if (buscaNotaCard) buscaNotaCard.style.display = 'none';
        if (btnGerarAuditoria) {
            btnGerarAuditoria.style.display = 'inline-block';
            btnGerarAuditoria.textContent = 'Gerar Auditoria';
        }
        if (btnAprovar) btnAprovar.textContent = 'Aprovar Matches'; // Texto para auditoria comparativa
    }
}

/**
 * Busca nota fiscal para auditoria
 */
function buscarNotaFiscal() {
    const chaveNF = document.getElementById('input-chave-nf').value.trim();

    if (!chaveNF) {
        showMessage('Por favor, informe a chave da nota fiscal', 'warning');
        return;
    }

    if (chaveNF.length !== 44) {
        showMessage('Chave da nota fiscal deve ter 44 caracteres', 'warning');
        return;
    }

    auditoriaComparativaData.chaveNF = chaveNF;

    // Primeiro, tentar buscar auditoria existente
    buscarAuditoriaComparativaExistente(chaveNF);
}

/**
 * Busca auditoria comparativa existente
 */
function buscarAuditoriaComparativaExistente(chaveNF) {
    const empresaId = localStorage.getItem('selectedCompany');

    if (!empresaId) {
        showMessage('Selecione uma empresa primeiro', 'warning');
        return;
    }

    showLoading('Buscando auditoria comparativa...');

    fetch(`/api/auditoria-comparativa/nota/${chaveNF}?empresa_id=${empresaId}`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            auditoriaComparativaData.auditorias = data.auditorias;
            renderAuditoriaComparativaTributo(auditoriaComparativaData.currentTributo);
            showMessage('Auditoria comparativa carregada com sucesso', 'success');
        } else {
            // Se não encontrou, mostrar mensagem
            showMessage('Auditoria comparativa não encontrada. Use os botões para gerar.', 'info');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao buscar auditoria:', error);
        showMessage('Erro ao buscar auditoria comparativa', 'error');
    });
}

/**
 * Gera auditoria comparativa para todos os tributos (função unificada)
 */
function gerarAuditoriaComparativaTodos() {
    // Verificar qual aba está ativa
    const activeTab = document.querySelector('#tributos-nav button.active');
    const tributoAtivo = activeTab ? activeTab.getAttribute('data-tributo') : null;

    if (tributoAtivo === 'escrituracao') {
        showMessage('Esta função não está disponível na aba de Escrituração', 'warning');
        return;
    }

    // NOVA LÓGICA: Usar período ao invés de chave NF
    const empresaId = localStorage.getItem('selectedCompany');
    const ano = document.getElementById('year-select')?.value || localStorage.getItem('selectedYear');
    const mes = document.getElementById('month-select')?.value || localStorage.getItem('selectedMonth');

    if (!empresaId || !ano || !mes) {
        showMessage('Selecione empresa, ano e mês para gerar auditoria comparativa', 'warning');
        return;
    }

    // Gerar auditoria para todos os tributos
    const tributos = ['icms', 'icms_st', 'ipi', 'pis', 'cofins'];
    gerarAuditoriaComparativaPeriodoComWebSocket(parseInt(mes), parseInt(ano), tributos);
}

/**
 * Gera auditoria comparativa por período com WebSocket (NOVA FUNÇÃO)
 */
function gerarAuditoriaComparativaPeriodoComWebSocket(mes, ano, tributos = null) {
    const empresaId = localStorage.getItem('selectedCompany');

    if (!empresaId || !mes || !ano) {
        showMessage('Empresa, mês e ano são obrigatórios', 'warning');
        return;
    }

    // Configurar WebSocket se não estiver configurado
    if (!window.auditoriaSocket) {
        setupAuditoriaWebSocket();
    }

    const tributosText = tributos ? tributos.join(', ').toUpperCase() : 'todos os tributos';

    // Mostrar barra de progresso inicial
    showAuditoriaProgressBar(`Iniciando auditoria comparativa para ${tributosText}...`, 0);

    fetch('/api/auditoria-comparativa/gerar-periodo', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            empresa_id: parseInt(empresaId),
            mes: mes,
            ano: ano,
            tributos_filter: tributos,
            force_recalculate: true
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Conectar à sala da auditoria
            if (window.auditoriaSocket && data.audit_id) {
                window.auditoriaSocket.emit('join_audit', {
                    token: localStorage.getItem('token'),
                    audit_id: data.audit_id
                });

                // Armazenar ID da auditoria para reconexão
                window.currentAuditId = data.audit_id;
            }

            showMessage(`Auditoria iniciada com sucesso! Acompanhe o progresso abaixo.`, 'info');
        } else {
            hideAuditoriaProgressBar();
            showMessage(data.message || 'Erro ao iniciar auditoria comparativa', 'error');
        }
    })
    .catch(error => {
        hideAuditoriaProgressBar();
        console.error('Erro ao iniciar auditoria:', error);
        showMessage('Erro ao iniciar auditoria comparativa', 'error');
    });
}

/**
 * Gera auditoria comparativa (FUNÇÃO LEGADA - mantida para compatibilidade)
 */
function gerarAuditoriaComparativa(chaveNF = null, tributos = null) {
    // Se não foi passada chave NF, usar a nova lógica de período
    if (!chaveNF) {
        return gerarAuditoriaComparativaTodos();
    }

    const empresaId = localStorage.getItem('selectedCompany');

    if (!chaveNF || !empresaId) {
        showMessage('Chave NF e empresa são obrigatórios', 'warning');
        return;
    }

    const tributosText = tributos ? tributos.join(', ').toUpperCase() : 'todos os tributos';
    showLoading(`Gerando auditoria comparativa para ${tributosText}...`);

    fetch('/api/auditoria-comparativa/gerar', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            chave_nf: chaveNF,
            empresa_id: parseInt(empresaId),
            tributos_filter: tributos,
            force_recalculate: true
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            auditoriaComparativaData.auditorias = data.auditorias;
            auditoriaComparativaData.chaveNF = chaveNF;

            // Renderizar o tributo atual
            renderAuditoriaComparativaTributo(auditoriaComparativaData.currentTributo);

            showMessage(`Auditoria comparativa gerada com sucesso! ${data.total_registros} registros processados.`, 'success');

            // Mostrar estatísticas do matching
            if (data.matching_stats) {
                showMatchingStatsModal(data.matching_stats);
            }
        } else {
            showMessage(data.message || 'Erro ao gerar auditoria comparativa', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao gerar auditoria:', error);
        showMessage('Erro ao gerar auditoria comparativa', 'error');
    });
}

/**
 * Carrega auditoria comparativa para um tributo específico baseado no período
 */
function carregarAuditoriaComparativaTributo(tributo) {
    const empresaId = localStorage.getItem('selectedCompany');
    const ano = document.getElementById('year-select')?.value || localStorage.getItem('selectedYear');
    const mes = document.getElementById('month-select')?.value || localStorage.getItem('selectedMonth');

    if (!empresaId || !ano || !mes) {
        const container = document.getElementById('auditoria-content');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Selecione empresa, ano e mês para visualizar a auditoria comparativa.
                </div>
            `;
        }
        return;
    }

    showLoading(`Carregando auditoria comparativa de ${TRIBUTOS_CONFIG[tributo]?.label || tributo.toUpperCase()}...`);

    // Mapear tributo para API (pis_cofins vira pis)
    let tributoApi = tributo;
    if (tributo === 'pis_cofins') {
        tributoApi = 'pis'; // A API vai retornar tanto PIS quanto COFINS
    }

    fetch(`/api/auditoria-comparativa/periodo?empresa_id=${empresaId}&mes=${mes}&ano=${ano}&tributo=${tributoApi}`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            auditoriaComparativaData.auditorias = data.auditorias;
            auditoriaComparativaData.currentTributo = tributo;

            // Renderizar o tributo
            renderAuditoriaComparativaTributo(tributo);
        } else {
            const container = document.getElementById('auditoria-content');
            if (container) {
                container.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        ${data.message || 'Nenhuma auditoria comparativa encontrada para este período.'}
                        <br><br>
                        <button class="btn btn-primary" onclick="gerarAuditoriaComparativaTributo()">
                            <i class="fas fa-play me-2"></i>Gerar Auditoria ${TRIBUTOS_CONFIG[tributo]?.label || tributo.toUpperCase()}
                        </button>
                    </div>
                `;
            }
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao carregar auditoria:', error);
        showMessage('Erro ao carregar auditoria comparativa', 'error');

        const container = document.getElementById('auditoria-content');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Erro ao carregar dados. Tente novamente.
                </div>
            `;
        }
    });
}

/**
 * Renderiza auditoria comparativa para um tributo específico
 */
function renderAuditoriaComparativaTributo(tributo) {
    const container = document.getElementById('auditoria-content');

    if (!container || !auditoriaComparativaData.auditorias.length) {
        if (container) {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Nenhuma auditoria comparativa encontrada. Use os botões acima para gerar.
                </div>
            `;
        }
        return;
    }

    // Filtrar dados do tributo (exceto escrituração)
    if (tributo === 'escrituracao') {
        return; // Não mexer na escrituração
    }

    // Mostrar TODOS os registros (sem filtrar por valor do tributo)
    let auditoriasFiltradas = auditoriaComparativaData.auditorias;

    if (auditoriasFiltradas.length === 0) {
        container.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Nenhum item encontrado para o tributo ${TRIBUTOS_CONFIG[tributo]?.label || tributo.toUpperCase()}
            </div>
        `;
        return;
    }

    // Armazenar tributo atual para uso nos modais
    auditoriaComparativaData.currentTributo = tributo;

    // Renderizar cards de análise fiscal
    let analysisCardsHtml = renderAnalysisCards(auditoriasFiltradas, tributo);

    // Renderizar tabela
    let html = analysisCardsHtml + `
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        Auditoria Comparativa - ${TRIBUTOS_CONFIG[tributo]?.label || tributo.toUpperCase()}
                    </h6>
                    <div>
                        <span class="badge bg-${TRIBUTOS_CONFIG[tributo]?.color || 'secondary'} me-2">
                            ${auditoriasFiltradas.length} produtos
                        </span>
                        <small class="text-muted">Período: ${getCurrentPeriodText()}</small>
                    </div>
                </div>
                <div class="d-flex gap-2 flex-wrap align-items-center">
                    <button class="btn btn-sm btn-outline-secondary" onclick="limparTodosFiltrosDropdownAuditoriaComparativa()">
                        <i class="fas fa-times me-1"></i>Limpar Todos os Filtros
                    </button>

                    <button class="btn btn-sm btn-success" onclick="aprovarSelecionados()" id="btn-aprovar-selecionados" disabled>
                        <i class="fas fa-check me-1"></i>Aprovar Selecionados (<span id="count-selecionados">0</span>)
                    </button>

                    <button class="btn btn-sm btn-warning" onclick="editarSpedMassa()" id="btn-editar-sped-massa" disabled>
                        <i class="fas fa-edit me-1"></i>Editar SPED em Massa
                    </button>

                    <small class="text-muted ms-2">
                        <span id="filtros-ativos-info"></span>
                    </small>
                    
                    <div class="d-flex align-items-center ms-auto">
                        <label for="records-per-page-select" class="me-1 small mb-0">Registros</label>
                        <select id="records-per-page-select" class="form-select form-select-sm">
                            <option value="200">200</option>
                            <option value="500">500</option>
                            <option value="1000">1000</option>
                            <option value="2000">2000</option>
                            <option value="3000">3000</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-sm" id="auditoria-comparativa-table">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 40px;">
                                    <input type="checkbox" id="select-all-matches" onchange="toggleSelectAllMatches(this)">
                                </th>
                                <th style="width: 80px;">Nota Fiscal</th>
                                <th style="width: 90px;">Emissão</th>
                                <th style="width: 150px;">Nome/Razão Social</th>
                                <th style="width: 60px;">UF</th>
                                <th style="width: 100px;">Regime Parceiro</th>
                                <th style="width: 120px;">PRODUTO CÓDIGO SPED</th>
                                <th style="width: 120px;">PRODUTO CÓDIGO NOTA</th>
                                <th style="width: 200px;">Descrição</th>
                                <th style="width: 80px;">NCM NFe</th>
                                <th style="width: 80px;">NCM SPED</th>
                                <th style="width: 80px;">Tipo</th>
                                <th style="width: 150px;">Descrição Tipo</th>
                                <th style="width: 100px;">Produto Nota</th>
                                <th style="width: 80px;">Origem SPED</th>
                                <th style="width: 80px;">Origem Nota</th>
                                <th style="width: 80px;">CFOP SPED</th>
                                <th style="width: 80px;">CFOP Nota</th>
                                ${tributo === 'pis_cofins' ? `
                                    <th style="width: 80px;">CST PIS SPED</th>
                                    <th style="width: 80px;">CST COFINS SPED</th>
                                    <th style="width: 80px;">CST PIS Nota</th>
                                    <th style="width: 80px;">CST COFINS Nota</th>
                                    <th style="width: 80px;">CSOSN Nota</th>
                                    <th style="width: 100px;">Base PIS SPED R$</th>
                                    <th style="width: 100px;">Base COFINS SPED R$</th>
                                    <th style="width: 100px;">Base PIS Nota R$</th>
                                    <th style="width: 100px;">Base COFINS Nota R$</th>
                                    <th style="width: 100px;">Alíq PIS SPED %</th>
                                    <th style="width: 100px;">Alíq COFINS SPED %</th>
                                    <th style="width: 100px;">Alíq PIS Nota %</th>
                                    <th style="width: 100px;">Alíq COFINS Nota %</th>
                                    <th style="width: 100px;">Valor PIS SPED R$</th>
                                    <th style="width: 100px;">Valor COFINS SPED R$</th>
                                    <th style="width: 100px;">Valor PIS Nota R$</th>
                                    <th style="width: 100px;">Valor COFINS Nota R$</th>
                                ` : `
                                    <th style="width: 80px;">CST SPED</th>
                                    <th style="width: 80px;">CST Nota</th>
                                    <th style="width: 80px;">CSOSN Nota</th>
                                    ${tributo === 'icms' || tributo === 'icms_st' ? '<th style="width: 100px;">Redução SPED %</th>' : ''}
                                    <th style="width: 100px;">Base SPED R$</th>
                                    <th style="width: 100px;">Base Nota R$</th>
                                    <th style="width: 100px;">Alíquota SPED %</th>
                                    <th style="width: 100px;">Alíquota Nota %</th>
                                    <th style="width: 100px;">ICMS SPED R$</th>
                                    <th style="width: 100px;">ICMS Nota R$</th>
                                `}
                                <th style="width: 100px;">Match</th>
                                <th style="width: 80px;">Status</th>
                                <th style="width: 100px;">Ações</th>
                            </tr>
                            <tr class="filters">
                                <th></th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="Nota Fiscal" data-original-index="1"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="Emissão" data-original-index="2"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="Nome/Razão Social" data-original-index="3"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="UF" data-original-index="4"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="Regime Parceiro" data-original-index="5"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="PRODUTO CÓDIGO SPED" data-original-index="6"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="PRODUTO CÓDIGO NOTA" data-original-index="7"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="Descrição" data-original-index="8"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="NCM NFe" data-original-index="9"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="NCM SPED" data-original-index="10"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="Tipo" data-original-index="11"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="Descrição Tipo" data-original-index="12"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="Produto Nota" data-original-index="13"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="Origem SPED" data-original-index="14"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="Origem Nota" data-original-index="15"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="CFOP SPED" data-original-index="16"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="CFOP Nota" data-original-index="17"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                ${tributo === 'pis_cofins' ? `
                                    <th>
                                        <input type="text" class="form-control form-control-sm column-filter"
                                               data-column-name="CST PIS SPED" data-original-index="17"
                                               placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                    </th>
                                    <th>
                                        <input type="text" class="form-control form-control-sm column-filter"
                                               data-column-name="CST COFINS SPED" data-original-index="18"
                                               placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                    </th>
                                    <th>
                                        <input type="text" class="form-control form-control-sm column-filter"
                                               data-column-name="CST PIS Nota" data-original-index="19"
                                               placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                    </th>
                                    <th>
                                        <input type="text" class="form-control form-control-sm column-filter"
                                               data-column-name="CST COFINS Nota" data-original-index="20"
                                               placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                    </th>
                                    <th>
                                        <input type="text" class="form-control form-control-sm column-filter"
                                               data-column-name="CSOSN Nota" data-original-index="21"
                                               placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                    </th>
                                    <th><input type="text" class="form-control form-control-sm column-filter" data-column-name="Base PIS SPED R$" data-original-index="22" placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;"></th>
                                    <th><input type="text" class="form-control form-control-sm column-filter" data-column-name="Base COFINS SPED R$" data-original-index="23" placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;"></th>
                                    <th><input type="text" class="form-control form-control-sm column-filter" data-column-name="Base PIS Nota R$" data-original-index="24" placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;"></th>
                                    <th><input type="text" class="form-control form-control-sm column-filter" data-column-name="Base COFINS Nota R$" data-original-index="25" placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;"></th>
                                    <th>
                                        <input type="text" class="form-control form-control-sm column-filter"
                                               data-column-name="Alíq PIS SPED %" data-original-index="26"
                                               placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                    </th>
                                    <th>
                                        <input type="text" class="form-control form-control-sm column-filter"
                                               data-column-name="Alíq COFINS SPED %" data-original-index="27"
                                               placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                    </th>
                                    <th>
                                        <input type="text" class="form-control form-control-sm column-filter"
                                               data-column-name="Alíq PIS Nota %" data-original-index="28"
                                               placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                    </th>
                                    <th>
                                        <input type="text" class="form-control form-control-sm column-filter"
                                               data-column-name="Alíq COFINS Nota %" data-original-index="29"
                                               placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                    </th>
                                    <th><input type="text" class="form-control form-control-sm column-filter" data-column-name="Valor PIS SPED R$" data-original-index="30" placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;"></th>
                                    <th><input type="text" class="form-control form-control-sm column-filter" data-column-name="Valor COFINS SPED R$" data-original-index="31" placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;"></th>
                                    <th><input type="text" class="form-control form-control-sm column-filter" data-column-name="Valor PIS Nota R$" data-original-index="32" placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;"></th>
                                    <th><input type="text" class="form-control form-control-sm column-filter" data-column-name="Valor COFINS Nota R$" data-original-index="33" placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;"></th>
                                ` : `
                                    <th>
                                        <input type="text" class="form-control form-control-sm column-filter"
                                               data-column-name="CST SPED" data-original-index="17"
                                               placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                    </th>
                                    <th>
                                        <input type="text" class="form-control form-control-sm column-filter"
                                               data-column-name="CST Nota" data-original-index="18"
                                               placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                    </th>
                                    <th>
                                        <input type="text" class="form-control form-control-sm column-filter"
                                               data-column-name="CSOSN Nota" data-original-index="19"
                                               placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                    </th>
                                    ${tributo === 'icms' || tributo === 'icms_st' ? `
                                        <th>
                                            <input type="text" class="form-control form-control-sm column-filter"
                                                   data-column-name="Redução SPED %" data-original-index="20"
                                                   placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                        </th>
                                    ` : ''}
                                    <th><input type="text" class="form-control form-control-sm column-filter" data-column-name="Base SPED R$" data-original-index="${tributo === 'icms' || tributo === 'icms_st' ? '21' : '20'}" placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;"></th>
                                    <th><input type="text" class="form-control form-control-sm column-filter" data-column-name="Base Nota R$" data-original-index="${tributo === 'icms' || tributo === 'icms_st' ? '22' : '21'}" placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;"></th>
                                    <th>
                                        <input type="text" class="form-control form-control-sm column-filter"
                                               data-column-name="Alíquota SPED %" data-original-index="${tributo === 'icms' || tributo === 'icms_st' ? '23' : '22'}"
                                               placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                    </th>
                                    <th>
                                        <input type="text" class="form-control form-control-sm column-filter"
                                               data-column-name="Alíquota Nota %" data-original-index="${tributo === 'icms' || tributo === 'icms_st' ? '24' : '23'}"
                                               placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                    </th>
                                    <th><input type="text" class="form-control form-control-sm column-filter" data-column-name="Valor SPED R$" data-original-index="${tributo === 'icms' || tributo === 'icms_st' ? '25' : '24'}" placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;"></th>
                                    <th><input type="text" class="form-control form-control-sm column-filter" data-column-name="Valor Nota R$" data-original-index="${tributo === 'icms' || tributo === 'icms_st' ? '26' : '25'}" placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;"></th>
                                `}
                                <th>
                                    <input type="text" class="form-control form-control-sm column-filter"
                                           data-column-name="Match" data-original-index="${tributo === 'pis_cofins' ? '34' : (tributo === 'icms' || tributo === 'icms_st' ? '27' : '26')}"
                                           placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                                </th>
                                <th>
                                   <input type="text" class="form-control form-control-sm column-filter"
                                          data-column-name="Status" data-original-index="${tributo === 'pis_cofins' ? '35' : (tributo === 'icms' || tributo === 'icms_st' ? '28' : '27')}"
                                          placeholder="Filtrar..." style="width: 100%; font-size: 0.8rem;">
                               </th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
    `;

    auditoriasFiltradas.forEach(auditoria => {
        const matchBadge = getMatchBadgeHtml(auditoria.match_type, auditoria.confidence_score);

        // Debug temporário para verificar dados XML
        if (auditoria.xml_data) {
            console.log('DEBUG XML Data:', auditoria.xml_data);
            console.log('DEBUG ICMS BC:', auditoria.xml_data.icms_bc);
            console.log('DEBUG ICMS Aliquota:', auditoria.xml_data.icms_aliquota);
            console.log('DEBUG ICMS Valor:', auditoria.xml_data.icms_valor);
        }

        // Para pis_cofins, mostrar dados de ambos
        if (tributo === 'pis_cofins') {
            const pisData = auditoria.tributos['pis'] || {};
            const cofinsData = auditoria.tributos['cofins'] || {};
            const statusBadgePis = getStatusBadgeHtml(pisData.status);
            const statusBadgeCofins = getStatusBadgeHtml(cofinsData.status);

            html += `
                <tr data-auditoria-id="${auditoria.id}">
                    <td>
                        ${auditoria.match_type !== 'unmatched_xml' && auditoria.match_type !== 'unmatched_sped' && !isMatchAprovadoParaTributo(auditoria, tributo) ?
                            `<input type="checkbox" class="match-checkbox" value="${auditoria.xml_item_id}-${auditoria.sped_item_id}" onchange="updateSelectedCount()">` :
                            isMatchAprovadoParaTributo(auditoria, tributo) ? '<i class="fas fa-check-circle text-success" title="Match já aprovado para este tributo"></i>' : ''
                        }
                    </td>
                    <td><small>${auditoria.numero_nf || '-'}</small></td>
                    <td><small>${auditoria.data_emissao ? formatDate(auditoria.data_emissao) : '-'}</small></td>
                    <td><small>${auditoria.parceiro_nome || auditoria.cliente_nome || '-'}</small></td>
                    <td><small>${auditoria.cliente_uf || '-'}</small></td>
                    <td><small>${auditoria.regime_parceiro || (auditoria.parceiro_simples_nacional ? 'Simples Nacional' : 'Regime Normal') || '-'}</small></td>
                    <td><small>${auditoria.sped_cod_item || auditoria.sped_codigo_produto || '-'}</small></td>
                    <td><small>${auditoria.xml_codigo_produto || '-'}</small></td>
                    <td>
                        <div style="font-size: 0.8em;" title="${auditoria.xml_descricao || auditoria.sped_descricao || '-'}">
                            ${(auditoria.xml_descricao || auditoria.sped_descricao || '-').substring(0, 30)}${(auditoria.xml_descricao || auditoria.sped_descricao || '').length > 30 ? '...' : ''}
                        </div>
                    </td>
                    <td><small>${auditoria.xml_ncm || '-'}</small></td>
                    <td><small>${auditoria.sped_ncm || '-'}</small></td>
                    <td><small>${auditoria.tipo_produto || '-'}</small></td>
                    <td><small>${auditoria.descricao_tipo_produto || '-'}</small></td>
                    <td><small>${auditoria.produto_nota || '-'}</small></td>
                    <td><small>${auditoria.sped_origem || '-'}</small></td>
                    <td><small>${auditoria.xml_origem || auditoria.origem_nota || '-'}</small></td>
                    <td><small>${auditoria.sped_cfop || '-'}</small></td>
                    <td><small>${auditoria.xml_cfop || '-'}</small></td>
                    <!-- Colunas separadas para PIS e COFINS -->
                    <td><small>${pisData.cst || '-'}</small></td>
                    <td><small>${cofinsData.cst || '-'}</small></td>
                    <td><small>${auditoria.xml_data?.pis_cst || '-'}</small></td>
                    <td><small>${auditoria.xml_data?.cofins_cst || '-'}</small></td>
                    <td><small>${auditoria.xml_csosn || auditoria.csosn_nota || '-'}</small></td>
                    <td><small>${formatCurrency(pisData.bc || 0)}</small></td>
                    <td><small>${formatCurrency(cofinsData.bc || 0)}</small></td>
                    <td><small>${formatCurrency(auditoria.xml_data?.pis_bc || 0)}</small></td>
                    <td><small>${formatCurrency(auditoria.xml_data?.cofins_bc || 0)}</small></td>
                    <td><small>${formatPercentage(pisData.aliquota || 0)}</small></td>
                    <td><small>${formatPercentage(cofinsData.aliquota || 0)}</small></td>
                    <td><small>${formatPercentage(auditoria.xml_data?.pis_aliquota || 0)}</small></td>
                    <td><small>${formatPercentage(auditoria.xml_data?.cofins_aliquota || 0)}</small></td>
                    <td><small>${formatCurrency(pisData.valor || 0)}</small></td>
                    <td><small>${formatCurrency(cofinsData.valor || 0)}</small></td>
                    <td><small>${formatCurrency(auditoria.xml_data?.pis_valor || 0)}</small></td>
                    <td><small>${formatCurrency(auditoria.xml_data?.cofins_valor || 0)}</small></td>
                    <td>${matchBadge}</td>
                    <td>
                        <div class="d-flex flex-column gap-1">
                            <small>PIS: ${statusBadgePis}</small>
                            <small>COFINS: ${statusBadgeCofins}</small>
                        </div>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary btn-sm"
                                    onclick="editarDadosSpedModal(${auditoria.id}, 'pis')"
                                    title="Editar PIS">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-warning btn-sm"
                                    onclick="editarDadosSpedModal(${auditoria.id}, 'cofins')"
                                    title="Editar COFINS">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info btn-sm"
                                    onclick="verDetalhesComparativoModal(${auditoria.id})"
                                    title="Ver detalhes">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        } else {
            const tributoData = auditoria.tributos[tributo] || {};

            const descricao = auditoria.xml_descricao || auditoria.sped_descricao || auditoria.xml_produto_descricao || auditoria.sped_produto_descricao || '-';
            const descricaoTruncada = descricao.length > 30 ? descricao.substring(0, 30) + '...' : descricao;

            html += `
                <tr data-auditoria-id="${auditoria.id}">
                    <td>
                        ${auditoria.match_type !== 'unmatched_xml' && auditoria.match_type !== 'unmatched_sped' && !isMatchAprovadoParaTributo(auditoria, tributo) ?
                            `<input type="checkbox" class="match-checkbox" value="${auditoria.xml_item_id}-${auditoria.sped_item_id}" onchange="updateSelectedCount()">` :
                            isMatchAprovadoParaTributo(auditoria, tributo) ? '<i class="fas fa-check-circle text-success" title="Match já aprovado para este tributo"></i>' : ''
                        }
                    </td>
                    <td><small>${auditoria.numero_nf || '-'}</small></td>
                    <td><small>${auditoria.data_emissao ? formatDate(auditoria.data_emissao) : '-'}</small></td>
                    <td><small>${auditoria.parceiro_nome || auditoria.cliente_nome || '-'}</small></td>
                    <td><small>${auditoria.cliente_uf || '-'}</small></td>
                    <td><small>${auditoria.regime_parceiro || '-'}</small></td>
                    <td><small>${auditoria.sped_cod_item || auditoria.sped_codigo_produto || '-'}</small></td>
                    <td><small>${auditoria.xml_codigo_produto || '-'}</small></td>
                    <td>
                        <div style="font-size: 0.8em;" title="${descricao}">
                            ${descricaoTruncada}
                        </div>
                    </td>
                    <td><small>${auditoria.xml_ncm || '-'}</small></td>
                    <td><small>${auditoria.sped_ncm || auditoria.sped_data?.ncm || '-'}</small></td>
                    <td><small>${auditoria.tipo_produto || '-'}</small></td>
                    <td><small>${auditoria.descricao_tipo_produto || '-'}</small></td>
                    <td><small>${auditoria.produto_nota || auditoria.xml_data?.descricao || '-'}</small></td>
                    <td><small class="${getSpedValueClass(auditoria, 'origem')}">${auditoria.sped_origem || '-'}</small></td>
                    <td><small>${auditoria.xml_origem || auditoria.xml_data?.origem || '-'}</small></td>
                    <td><small class="${getSpedValueClass(auditoria, 'cfop')}">${auditoria.sped_cfop || '-'}</small></td>
                    <td><small>${auditoria.xml_cfop || '-'}</small></td>
                    <td><small class="${getSpedValueClass(auditoria, 'cst')}">${tributoData.cst || '-'}</small></td>
                    <td><small>${auditoria.xml_cst || auditoria.xml_data?.cst || '-'}</small></td>
                    <td><small>${auditoria.xml_csosn || auditoria.xml_data?.csosn || '-'}</small></td>
                    ${tributo === 'icms' || tributo === 'icms_st' ? `<td><small>${formatPercentage(tributoData.reducao || 0)}</small></td>` : ''}
                    <td><small>${formatCurrency(tributoData.bc || 0)}</small></td>
                    <td><small>${formatCurrency(getXmlTributoValue(auditoria, tributo, 'bc') || 0)}</small></td>
                    <td><small class="${getSpedValueClass(auditoria, 'aliquota')}">${formatPercentage(tributoData.aliquota || 0)}</small></td>
                    <td><small>${formatPercentage(getXmlTributoValue(auditoria, tributo, 'aliquota') || 0)}</small></td>
                    <td><small>${formatCurrency(tributoData.valor || 0)}</small></td>
                    <td><small>${formatCurrency(getXmlTributoValue(auditoria, tributo, 'valor') || 0)}</small></td>
                    <td>${matchBadge}</td>
                    <td>${getStatusBadgeHtml(getStatusTributo(auditoria, tributo))}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary btn-sm"
                                    onclick="editarDadosSpedModal(${auditoria.id}, '${tributo}')"
                                    title="Editar dados SPED">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info btn-sm"
                                    onclick="verDetalhesComparativoModal(${auditoria.id})"
                                    title="Ver detalhes">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${auditoria.match_type === 'unmatched_sped' ? `
                                <button class="btn btn-outline-warning btn-sm"
                                        onclick="abrirModalMatchingManual(${auditoria.sped_item_id})"
                                        title="Matching Manual">
                                    <i class="fas fa-link"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `;
        }
    });

    html += `
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;

    // Selecionar quantidade inicial e configurar alteração
    const recordsSelect = document.getElementById('records-per-page-select');
    if (recordsSelect) {
        recordsSelect.value = auditoriaComparativaData.pageLength;
        recordsSelect.addEventListener('change', function() {
            changeAuditoriaComparativaPageLength(parseInt(this.value));
        });
    }

    // Inicializar DataTable com paginação
    const table = document.getElementById('auditoria-comparativa-table');
    if ($.fn.DataTable && table) {
        if (auditoriaComparativaTable) {
            auditoriaComparativaTable.destroy();
        }
        auditoriaComparativaTable = $(table).DataTable({
            language: {
                url: '/static/js/vendor/datatables/pt-BR.json'
            },
            pageLength: auditoriaComparativaData.pageLength,
            lengthMenu: [ [200, 500, 1000, 2000, 3000], [200, 500, 1000, 2000, 3000] ],
            responsive: true
        });
    }

    // Configurar filtros após renderizar a tabela
    setupTableFiltersAuditoriaComparativa();

    // Configurar eventos de seleção
    setupSelectionEvents();
}

/**
 * Retorna badge HTML do tipo de match
 */
function getMatchBadgeHtml(matchType, confidence) {
    const badges = {
        'direct': '<span class="badge bg-success">Direto</span>',
        'embedding': '<span class="badge bg-primary">IA</span>',
        'manual': '<span class="badge bg-info">Manual</span>',
        'unmatched_xml': '<span class="badge bg-warning">XML sem par</span>',
        'unmatched_sped': '<span class="badge bg-warning">SPED sem par</span>'
    };

    let badge = badges[matchType] || '<span class="badge bg-secondary">Desconhecido</span>';

    if (confidence && confidence > 0) {
        const confidencePercent = Math.round(confidence * 100);
        badge += `<br><small class="text-muted">${confidencePercent}%</small>`;
    }

    return badge;
}

/**
 * Retorna descrição do tipo de match
 */
function getMatchTypeDescription(matchType) {
    const descriptions = {
        'direct': 'Match direto por critérios exatos',
        'embedding': 'Match por inteligência artificial',
        'manual': 'Match manual pelo usuário',
        'unmatched_xml': 'Item XML sem correspondência',
        'unmatched_sped': 'Item SPED sem correspondência'
    };

    return descriptions[matchType] || 'Tipo desconhecido';
}

/**
 * Retorna badge HTML do status
 */
function getStatusBadgeHtml(status) {
    const badges = {
        'conforme': '<span class="badge bg-success">Conforme</span>',
        'divergente': '<span class="badge bg-danger">Divergente</span>',
        'pendente': '<span class="badge bg-warning">Pendente</span>',
        'aprovado': '<span class="badge bg-info">Aprovado</span>'
    };

    return badges[status] || '<span class="badge bg-secondary">-</span>';
}



/**
 * Formata porcentagem
 */
function formatPercentage(value) {
    if (!value || value === 0) return '0%';
    return `${parseFloat(value).toFixed(2)}%`;
}

/**
 * Edita dados SPED em modal
 */
function editarDadosSpedModal(auditoriaId, tributo) {
    // Buscar dados atuais
    const auditoria = auditoriaComparativaData.auditorias.find(a => a.id === auditoriaId);
    if (!auditoria) {
        showMessage('Auditoria não encontrada', 'error');
        return;
    }

    const tributoData = auditoria.tributos[tributo];

    // Criar modal de edição
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'modal-editar-sped';
    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        Editar Dados SPED - ${TRIBUTOS_CONFIG[tributo]?.label || tributo.toUpperCase()}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <form id="form-editar-sped">
                                ${renderSpedEditFormHtml(tributo, tributoData)}

                                <div class="mt-3">
                                    <label class="form-label">Motivo da Alteração</label>
                                    <textarea class="form-control" name="motivo_alteracao" rows="2"
                                              placeholder="Descreva o motivo da alteração..."></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <div id="sugestoes-container">
                                <div class="d-flex justify-content-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Carregando sugestões...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-success" id="btn-auto-aprovar" style="display: none;"
                            onclick="autoAprovarItem(${auditoriaId}, '${tributo}')">
                        <i class="fas fa-magic me-2"></i>Auto-Aprovar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="salvarEdicaoSped(${auditoriaId}, '${tributo}')">
                        <i class="fas fa-save me-2"></i>Salvar Alterações
                    </button>
                </div>
            </div>
        </div>
    `;

    // Remover modal anterior se existir
    const existingModal = document.getElementById('modal-editar-sped');
    if (existingModal) {
        existingModal.remove();
    }

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Carregar sugestões inteligentes
    carregarSugestoesInteligentes(auditoriaId, tributo);

    // Remover modal do DOM quando fechado
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

/**
 * Carrega sugestões inteligentes para o item
 */
function carregarSugestoesInteligentes(auditoriaId, tributo) {
    const empresaId = localStorage.getItem('selectedCompany');
    const container = document.getElementById('sugestoes-container');

    if (!container) return;

    fetch(`/api/auditoria-comparativa/sugestoes/${auditoriaId}?tributo=${tributo}&empresa_id=${empresaId}`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderSugestoesInteligentes(data);
        } else {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Nenhuma sugestão disponível
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Erro ao carregar sugestões:', error);
        container.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Erro ao carregar sugestões
            </div>
        `;
    });
}

/**
 * Renderiza sugestões inteligentes
 */
function renderSugestoesInteligentes(data) {
    const container = document.getElementById('sugestoes-container');
    if (!container) return;

    let html = `
        <h6><i class="fas fa-lightbulb me-2"></i>Sugestões Inteligentes</h6>
        <div class="mb-3">
            <small class="text-muted">Score de Confiança: ${Math.round(data.score_confianca * 100)}%</small>
        </div>
    `;

    // Mostrar botão de auto-aprovação se disponível
    if (data.pode_auto_aprovar) {
        html += `
            <div class="alert alert-success">
                <i class="fas fa-magic me-2"></i>
                <strong>Auto-aprovação disponível!</strong><br>
                <small>${data.motivo_auto_aprovacao}</small>
            </div>
        `;

        // Mostrar botão de auto-aprovação
        const btnAutoAprovar = document.getElementById('btn-auto-aprovar');
        if (btnAutoAprovar) {
            btnAutoAprovar.style.display = 'inline-block';
        }
    }

    // Mostrar sugestões de correção
    if (data.sugestoes_correcao && Object.keys(data.sugestoes_correcao).length > 0) {
        html += `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-edit me-2"></i>Sugestões de Correção</h6>
                </div>
                <div class="card-body">
        `;

        Object.entries(data.sugestoes_correcao).forEach(([campo, sugestao]) => {
            html += `
                <div class="mb-2">
                    <strong>${campo.toUpperCase()}:</strong>
                    <button class="btn btn-sm btn-outline-primary ms-2"
                            onclick="aplicarSugestao('${campo}', '${sugestao.valor_sugerido}')">
                        ${sugestao.valor_sugerido}
                        <small class="text-muted">(${Math.round(sugestao.confianca * 100)}%)</small>
                    </button>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;
    }

    // Mostrar histórico de sugestões
    if (data.sugestoes_historico && data.sugestoes_historico.length > 0) {
        html += `
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-history me-2"></i>Histórico de Aprendizado</h6>
                </div>
                <div class="card-body">
        `;

        data.sugestoes_historico.slice(0, 3).forEach(sugestao => {
            const tipoLabel = {
                'match_exato': 'Match Exato',
                'similar_cliente': 'Similar (Cliente)',
                'similar_codigo': 'Similar (Código)'
            };

            html += `
                <div class="mb-2 p-2 border rounded">
                    <small>
                        <strong>${tipoLabel[sugestao.tipo] || sugestao.tipo}</strong><br>
                        Confiança: ${Math.round(sugestao.confianca * 100)}%<br>
                        Data: ${new Date(sugestao.historico.data_acao).toLocaleDateString('pt-BR')}
                    </small>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;
    }

    container.innerHTML = html;
}

/**
 * Aplica uma sugestão ao formulário
 */
function aplicarSugestao(campo, valor) {
    const input = document.querySelector(`#form-editar-sped [name="${campo}"]`);
    if (input) {
        input.value = valor;
        input.focus();
        showMessage(`Sugestão aplicada para ${campo.toUpperCase()}`, 'success');
    }
}

/**
 * Processa auto-aprovação do item
 */
function autoAprovarItem(auditoriaId, tributo) {
    const empresaId = localStorage.getItem('selectedCompany');

    if (!confirm('Deseja auto-aprovar este item baseado nas sugestões do histórico?')) {
        return;
    }

    showLoading('Processando auto-aprovação...');

    fetch(`/api/auditoria-comparativa/auto-aprovar/${auditoriaId}`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            tributo: tributo,
            empresa_id: parseInt(empresaId)
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            // Atualizar dados locais
            const auditoriaIndex = auditoriaComparativaData.auditorias.findIndex(a => a.id === auditoriaId);
            if (auditoriaIndex !== -1) {
                auditoriaComparativaData.auditorias[auditoriaIndex] = data.auditoria;
            }

            // Fechar modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('modal-editar-sped'));
            if (modal) modal.hide();

            // Recarregar tabela
            renderAuditoriaComparativaTributo(auditoriaComparativaData.currentTributo);

            showMessage('Item auto-aprovado com sucesso!', 'success');
        } else {
            showMessage(data.message || 'Erro na auto-aprovação', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro na auto-aprovação:', error);
        showMessage('Erro na auto-aprovação', 'error');
    });
}

/**
 * Salva edição dos dados SPED
 */
function salvarEdicaoSped(auditoriaId, tributo) {
    const form = document.getElementById('form-editar-sped');
    if (!form) return;

    const formData = new FormData(form);
    const empresaId = localStorage.getItem('selectedCompany');

    // Construir objeto de dados
    const dadosSped = {
        empresa_id: parseInt(empresaId),
        motivo_alteracao: formData.get('motivo_alteracao')
    };

    // Campos básicos do item (fora do tributo específico)
    const camposBasicos = ['cfop', 'ncm', 'origem_icms', 'tipo_item'];
    camposBasicos.forEach(campo => {
        const valor = formData.get(campo);
        if (valor) {
            dadosSped[campo] = valor;
        }
    });

    // Adicionar dados específicos do tributo
    dadosSped[tributo] = {};

    // Coletar campos tributários do formulário
    const inputs = form.querySelectorAll('input[name], select[name], textarea[name]');
    inputs.forEach(input => {
        if (input.name !== 'motivo_alteracao' && !camposBasicos.includes(input.name) && input.value) {
            dadosSped[tributo][input.name] = input.type === 'number' ? parseFloat(input.value) : input.value;
        }
    });

    console.log('DEBUG FRONTEND - Dados sendo enviados:', dadosSped);
    console.log('DEBUG FRONTEND - Auditoria ID:', auditoriaId);
    console.log('DEBUG FRONTEND - Tributo:', tributo);

    showLoading('Salvando alterações...');

    fetch(`/api/auditoria-comparativa/editar-sped/${auditoriaId}`, {
        method: 'PUT',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(dadosSped)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            // Atualizar dados locais
            const auditoriaIndex = auditoriaComparativaData.auditorias.findIndex(a => a.id === auditoriaId);
            if (auditoriaIndex !== -1) {
                auditoriaComparativaData.auditorias[auditoriaIndex] = data.auditoria;
            }

            // Fechar modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('modal-editar-sped'));
            if (modal) modal.hide();

            // Recarregar tabela
            renderAuditoriaComparativaTributo(auditoriaComparativaData.currentTributo);

            showMessage('Dados SPED atualizados com sucesso', 'success');
        } else {
            showMessage(data.message || 'Erro ao salvar alterações', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao salvar:', error);
        showMessage('Erro ao salvar alterações', 'error');
    });
}

/**
 * Ver detalhes comparativos em modal
 */
function verDetalhesComparativoModal(auditoriaId) {
    const auditoria = auditoriaComparativaData.auditorias.find(a => a.id === auditoriaId);
    if (!auditoria) {
        showMessage('Auditoria não encontrada', 'error');
        return;
    }

    // Criar modal de detalhes
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'modal-detalhes-comparativo';
    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-eye me-2"></i>
                        Detalhes Comparativos - Item ${auditoria.xml_item_id || auditoria.sped_item_id}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${renderDetalhesComparativoHtml(auditoria)}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                    ${auditoria.match_type !== 'unmatched_xml' && auditoria.match_type !== 'unmatched_sped' ? `
                        <button type="button" class="btn btn-success" onclick="aprovarMatch(${auditoria.xml_item_id}, ${auditoria.sped_item_id})">
                            <i class="fas fa-check me-2"></i>Aprovar Match
                        </button>
                        <button type="button" class="btn btn-danger" onclick="rejeitarMatch(${auditoria.xml_item_id}, ${auditoria.sped_item_id})">
                            <i class="fas fa-times me-2"></i>Rejeitar Match
                        </button>
                    ` : ''}
                </div>
            </div>
        </div>
    `;

    // Remover modal anterior se existir
    const existingModal = document.getElementById('modal-detalhes-comparativo');
    if (existingModal) {
        existingModal.remove();
    }

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Remover modal do DOM quando fechado
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

/**
 * Renderiza formulário de edição SPED por tributo
 */
function renderSpedEditFormHtml(tributo, data) {
    switch (tributo) {
        case 'icms':
            return `
                <!-- Dados Básicos do Item -->
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">CFOP</label>
                        <input type="text" class="form-control" name="cfop" value="${data.cfop || ''}" maxlength="4">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">NCM</label>
                        <input type="text" class="form-control" name="ncm" value="${data.ncm || ''}" maxlength="8">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Tipo Item</label>
                        <select class="form-control" name="tipo_item">
                            <option value="">Selecione...</option>
                            <option value="01" ${data.tipo_item === '01' ? 'selected' : ''}>01 - Mercadoria para Revenda</option>
                            <option value="02" ${data.tipo_item === '02' ? 'selected' : ''}>02 - Matéria-Prima</option>
                            <option value="03" ${data.tipo_item === '03' ? 'selected' : ''}>03 - Embalagem</option>
                            <option value="04" ${data.tipo_item === '04' ? 'selected' : ''}>04 - Produto em Processo</option>
                            <option value="05" ${data.tipo_item === '05' ? 'selected' : ''}>05 - Produto Acabado</option>
                            <option value="06" ${data.tipo_item === '06' ? 'selected' : ''}>06 - Subproduto</option>
                            <option value="07" ${data.tipo_item === '07' ? 'selected' : ''}>07 - Produto Intermediário</option>
                            <option value="08" ${data.tipo_item === '08' ? 'selected' : ''}>08 - Material de Uso e Consumo</option>
                            <option value="09" ${data.tipo_item === '09' ? 'selected' : ''}>09 - Ativo Imobilizado</option>
                            <option value="10" ${data.tipo_item === '10' ? 'selected' : ''}>10 - Serviços</option>
                            <option value="99" ${data.tipo_item === '99' ? 'selected' : ''}>99 - Outros</option>
                        </select>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-12">
                        <label class="form-label">Origem ICMS</label>
                        <select class="form-control" name="origem_icms">
                            <option value="">Selecione...</option>
                            <option value="0" ${data.origem_icms === '0' ? 'selected' : ''}>0 - Nacional</option>
                            <option value="1" ${data.origem_icms === '1' ? 'selected' : ''}>1 - Estrangeira - Importação direta</option>
                            <option value="2" ${data.origem_icms === '2' ? 'selected' : ''}>2 - Estrangeira - Adquirida no mercado interno</option>
                            <option value="3" ${data.origem_icms === '3' ? 'selected' : ''}>3 - Nacional - Conteúdo de importação superior a 40%</option>
                            <option value="4" ${data.origem_icms === '4' ? 'selected' : ''}>4 - Nacional - Produção em conformidade com processos produtivos básicos</option>
                            <option value="5" ${data.origem_icms === '5' ? 'selected' : ''}>5 - Nacional - Conteúdo de importação inferior ou igual a 40%</option>
                            <option value="6" ${data.origem_icms === '6' ? 'selected' : ''}>6 - Estrangeira - Importação direta, sem similar nacional</option>
                            <option value="7" ${data.origem_icms === '7' ? 'selected' : ''}>7 - Estrangeira - Adquirida no mercado interno, sem similar nacional</option>
                            <option value="8" ${data.origem_icms === '8' ? 'selected' : ''}>8 - Nacional - Conteúdo de importação superior a 70%</option>
                        </select>
                    </div>
                </div>

                <!-- Dados Tributários ICMS -->
                <div class="row mt-2">
                    <div class="col-md-6">
                        <label class="form-label">CST ICMS</label>
                        <input type="text" class="form-control" name="cst" value="${data.cst || ''}" maxlength="3">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">CSOSN (Simples Nacional)</label>
                        <input type="text" class="form-control" name="csosn" value="${data.csosn || ''}" maxlength="3" placeholder="Apenas para Simples Nacional">
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <label class="form-label">Base de Cálculo (R$)</label>
                        <input type="number" class="form-control" name="bc" value="${data.bc || ''}" step="0.01">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Alíquota (%)</label>
                        <input type="number" class="form-control" name="aliquota" value="${data.aliquota || ''}" step="0.01">
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <label class="form-label">Valor ICMS (R$)</label>
                        <input type="number" class="form-control" name="valor" value="${data.valor || ''}" step="0.01">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">% Redução</label>
                        <input type="number" class="form-control" name="reducao" value="${data.reducao || ''}" step="0.0001">
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <label class="form-label">% Crédito ICMS (Simples Nacional)</label>
                        <input type="number" class="form-control" name="credito_icms" value="${data.credito_icms || ''}" step="0.0001" placeholder="Apenas para Simples Nacional">
                    </div>
                </div>
            `;

        case 'icms_st':
            return `
                <!-- Dados Básicos do Item -->
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">CFOP</label>
                        <input type="text" class="form-control" name="cfop" value="${data.cfop || ''}" maxlength="4">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">NCM</label>
                        <input type="text" class="form-control" name="ncm" value="${data.ncm || ''}" maxlength="8">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">CST ICMS</label>
                        <input type="text" class="form-control" name="cst" value="${data.cst || ''}" maxlength="3">
                    </div>
                </div>

                <!-- Dados Tributários ICMS-ST -->
                <div class="row mt-2">
                    <div class="col-md-6">
                        <label class="form-label">Base de Cálculo ST (R$)</label>
                        <input type="number" class="form-control" name="bc" value="${data.bc || ''}" step="0.01">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Alíquota ST (%)</label>
                        <input type="number" class="form-control" name="aliquota" value="${data.aliquota || ''}" step="0.01">
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <label class="form-label">Valor ICMS-ST (R$)</label>
                        <input type="number" class="form-control" name="valor" value="${data.valor || ''}" step="0.01">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">MVA (%)</label>
                        <input type="number" class="form-control" name="mva" value="${data.mva || ''}" step="0.0001">
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <label class="form-label">% Redução ST</label>
                        <input type="number" class="form-control" name="reducao" value="${data.reducao || ''}" step="0.0001">
                    </div>
                </div>
            `;

        default:
            return `
                <!-- Dados Básicos do Item -->
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">CFOP</label>
                        <input type="text" class="form-control" name="cfop" value="${data.cfop || ''}" maxlength="4">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">NCM</label>
                        <input type="text" class="form-control" name="ncm" value="${data.ncm || ''}" maxlength="8">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">CST ${TRIBUTOS_CONFIG[tributo]?.label || tributo.toUpperCase()}</label>
                        <input type="text" class="form-control" name="cst" value="${data.cst || ''}" maxlength="2">
                    </div>
                </div>

                <!-- Dados Tributários -->
                <div class="row mt-2">
                    <div class="col-md-6">
                        <label class="form-label">Base de Cálculo (R$)</label>
                        <input type="number" class="form-control" name="bc" value="${data.bc || ''}" step="0.01">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Alíquota (%)</label>
                        <input type="number" class="form-control" name="aliquota" value="${data.aliquota || ''}" step="0.0001">
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <label class="form-label">Valor ${TRIBUTOS_CONFIG[tributo]?.label || tributo.toUpperCase()} (R$)</label>
                        <input type="number" class="form-control" name="valor" value="${data.valor || ''}" step="0.01">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">% Redução</label>
                        <input type="number" class="form-control" name="reducao" value="${data.reducao || ''}" step="0.0001">
                    </div>
                </div>
            `;
    }
}

/**
 * Renderiza detalhes comparativos
 */
function renderDetalhesComparativoHtml(auditoria) {
    return `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-file-code me-2"></i>Dados XML</h6>
                <div class="card">
                    <div class="card-body">
                        <p><strong>Descrição:</strong> ${auditoria.xml_data.descricao || 'N/A'}</p>
                        <p><strong>Quantidade:</strong> ${auditoria.xml_data.quantidade || 'N/A'}</p>
                        <p><strong>Valor Total:</strong> ${formatCurrency(auditoria.xml_data.valor_total || 0)}</p>
                        <p><strong>Unidade:</strong> ${auditoria.xml_data.unidade || 'N/A'}</p>
                        <p><strong>Código Produto:</strong> ${auditoria.xml_codigo_produto || auditoria.codigo_produto || 'N/A'}</p>
                        <p><strong>NCM:</strong> ${auditoria.xml_data.ncm || 'N/A'}</p>
                        <p><strong>CFOP:</strong> ${auditoria.xml_data.cfop || 'N/A'}</p>
                        <p><strong>Origem:</strong> ${auditoria.xml_origem || auditoria.xml_data?.origem || 'N/A'}</p>
                        <p><strong>CST:</strong> ${auditoria.xml_cst || auditoria.xml_data?.cst || 'N/A'}</p>
                        <p><strong>CSOSN:</strong> ${auditoria.xml_csosn || auditoria.xml_data?.csosn || 'N/A'}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-database me-2"></i>Dados SPED</h6>
                <div class="card">
                    <div class="card-body">
                        <p><strong>Descrição:</strong> ${auditoria.sped_data.descricao || 'N/A'}</p>
                        <p><strong>Quantidade:</strong> ${auditoria.sped_data.quantidade || 'N/A'}</p>
                        <p><strong>Valor Item:</strong> ${formatCurrency(auditoria.sped_data.valor_item || 0)}</p>
                        <p><strong>Unidade:</strong> ${auditoria.sped_data.unidade || 'N/A'}</p>
                        <p><strong>Código Item:</strong> ${auditoria.sped_data.cod_item || 'N/A'}</p>
                        <p><strong>CFOP:</strong> ${auditoria.sped_data.cfop || 'N/A'}</p>
                        <p><strong>NCM:</strong> ${auditoria.sped_ncm || auditoria.sped_data?.ncm || 'N/A'}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <h6><i class="fas fa-search me-2"></i>Informações do Matching</h6>
                <div class="card">
                    <div class="card-body">
                        <p><strong>Tipo de Match:</strong> ${getMatchBadgeHtml(auditoria.match_type, auditoria.confidence_score)}</p>
                        <p><strong>Score de Confiança:</strong> ${auditoria.confidence_score ? Math.round(auditoria.confidence_score * 100) + '%' : 'N/A'}</p>
                        <p><strong>Detalhes:</strong> ${JSON.stringify(auditoria.match_details || {}, null, 2)}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <h6><i class="fas fa-calculator me-2"></i>Dados Tributários</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Tributo</th>
                                <th>CST</th>
                                <th>Base Cálculo</th>
                                <th>Alíquota</th>
                                <th>Valor</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${Object.entries(auditoria.tributos).map(([tributo, data]) => `
                                <tr>
                                    <td>${TRIBUTOS_CONFIG[tributo]?.label || tributo.toUpperCase()}</td>
                                    <td>${data.cst || '-'}</td>
                                    <td>${formatCurrency(data.bc || 0)}</td>
                                    <td>${data.aliquota || 0}%</td>
                                    <td>${formatCurrency(data.valor || 0)}</td>
                                    <td>${getStatusBadgeHtml(data.status)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
}

/**
 * Mostra estatísticas do matching em modal
 */
function showMatchingStatsModal(stats) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-chart-bar me-2"></i>
                        Estatísticas do Matching
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h4 class="text-primary">${stats.total_matches || 0}</h4>
                                    <small>Matches Encontrados</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h4 class="text-success">${Math.round(stats.match_rate || 0)}%</h4>
                                    <small>Taxa de Matching</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <h6>Tipos de Match:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Diretos: ${stats.direct_matches || 0}</li>
                        <li><i class="fas fa-brain text-primary me-2"></i>Por IA: ${stats.embedding_matches || 0}</li>
                        <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>XML sem par: ${stats.unmatched_xml_items || 0}</li>
                        <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>SPED sem par: ${stats.unmatched_sped_items || 0}</li>
                    </ul>

                    <hr>

                    <h6>Confiança dos Matches:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-star text-success me-2"></i>Alta (≥80%): ${stats.high_confidence_matches || 0}</li>
                        <li><i class="fas fa-star-half-alt text-warning me-2"></i>Média (60-79%): ${stats.medium_confidence_matches || 0}</li>
                        <li><i class="fas fa-star text-danger me-2"></i>Baixa (<60%): ${stats.low_confidence_matches || 0}</li>
                    </ul>

                    <div class="alert alert-info mt-3">
                        <small>
                            <i class="fas fa-info-circle me-2"></i>
                            Confiança média: ${Math.round((stats.average_confidence || 0) * 100)}%
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Remover modal do DOM quando fechado
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

/**
 * Obtém o texto do período atual
 */
function getCurrentPeriodText() {
    const ano = document.getElementById('year-select')?.value || localStorage.getItem('selectedYear');
    const mes = document.getElementById('month-select')?.value || localStorage.getItem('selectedMonth');

    if (!ano || !mes) {
        return 'Período não selecionado';
    }

    const meses = [
        'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
        'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];

    return `${meses[parseInt(mes) - 1]} ${ano}`;
}

/**
 * Formata data para exibição
 */
function formatDate(dateString) {
    if (!dateString) return '-';

    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('pt-BR');
    } catch (e) {
        return dateString;
    }
}

// Event listener removido - duplicado no final do arquivo

/**
 * Aprova um match sugerido
 */
function aprovarMatch(xmlItemId, spedItemId) {
    const empresaId = localStorage.getItem('selectedCompany');

    // Verificar se já foi aprovado para este tributo
    const auditoria = auditoriaComparativaData.auditorias.find(a =>
        a.xml_item_id === xmlItemId && a.sped_item_id === spedItemId
    );

    if (auditoria && isMatchAprovadoParaTributo(auditoria, auditoriaComparativaData.currentTributo)) {
        showMessage(`Este match já foi aprovado para ${auditoriaComparativaData.currentTributo.toUpperCase()}`, 'warning');
        return;
    }

    fetch('/api/auditoria-comparativa/aprovar-match', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            xml_item_id: xmlItemId,
            sped_item_id: spedItemId,
            empresa_id: parseInt(empresaId),
            tributo: auditoriaComparativaData.currentTributo,
            feedback: 'Match aprovado pelo usuário'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('Match aprovado com sucesso', 'success');

            // Atualizar status local para o tributo específico
            const auditoria = auditoriaComparativaData.auditorias.find(a =>
                a.xml_item_id === xmlItemId && a.sped_item_id === spedItemId
            );
            if (auditoria && auditoria.tributos && auditoria.tributos[auditoriaComparativaData.currentTributo]) {
                auditoria.tributos[auditoriaComparativaData.currentTributo].status = 'aprovado';
            }

            // Recarregar tabela
            renderAuditoriaComparativaTributo(auditoriaComparativaData.currentTributo);

            // Fechar modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('modal-detalhes-comparativo'));
            if (modal) modal.hide();
        } else {
            showMessage(data.message || 'Erro ao aprovar match', 'error');
        }
    })
    .catch(error => {
        console.error('Erro ao aprovar match:', error);
        showMessage('Erro ao aprovar match', 'error');
    });
}

/**
 * Rejeita um match sugerido
 */
function rejeitarMatch(xmlItemId, spedItemId) {
    const motivo = prompt('Informe o motivo da rejeição:');
    if (!motivo) return;

    const empresaId = localStorage.getItem('selectedCompany');

    fetch('/api/auditoria-comparativa/rejeitar-match', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            xml_item_id: xmlItemId,
            sped_item_id: spedItemId,
            empresa_id: parseInt(empresaId),
            tributo: auditoriaComparativaData.currentTributo,
            motivo: motivo
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('Match rejeitado com sucesso', 'success');

            // Atualizar status local para o tributo específico
            const auditoria = auditoriaComparativaData.auditorias.find(a =>
                a.xml_item_id === xmlItemId && a.sped_item_id === spedItemId
            );
            if (auditoria && auditoria.tributos && auditoria.tributos[auditoriaComparativaData.currentTributo]) {
                auditoria.tributos[auditoriaComparativaData.currentTributo].status = 'rejeitado';
            }

            // Recarregar tabela
            renderAuditoriaComparativaTributo(auditoriaComparativaData.currentTributo);

            // Fechar modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('modal-detalhes-comparativo'));
            if (modal) modal.hide();
        } else {
            showMessage(data.message || 'Erro ao rejeitar match', 'error');
        }
    })
    .catch(error => {
        console.error('Erro ao rejeitar match:', error);
        showMessage('Erro ao rejeitar match', 'error');
    });
}

/**
 * Configura filtros da tabela de auditoria comparativa
 */
async function setupTableFiltersAuditoriaComparativa() {
    try {
        const empresaId = localStorage.getItem('selectedCompany');
        const ano = document.getElementById('year-select')?.value || localStorage.getItem('selectedYear');
        const mes = document.getElementById('month-select')?.value || localStorage.getItem('selectedMonth');
        const tributo = auditoriaComparativaData.currentTributo;

        if (!empresaId || !ano || !mes) {
            console.warn('Parâmetros necessários não encontrados para carregar filtros');
            return;
        }

        // Carregar opções de filtros do backend
        await carregarOpcoesFiltrosAuditoriaComparativa(
            parseInt(empresaId),
            parseInt(mes),
            parseInt(ano),
            tributo
        );

        // Inicializar filtros dropdown
        await inicializarFiltrosDropdownAuditoriaComparativa();

        console.log('Filtros de auditoria comparativa configurados com sucesso');
    } catch (error) {
        console.error('Erro ao configurar filtros de auditoria comparativa:', error);
    }
}

// Função removida - agora usando sistema de filtros avançados

// Funções antigas de filtros removidas - agora usando sistema de filtros avançados



/**
 * Obtém valor da célula baseado na coluna
 */
function getCellValueByColumn(auditoria, column) {
    if (!auditoria) return '';

    switch (column) {
        case 'numero_nf': return auditoria.numero_nf || '';
        case 'data_emissao': return auditoria.data_emissao || '';
        case 'parceiro_nome': return auditoria.parceiro_nome || auditoria.cliente_nome || '';
        case 'sped_cfop': return auditoria.sped_cfop || '';
        case 'cst_sped':
            const tributo = auditoria.tributos[auditoriaComparativaData.currentTributo];
            return tributo?.cst || '';
        case 'xml_ncm': return auditoria.xml_ncm || '';
        case 'status_auditoria': return auditoria.status_auditoria || 'pendente';
        case 'aliquota_sped':
            const tributoSped = auditoria.tributos[auditoriaComparativaData.currentTributo];
            return tributoSped?.aliquota ? `${tributoSped.aliquota}%` : '';
        case 'aliquota_xml':
            const aliqXml = getXmlTributoValue(auditoria, auditoriaComparativaData.currentTributo, 'aliquota');
            return aliqXml ? `${aliqXml}%` : '';
        case 'reducao_sped':
            const tributoRed = auditoria.tributos[auditoriaComparativaData.currentTributo];
            return tributoRed?.reducao ? `${tributoRed.reducao}%` : '';
        case 'match_type': return auditoria.match_type || '';
        case 'sped_origem': return auditoria.sped_origem || '';
        default: return '';
    }
}

/**
 * Atualiza informações dos filtros ativos (compatibilidade)
 */
function updateFilterInfo() {
    // Usar a nova função de filtros avançados
    atualizarInfoFiltrosAtivosAuditoriaComparativa();
}



/**
 * Configura eventos de seleção
 */
function setupSelectionEvents() {
    // Limpar seleções anteriores
    auditoriaComparativaData.itensSelecionados.clear();
    updateSelectedCount();
}

/**
 * Toggle seleção de todos os matches visíveis
 */
function toggleSelectAllMatches(checkbox) {
    const visibleCheckboxes = document.querySelectorAll('#auditoria-comparativa-table tbody tr:not([style*="display: none"]) .match-checkbox');

    visibleCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });

    updateSelectedCount();
}

/**
 * Atualiza contador de itens selecionados
 */
function updateSelectedCount() {
    const checkedBoxes = document.querySelectorAll('.match-checkbox:checked');
    const count = checkedBoxes.length;

    // Atualizar contador no botão
    const countElement = document.getElementById('count-selecionados');
    if (countElement) {
        countElement.textContent = count;
    }

    // Habilitar/desabilitar botões de ação
    const btnAprovar = document.getElementById('btn-aprovar-selecionados');
    if (btnAprovar) {
        btnAprovar.disabled = count === 0;
    }

    const btnEditarMassa = document.getElementById('btn-editar-sped-massa');
    if (btnEditarMassa) {
        btnEditarMassa.disabled = count === 0;
    }

    // Atualizar checkbox "selecionar todos"
    const selectAllCheckbox = document.getElementById('select-all-matches');
    if (selectAllCheckbox) {
        const visibleCheckboxes = document.querySelectorAll('#auditoria-comparativa-table tbody tr:not([style*="display: none"]) .match-checkbox');
        const visibleChecked = document.querySelectorAll('#auditoria-comparativa-table tbody tr:not([style*="display: none"]) .match-checkbox:checked');

        if (visibleCheckboxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (visibleChecked.length === visibleCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else if (visibleChecked.length > 0) {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        } else {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        }
    }
}

/**
 * Altera quantidade de registros exibidos por página
 */
function changeAuditoriaComparativaPageLength(length) {
    auditoriaComparativaData.pageLength = length;
    if (auditoriaComparativaTable) {
        auditoriaComparativaTable.page.len(length).draw();
    }
}

/**
 * Aprova matches selecionados
 */
function aprovarSelecionados() {
    const checkedBoxes = document.querySelectorAll('.match-checkbox:checked');

    if (checkedBoxes.length === 0) {
        showMessage('Nenhum match selecionado', 'warning');
        return;
    }

    const matches = Array.from(checkedBoxes).map(checkbox => {
        const [xml_item_id, sped_item_id] = checkbox.value.split('-');
        return {
            xml_item_id: parseInt(xml_item_id),
            sped_item_id: parseInt(sped_item_id)
        };
    });

    const empresaId = localStorage.getItem('selectedCompany');

    if (!confirm(`Deseja aprovar ${matches.length} matches selecionados?`)) {
        return;
    }

    showLoading('Aprovando matches...');

    fetch('/api/auditoria-comparativa/aprovar-matches-massa', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            matches: matches,
            empresa_id: parseInt(empresaId),
            tributo: auditoriaComparativaData.currentTributo,
            feedback: 'Aprovação em massa via interface'
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage(`${data.aprovados} matches aprovados com sucesso`, 'success');

            // Recarregar dados da auditoria
            loadAuditoriaComparativaPeriodo(auditoriaComparativaData.currentTributo);

            // Limpar seleções
            auditoriaComparativaData.itensSelecionados.clear();
            updateSelectedCount();
        } else {
            showMessage(data.message || 'Erro ao aprovar matches', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao aprovar matches:', error);
        showMessage('Erro ao aprovar matches', 'error');
    });
}

/**
 * Abre modal de edição em massa de dados SPED
 */
function editarSpedMassa() {
    const checkedBoxes = document.querySelectorAll('.match-checkbox:checked');

    if (checkedBoxes.length === 0) {
        showMessage('Nenhum match selecionado', 'warning');
        return;
    }

    // Obter IDs das auditorias selecionadas
    const auditoriaIds = Array.from(checkedBoxes).map(checkbox => {
        const row = checkbox.closest('tr');
        return parseInt(row.dataset.auditoriaId);
    });

    const tributo = auditoriaComparativaData.currentTributo;

    // Criar modal de edição em massa
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'modal-editar-sped-massa';
    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        Edição em Massa - ${TRIBUTOS_CONFIG[tributo]?.label || tributo.toUpperCase()}
                        (${auditoriaIds.length} registros)
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Os campos preenchidos serão aplicados a todos os ${auditoriaIds.length} registros selecionados.
                        Deixe em branco os campos que não deseja alterar.
                    </div>

                    <form id="form-editar-sped-massa">
                        ${renderSpedEditFormHtml(tributo, {})}

                        <div class="row mt-3">
                            <div class="col-12">
                                <label class="form-label">Motivo da Alteração em Massa</label>
                                <textarea class="form-control" name="motivo_alteracao" rows="3"
                                    placeholder="Descreva o motivo da alteração em massa..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="salvarEdicaoSpedMassa([${auditoriaIds.join(',')}], '${tributo}')">
                        <i class="fas fa-save me-2"></i>Salvar Alterações em Massa
                    </button>
                </div>
            </div>
        </div>
    `;

    // Remover modal anterior se existir
    const existingModal = document.getElementById('modal-editar-sped-massa');
    if (existingModal) {
        existingModal.remove();
    }

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Remover modal do DOM quando fechado
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

/**
 * Salva edição em massa dos dados SPED
 */
function salvarEdicaoSpedMassa(auditoriaIds, tributo) {
    const form = document.getElementById('form-editar-sped-massa');
    if (!form) return;

    const formData = new FormData(form);
    const empresaId = localStorage.getItem('selectedCompany');

    // Construir objeto de dados - estrutura igual à edição individual
    const dadosSped = {
        auditoria_ids: auditoriaIds,
        empresa_id: parseInt(empresaId),
        motivo_alteracao: formData.get('motivo_alteracao') || ''
    };

    // Campos básicos do item (fora do tributo específico)
    const camposBasicos = ['cfop', 'ncm', 'origem_icms', 'tipo_item'];
    camposBasicos.forEach(campo => {
        const valor = formData.get(campo);
        if (valor && valor.trim() !== '') {
            dadosSped[campo] = valor.trim();
        }
    });

    // Adicionar dados específicos do tributo - estrutura aninhada
    dadosSped[tributo] = {};

    // Coletar campos tributários do formulário
    const inputs = form.querySelectorAll('input[name], select[name], textarea[name]');
    inputs.forEach(input => {
        if (input.name !== 'motivo_alteracao' && !camposBasicos.includes(input.name) && input.value && input.value.trim() !== '') {
            const valor = input.type === 'number' ? parseFloat(input.value) : input.value.trim();
            dadosSped[tributo][input.name] = valor;
        }
    });

    console.log('DEBUG MASSA - Dados coletados do formulário:', dadosSped);
    console.log('DEBUG MASSA - FormData completo:', Object.fromEntries(formData.entries()));
    console.log('DEBUG MASSA - Inputs encontrados:', Array.from(inputs).map(i => ({name: i.name, value: i.value, type: i.type})));
    console.log('DEBUG MASSA - Tributo atual:', tributo);
    console.log('DEBUG MASSA - Campos básicos encontrados:', camposBasicos.filter(campo => dadosSped[campo]));
    console.log('DEBUG MASSA - Campos do tributo encontrados:', Object.keys(dadosSped[tributo]));

    // Verificar se pelo menos um campo foi preenchido
    const temAlteracao = camposBasicos.some(campo => dadosSped[campo]) ||
                        Object.keys(dadosSped[tributo]).length > 0;

    console.log('DEBUG MASSA - Tem alteração?', temAlteracao);

    if (!temAlteracao) {
        showMessage('Preencha pelo menos um campo para alterar', 'warning');
        return;
    }

    showLoading('Salvando alterações em massa...');

    fetch('/api/auditoria-comparativa/editar-sped-massa', {
        method: 'PUT',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(dadosSped)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            // Fechar modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('modal-editar-sped-massa'));
            if (modal) modal.hide();

            // Recarregar tabela
            renderAuditoriaComparativaTributo(auditoriaComparativaData.currentTributo);

            // Limpar seleções
            auditoriaComparativaData.itensSelecionados.clear();
            updateSelectedCount();

            showMessage(`${data.total_sucessos} registros atualizados com sucesso`, 'success');

            if (data.total_erros > 0) {
                console.warn('Erros na edição em massa:', data.erros);
                showMessage(`${data.total_erros} registros com erro. Verifique o console.`, 'warning');
            }
        } else {
            showMessage(data.message || 'Erro ao salvar alterações em massa', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao salvar alterações em massa:', error);
        showMessage('Erro ao salvar alterações em massa', 'error');
    });
}

/**
 * Verifica se match foi aprovado para tributo específico
 */
function isMatchAprovadoParaTributo(auditoria, tributo) {
    if (!auditoria.tributos || !auditoria.tributos[tributo]) return false;
    return auditoria.tributos[tributo].status === 'aprovado';
}

/**
 * Obtém status do tributo específico
 */
function getStatusTributo(auditoria, tributo) {
    if (!auditoria.tributos || !auditoria.tributos[tributo]) return 'pendente';
    return auditoria.tributos[tributo].status || 'pendente';
}

/**
 * Obtém valor de tributo XML
 */
function getXmlTributoValue(auditoria, tributo, campo) {
    if (!auditoria.xml_data) return null;

    switch (tributo) {
        case 'icms':
            return auditoria.xml_data[`icms_${campo}`];
        case 'icms_st':
            return auditoria.xml_data[`icms_st_${campo}`];
        case 'ipi':
            return auditoria.xml_data[`ipi_${campo}`];
        case 'pis':
            return auditoria.xml_data[`pis_${campo}`];
        case 'cofins':
            return auditoria.xml_data[`cofins_${campo}`];
        default:
            return null;
    }
}

/**
 * Função debounce para otimizar filtros
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Configura WebSocket para auditoria comparativa
 */
function setupAuditoriaWebSocket() {
    try {
        // Verificar se Socket.IO está disponível
        if (typeof io === 'undefined') {
            console.warn('Socket.IO não está disponível para auditoria');
            return null;
        }

        window.auditoriaSocket = io({
            autoConnect: true,
            reconnection: true,
            reconnectionDelay: 1000,
            reconnectionAttempts: 10,
            timeout: 60000,  // 60 segundos para operações longas
            forceNew: true
        });

        window.auditoriaSocket.on('connect', () => {
            console.log('WebSocket de auditoria conectado');
        });

        window.auditoriaSocket.on('disconnect', () => {
            console.log('WebSocket de auditoria desconectado');
        });

        window.auditoriaSocket.on('audit_progress', (data) => {
            updateAuditoriaProgressBar(data);
        });

        window.auditoriaSocket.on('audit_complete', (data) => {
            handleAuditoriaComplete(data);
        });

        window.auditoriaSocket.on('audit_error', (data) => {
            handleAuditoriaError(data);
        });

        return window.auditoriaSocket;
    } catch (error) {
        console.error('Erro ao configurar WebSocket de auditoria:', error);
        return null;
    }
}

/**
 * Mostra barra de progresso da auditoria
 */
function showAuditoriaProgressBar(message, percentage) {
    const container = document.getElementById('auditoria-content');
    if (!container) return;

    const progressHtml = `
        <div id="auditoria-progress-container" class="card auditoria-progress-container">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Processando Auditoria Comparativa
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span id="auditoria-progress-message">${message}</span>
                        <span id="auditoria-progress-percentage">${percentage}%</span>
                    </div>
                    <div class="progress">
                        <div id="auditoria-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: ${percentage}%"
                             aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100">
                        </div>
                    </div>
                </div>
                <div id="auditoria-progress-details" class="auditoria-progress-details">
                    Aguardando início do processamento...
                </div>
                <div id="auditoria-progress-stats" class="auditoria-progress-stats" style="display: none;">
                    <div class="auditoria-stats-container">
                        <div class="auditoria-stat-item stat-notas">
                            <div class="auditoria-stat-value" id="stats-notas-processadas">0</div>
                            <div class="auditoria-stat-label">Notas Processadas</div>
                        </div>
                        <div class="auditoria-stat-item stat-matches">
                            <div class="auditoria-stat-value" id="stats-total-matches">0</div>
                            <div class="auditoria-stat-label">Matches Encontrados</div>
                        </div>
                        <div class="auditoria-stat-item stat-tempo">
                            <div class="auditoria-stat-value" id="stats-tempo-decorrido">00:00</div>
                            <div class="auditoria-stat-label">Tempo Decorrido</div>
                        </div>
                        <div class="auditoria-stat-item stat-atual">
                            <div class="auditoria-stat-value" id="stats-nota-atual">-</div>
                            <div class="auditoria-stat-label">Nota Atual</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = progressHtml;

    // Iniciar contador de tempo
    window.auditoriaStartTime = Date.now();
    window.auditoriaTimeInterval = setInterval(updateAuditoriaTimer, 1000);
}

/**
 * Atualiza barra de progresso da auditoria
 */
function updateAuditoriaProgressBar(data) {
    const progressBar = document.getElementById('auditoria-progress-bar');
    const progressMessage = document.getElementById('auditoria-progress-message');
    const progressPercentage = document.getElementById('auditoria-progress-percentage');
    const progressDetails = document.getElementById('auditoria-progress-details');
    const progressStats = document.getElementById('auditoria-progress-stats');

    if (progressBar) {
        progressBar.style.width = `${data.porcentagem}%`;
        progressBar.setAttribute('aria-valuenow', data.porcentagem);
    }

    if (progressMessage) {
        progressMessage.textContent = data.mensagem || 'Processando...';
    }

    if (progressPercentage) {
        progressPercentage.textContent = `${data.porcentagem}%`;
    }

    if (progressDetails) {
        let detailsText = `Etapa: ${data.etapa}`;
        if (data.progresso && data.total) {
            detailsText += ` | Progresso: ${data.progresso}/${data.total}`;
        }
        if (data.nota_atual) {
            detailsText += ` | Nota: ${data.nota_atual}`;
        }
        progressDetails.textContent = detailsText;
    }

    // Mostrar estatísticas quando começar o processamento
    if (progressStats && (data.status === 'processando' || data.status === 'iniciando')) {
        progressStats.style.display = 'block';

        // Atualizar notas processadas
        if (data.progresso !== undefined) {
            const totalText = data.total ? `${data.progresso}/${data.total}` : data.progresso;
            document.getElementById('stats-notas-processadas').textContent = totalText;
        }

        // Atualizar matches encontrados (se disponível)
        if (data.total_matches !== undefined) {
            document.getElementById('stats-total-matches').textContent = data.total_matches;
        }

        // Atualizar nota atual
        if (data.nota_atual) {
            document.getElementById('stats-nota-atual').textContent = data.nota_atual;
        }
    }
}

/**
 * Atualiza timer da auditoria
 */
function updateAuditoriaTimer() {
    if (!window.auditoriaStartTime) return;

    const elapsed = Math.floor((Date.now() - window.auditoriaStartTime) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    const timerElement = document.getElementById('stats-tempo-decorrido');
    if (timerElement) {
        timerElement.textContent = timeString;
    }
}

/**
 * Manipula conclusão da auditoria
 */
function handleAuditoriaComplete(data) {
    // Parar timer
    if (window.auditoriaTimeInterval) {
        clearInterval(window.auditoriaTimeInterval);
        window.auditoriaTimeInterval = null;
    }

    hideAuditoriaProgressBar();

    // Carregar dados do tributo atual
    carregarAuditoriaComparativaTributo(auditoriaComparativaData.currentTributo);

    showMessage(`Auditoria comparativa concluída com sucesso! ${data.total_registros} registros processados para ${data.total_notas_processadas} notas.`, 'success');

    // Mostrar estatísticas do matching
    if (data.matching_stats) {
        showMatchingStatsModal(data.matching_stats);
    }
}

/**
 * Manipula erro na auditoria
 */
function handleAuditoriaError(data) {
    // Parar timer
    if (window.auditoriaTimeInterval) {
        clearInterval(window.auditoriaTimeInterval);
        window.auditoriaTimeInterval = null;
    }

    hideAuditoriaProgressBar();
    showMessage(data.message || 'Erro durante o processamento da auditoria', 'error');
}

/**
 * Esconde barra de progresso da auditoria
 */
function hideAuditoriaProgressBar() {
    const container = document.getElementById('auditoria-progress-container');
    if (container) {
        container.remove();
    }
}

/**
 * Abre modal para matching manual de um item SPED
 */
function abrirModalMatchingManual(spedItemId) {
    const empresaId = localStorage.getItem('selectedCompany');
    const ano = document.getElementById('year-select')?.value || localStorage.getItem('selectedYear');
    const mes = document.getElementById('month-select')?.value || localStorage.getItem('selectedMonth');

    if (!empresaId || !ano || !mes) {
        showMessage('Selecione empresa, ano e mês para realizar matching manual', 'warning');
        return;
    }

    showLoading('Buscando candidatos para matching...');

    // Buscar candidatos XML para o item SPED
    fetch(`/api/matching-manual/candidatos?empresa_id=${empresaId}&sped_item_id=${spedItemId}&mes=${mes}&ano=${ano}`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            mostrarModalMatchingManual(data.sped_item, data.xml_candidatos);
        } else {
            showMessage(data.message || 'Erro ao buscar candidatos para matching', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao buscar candidatos:', error);
        showMessage('Erro ao buscar candidatos para matching', 'error');
    });
}

/**
 * Mostra modal de matching manual com drag-and-drop
 */
function mostrarModalMatchingManual(spedItem, xmlCandidatos) {
    // Criar modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'modal-matching-manual';
    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-link me-2"></i>
                        Matching Manual - NF ${spedItem.numero_nf || 'N/A'}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- Item SPED -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-file-alt me-2"></i>
                                        Item SPED (Destino)
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="sped-item-container p-3 border rounded bg-light"
                                         data-sped-id="${spedItem.id}"
                                         ondrop="drop(event)"
                                         ondragover="allowDrop(event)">
                                        <h6 class="text-primary">${spedItem.descricao}</h6>
                                        <div class="row">
                                            <div class="col-6">
                                                <small><strong>Código:</strong> ${spedItem.codigo || '-'}</small><br>
                                                <small><strong>NCM:</strong> ${spedItem.ncm || '-'}</small><br>
                                                <small><strong>Unidade:</strong> ${spedItem.unidade || '-'}</small>
                                            </div>
                                            <div class="col-6">
                                                <small><strong>Quantidade:</strong> ${spedItem.quantidade || 0}</small><br>
                                                <small><strong>Valor:</strong> R$ ${(spedItem.valor_item || 0).toFixed(2)}</small><br>
                                                <small><strong>CFOP:</strong> ${spedItem.cfop || '-'}</small>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <small><strong>Cliente:</strong> ${spedItem.cliente_nome || '-'}</small><br>
                                            <small><strong>NF:</strong> ${spedItem.numero_nf || '-'}</small>
                                        </div>

                                        <!-- Área para itens XML arrastados -->
                                        <div class="xml-items-dropped mt-3" id="xml-items-dropped">
                                            <div class="text-center text-muted p-3 border-dashed">
                                                <i class="fas fa-arrow-down fa-2x mb-2"></i>
                                                <p>Arraste os itens XML aqui para criar o matching</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Candidatos XML -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-file-code me-2"></i>
                                        Produtos XML da NF ${spedItem.numero_nf || 'N/A'} (${xmlCandidatos.length})
                                    </h6>
                                </div>
                                <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                                    ${xmlCandidatos.length === 0 ?
                                        '<div class="alert alert-info">Nenhum produto XML não pareado encontrado nesta nota fiscal.<br><small class="text-muted">Todos os produtos desta nota já foram pareados ou não existem produtos XML nesta NF.</small></div>' :
                                        xmlCandidatos.map(xml => `
                                            <div class="xml-candidate-item mb-2 p-2 border rounded ${xml.ja_pareado ? 'bg-warning-light' : 'bg-light'}"
                                                 draggable="true"
                                                 ondragstart="drag(event)"
                                                 data-xml-id="${xml.id}"
                                                 data-xml-data='${JSON.stringify(xml)}'>
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div class="flex-grow-1">
                                                        <h6 class="text-success mb-1">
                                                            ${xml.descricao}
                                                            ${xml.ja_pareado ? '<i class="fas fa-link ms-2 text-warning" title="Já pareado com outro SPED"></i>' : ''}
                                                        </h6>
                                                        <div class="row">
                                                            <div class="col-6">
                                                                <small><strong>Código:</strong> ${xml.codigo || '-'}</small><br>
                                                                <small><strong>NCM:</strong> ${xml.ncm || '-'}</small><br>
                                                                <small><strong>Unidade:</strong> ${xml.unidade || '-'}</small>
                                                            </div>
                                                            <div class="col-6">
                                                                <small><strong>Qtd:</strong> ${xml.quantidade || 0}</small><br>
                                                                <small><strong>Valor:</strong> R$ ${(xml.valor_total || 0).toFixed(2)}</small><br>
                                                                <small><strong>CFOP:</strong> ${xml.cfop || '-'}</small>
                                                            </div>
                                                        </div>
                                                        <small><strong>Cliente:</strong> ${xml.cliente_nome || '-'}</small><br>
                                                        <small><strong>NF:</strong> ${xml.numero_nf || '-'}</small>
                                                        ${xml.ja_pareado ? '<small class="text-warning"><strong>Status:</strong> Já pareado (pode ser reutilizado)</small>' : ''}
                                                    </div>
                                                    <div class="text-end">
                                                        ${xml.ja_pareado ? `
                                                            <span class="badge bg-warning text-dark">Já Pareado</span><br>
                                                        ` : ''}
                                                        ${xml.score_similaridade ? `
                                                            <span class="badge bg-info">${xml.score_similaridade}% similar</span><br>
                                                            <small class="text-muted">${xml.detalhes_similaridade ? xml.detalhes_similaridade.join(', ') : ''}</small>
                                                        ` : ''}
                                                    </div>
                                                </div>
                                            </div>
                                        `).join('')
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Justificativa -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="justificativa-matching">Justificativa do Matching (opcional):</label>
                                <textarea class="form-control" id="justificativa-matching" rows="3"
                                          placeholder="Explique por que estes itens devem ser pareados..."></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Nível de confiança -->
                    <div class="row mt-2">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="confianca-matching">Nível de Confiança:</label>
                                <select class="form-control" id="confianca-matching">
                                    <option value="1">1 - Baixa confiança</option>
                                    <option value="2">2 - Confiança limitada</option>
                                    <option value="3">3 - Confiança moderada</option>
                                    <option value="4">4 - Alta confiança</option>
                                    <option value="5" selected>5 - Confiança total</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="salvarMatchingManual()" id="btn-salvar-matching">
                        <i class="fas fa-save me-2"></i>Salvar Matching
                    </button>
                </div>
            </div>
        </div>
    `;

    // Remover modal anterior se existir
    const existingModal = document.getElementById('modal-matching-manual');
    if (existingModal) {
        existingModal.remove();
    }

    // Adicionar modal ao DOM
    document.body.appendChild(modal);

    // Mostrar modal
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // Armazenar dados para uso posterior
    window.matchingManualData = {
        spedItem: spedItem,
        xmlCandidatos: xmlCandidatos,
        xmlSelecionados: []
    };
}

/**
 * Funções de Drag and Drop para Matching Manual
 */
function allowDrop(ev) {
    ev.preventDefault();
}

function drag(ev) {
    ev.dataTransfer.setData("text", ev.target.getAttribute('data-xml-id'));
    ev.dataTransfer.setData("xml-data", ev.target.getAttribute('data-xml-data'));
}

function drop(ev) {
    ev.preventDefault();
    const xmlId = ev.dataTransfer.getData("text");
    const xmlDataStr = ev.dataTransfer.getData("xml-data");

    if (!xmlId || !xmlDataStr) return;

    try {
        const xmlData = JSON.parse(xmlDataStr);

        // Verificar se já foi adicionado
        if (window.matchingManualData.xmlSelecionados.find(x => x.id == xmlId)) {
            showMessage('Este item XML já foi adicionado', 'warning');
            return;
        }

        // Adicionar aos selecionados
        window.matchingManualData.xmlSelecionados.push(xmlData);

        // Atualizar interface
        atualizarXmlSelecionados();

        // Remover da lista de candidatos
        const candidateElement = document.querySelector(`[data-xml-id="${xmlId}"]`);
        if (candidateElement) {
            candidateElement.style.opacity = '0.5';
            candidateElement.style.pointerEvents = 'none';
            candidateElement.innerHTML += '<div class="badge bg-success position-absolute top-0 end-0">Selecionado</div>';
        }

    } catch (error) {
        console.error('Erro ao processar drop:', error);
        showMessage('Erro ao adicionar item', 'error');
    }
}

/**
 * Atualiza a área de XMLs selecionados
 */
function atualizarXmlSelecionados() {
    const container = document.getElementById('xml-items-dropped');

    if (window.matchingManualData.xmlSelecionados.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted p-3 border-dashed">
                <i class="fas fa-arrow-down fa-2x mb-2"></i>
                <p>Arraste os itens XML aqui para criar o matching</p>
            </div>
        `;
        return;
    }

    const html = window.matchingManualData.xmlSelecionados.map((xml, index) => `
        <div class="xml-selected-item mb-2 p-2 border rounded bg-success text-white">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h6 class="mb-1">${xml.descricao}</h6>
                    <div class="row">
                        <div class="col-6">
                            <small><strong>Código:</strong> ${xml.codigo || '-'}</small><br>
                            <small><strong>NCM:</strong> ${xml.ncm || '-'}</small>
                        </div>
                        <div class="col-6">
                            <small><strong>Qtd:</strong> ${xml.quantidade || 0}</small><br>
                            <small><strong>Valor:</strong> R$ ${(xml.valor_total || 0).toFixed(2)}</small>
                        </div>
                    </div>
                    <small><strong>NF:</strong> ${xml.numero_nf || '-'}</small>
                </div>
                <button class="btn btn-sm btn-outline-light" onclick="removerXmlSelecionado(${index})" title="Remover">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `).join('');

    container.innerHTML = html;

    // Habilitar/desabilitar botão de salvar
    const btnSalvar = document.getElementById('btn-salvar-matching');
    if (btnSalvar) {
        btnSalvar.disabled = window.matchingManualData.xmlSelecionados.length === 0;
    }
}

/**
 * Remove um XML selecionado
 */
function removerXmlSelecionado(index) {
    const xmlRemovido = window.matchingManualData.xmlSelecionados[index];
    window.matchingManualData.xmlSelecionados.splice(index, 1);

    // Atualizar interface
    atualizarXmlSelecionados();

    // Reabilitar na lista de candidatos
    const candidateElement = document.querySelector(`[data-xml-id="${xmlRemovido.id}"]`);
    if (candidateElement) {
        candidateElement.style.opacity = '1';
        candidateElement.style.pointerEvents = 'auto';
        const badge = candidateElement.querySelector('.badge.bg-success');
        if (badge) badge.remove();
    }
}

/**
 * Salva o matching manual
 */
function salvarMatchingManual() {
    if (!window.matchingManualData || window.matchingManualData.xmlSelecionados.length === 0) {
        showMessage('Selecione pelo menos um item XML para criar o matching', 'warning');
        return;
    }

    const empresaId = localStorage.getItem('selectedCompany');
    const justificativa = document.getElementById('justificativa-matching')?.value || '';
    const confianca = parseInt(document.getElementById('confianca-matching')?.value || 5);

    const xmlItemsIds = window.matchingManualData.xmlSelecionados.map(xml => xml.id);

    showLoading('Salvando matching manual...');

    fetch('/api/matching-manual/salvar', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            empresa_id: parseInt(empresaId),
            sped_item_id: window.matchingManualData.spedItem.id,
            xml_items_ids: xmlItemsIds,
            justificativa: justificativa,
            confianca: confianca
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage(`Matching manual salvo com sucesso! ${data.xml_items.length} itens pareados.`, 'success');

            // Fechar modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('modal-matching-manual'));
            if (modal) modal.hide();

            // Processar automaticamente o matching
            processarMatchingManual(data.match_temporario_id);

        } else {
            showMessage(data.message || 'Erro ao salvar matching manual', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao salvar matching:', error);
        showMessage('Erro ao salvar matching manual', 'error');
    });
}

/**
 * Processa um matching manual temporário
 */
function processarMatchingManual(matchTemporarioId) {
    const empresaId = localStorage.getItem('selectedCompany');

    showLoading('Processando matching manual...');

    fetch('/api/matching-manual/processar', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            empresa_id: parseInt(empresaId),
            match_temporario_id: matchTemporarioId
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage(`Matching processado com sucesso! ${data.itens_processados} itens pareados.`, 'success');

            // Recarregar a tabela de auditoria
            carregarAuditoriaComparativaTributo(auditoriaComparativaData.currentTributo);

        } else {
            showMessage(data.message || 'Erro ao processar matching manual', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao processar matching:', error);
        showMessage('Erro ao processar matching manual', 'error');
    });
}

/**
 * Renderiza os cards de análise fiscal baseado no tributo
 */
function renderAnalysisCards(auditorias, tributo) {
    const analysisResults = performFiscalAnalysis(auditorias, tributo);
    const config = window.FiscalAnalysisRules?.ANALYSIS_CARDS_CONFIG || {};

    // Filtrar cards baseado no tributo
    const applicableCards = Object.keys(config).filter(analysisType => {
        const cardConfig = config[analysisType];
        return cardConfig.tributos && cardConfig.tributos.includes(tributo);
    });

    if (applicableCards.length === 0) {
        return ''; // Nenhum card aplicável para este tributo
    }

    const cardsHtml = `
        <div class="row mb-4" id="analysis-cards">
            ${applicableCards.map(analysisType => {
                const cardConfig = config[analysisType];
                const result = analysisResults[`${analysisType}Analysis`] || { count: 0 };
                const hasProblems = result.count > 0;

                return `
                    <div class="col analysis-card-col mb-3">
                        <div class="card analysis-card ${hasProblems ? 'border-danger' : 'border-success'}"
                             data-analysis-type="${analysisType}"
                             onclick="filterByAnalysis('${analysisType}')">
                            <div class="card-body">
                                <div class="analysis-card-content">
                                    <div class="analysis-icon ${hasProblems ? 'text-danger' : 'text-success'}">
                                        <i class="${cardConfig.icon} fa-lg"></i>
                                    </div>
                                    <h5>${result.count}</h5>
                                    <p class="card-text mb-1">${cardConfig.title}</p>
                                    <small class="text-muted">${cardConfig.description}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('')}
        </div>
    `;

    // Adicionar botões de detalhes após renderização
    setTimeout(() => {
        if (window.FiscalAnalysisModals) {
            applicableCards.forEach(analysisType => {
                const cardElement = document.querySelector(`[data-analysis-type="${analysisType}"]`);
                if (cardElement) {
                    window.FiscalAnalysisModals.addDetailsButtonToAnalysisCard(cardElement, analysisType);
                }
            });
        }
    }, 100);

    return cardsHtml;
}

/**
 * Realiza análise fiscal dos dados
 */
function performFiscalAnalysis(auditorias, tributo = 'icms') {
    const results = {
        cfopAnalysis: { count: 0, items: [] },
        cfop_cstAnalysis: { count: 0, items: [] },
        product_typeAnalysis: { count: 0, items: [] },
        originAnalysis: { count: 0, items: [] },
        aliquotaAnalysis: { count: 0, items: [] }
    };

    // Verificar se as regras estão disponíveis
    if (!window.FiscalAnalysisRules) {
        console.warn('Regras de análise fiscal não carregadas');
        return results;
    }

    const rules = window.FiscalAnalysisRules;
    const config = rules.ANALYSIS_CARDS_CONFIG;

    auditorias.forEach(auditoria => {
        const spedCfop = auditoria.sped_cfop;
        const xmlCfop = auditoria.xml_cfop;
        const spedCst = auditoria.tributos?.icms?.cst;
        const xmlCst = auditoria.xml_cst || auditoria.xml_data?.cst;
        const tipoItem = auditoria.tipo_produto;
        const spedOrigem = auditoria.sped_origem;
        const xmlOrigem = auditoria.xml_origem || auditoria.xml_data?.origem;
        const regimeParceiro = auditoria.regime_parceiro;

        // Obter alíquotas baseado no tributo
        let spedAliquota, xmlAliquota;
        switch (tributo) {
            case 'icms':
                spedAliquota = auditoria.tributos?.icms?.aliquota;
                xmlAliquota = auditoria.xml_data?.icms_aliquota || auditoria.xml_icms_aliquota;
                break;
            case 'icms_st':
                spedAliquota = auditoria.tributos?.icms_st?.aliquota;
                xmlAliquota = auditoria.xml_data?.icms_st_aliquota || auditoria.xml_icms_st_aliquota;
                break;
            case 'ipi':
                spedAliquota = auditoria.tributos?.ipi?.aliquota;
                xmlAliquota = auditoria.xml_data?.ipi_aliquota || auditoria.xml_ipi_aliquota;
                break;
            case 'pis':
                spedAliquota = auditoria.tributos?.pis?.aliquota;
                xmlAliquota = auditoria.xml_data?.pis_aliquota || auditoria.xml_pis_aliquota;
                break;
            case 'cofins':
                spedAliquota = auditoria.tributos?.cofins?.aliquota;
                xmlAliquota = auditoria.xml_data?.cofins_aliquota || auditoria.xml_cofins_aliquota;
                break;
            case 'pis_cofins':
                // Para PIS/COFINS, verificar ambos
                const pisSpedAliq = auditoria.tributos?.pis?.aliquota;
                const pisXmlAliq = auditoria.xml_data?.pis_aliquota || auditoria.xml_pis_aliquota;
                const cofinsSpedAliq = auditoria.tributos?.cofins?.aliquota;
                const cofinsXmlAliq = auditoria.xml_data?.cofins_aliquota || auditoria.xml_cofins_aliquota;

                // Usar PIS como referência principal, mas verificar ambos
                spedAliquota = pisSpedAliq;
                xmlAliquota = pisXmlAliq;
                break;
        }

        // 1. Análise CFOP (apenas se aplicável ao tributo)
        if (config.cfop?.tributos.includes(tributo) && rules.isCfopInconsistent(spedCfop, xmlCfop)) {
            results.cfopAnalysis.count++;
            results.cfopAnalysis.items.push(auditoria.id);
        }

        // 2. Análise CFOP x CST (exceto Simples Nacional)
        if (config.cfop_cst?.tributos.includes(tributo) &&
            regimeParceiro !== 'Simples Nacional' && !auditoria.parceiro_simples_nacional) {
            if (rules.isCfopCstInconsistent(spedCfop, spedCst, xmlCst)) {
                results.cfop_cstAnalysis.count++;
                results.cfop_cstAnalysis.items.push(auditoria.id);
            }
        }

        // 3. Análise Tipo de Produto
        if (config.product_type?.tributos.includes(tributo) &&
            rules.isProductTypeInconsistent(spedCfop, spedCst, tipoItem)) {
            results.product_typeAnalysis.count++;
            results.product_typeAnalysis.items.push(auditoria.id);
        }

        // 4. Análise Origem (apenas se aplicável ao tributo)
        if (config.origin?.tributos.includes(tributo) &&
            rules.isOriginInconsistent(spedOrigem, xmlOrigem)) {
            results.originAnalysis.count++;
            results.originAnalysis.items.push(auditoria.id);
        }

        // 5. Análise Alíquota
        if (config.aliquota?.tributos.includes(tributo) &&
            rules.isAliquotaInconsistent(spedAliquota, xmlAliquota, tipoItem)) {
            results.aliquotaAnalysis.count++;
            results.aliquotaAnalysis.items.push(auditoria.id);
        }
    });

    return results;
}



/**
 * Filtra a tabela baseado na análise selecionada
 */
function filterByAnalysis(analysisType) {
    if (!auditoriaComparativaTable) return;

    // Limpar filtros existentes
    auditoriaComparativaTable.search('').columns().search('').draw();

    // Obter dados atuais
    const auditorias = auditoriaComparativaData.auditorias || [];
    const currentTributo = auditoriaComparativaData.currentTributo || 'icms';
    const analysisResults = performFiscalAnalysis(auditorias, currentTributo);

    let itemsToShow = [];
    let analysisName = '';

    switch (analysisType) {
        case 'cfop':
            itemsToShow = analysisResults.cfopAnalysis.items;
            analysisName = 'Análise CFOP';
            break;
        case 'cfop_cst':
            itemsToShow = analysisResults.cfop_cstAnalysis.items;
            analysisName = 'Análise CFOP x CST';
            break;
        case 'product_type':
            itemsToShow = analysisResults.product_typeAnalysis.items;
            analysisName = 'Análise Tipo de Produto';
            break;
        case 'origin':
            itemsToShow = analysisResults.originAnalysis.items;
            analysisName = 'Análise Origem';
            break;
        case 'aliquota':
            itemsToShow = analysisResults.aliquotaAnalysis.items;
            analysisName = 'Análise Alíquota';
            break;
    }

    if (itemsToShow.length === 0) {
        showMessage(`Nenhum problema encontrado na ${analysisName}`, 'success');
        return;
    }

    // Filtrar tabela para mostrar apenas os itens problemáticos
    auditoriaComparativaTable.rows().every(function() {
        const row = this.node();
        const auditoriaId = parseInt(row.getAttribute('data-auditoria-id'));

        if (itemsToShow.includes(auditoriaId)) {
            $(row).show();
        } else {
            $(row).hide();
        }
    });

    // Atualizar informações de filtro
    const filtroInfo = document.getElementById('filtros-ativos-info');
    if (filtroInfo) {
        filtroInfo.innerHTML = `<span class="badge bg-warning">${analysisName}: ${itemsToShow.length} registros</span>`;
    }

    // Destacar card ativo
    document.querySelectorAll('.analysis-card').forEach(card => {
        card.classList.remove('analysis-card-active');
    });

    const activeCard = document.querySelector(`[onclick="filterByAnalysis('${analysisType}')"]`);
    if (activeCard) {
        activeCard.classList.add('analysis-card-active');
    }

    showMessage(`Filtro aplicado: ${analysisName} - ${itemsToShow.length} registros com problemas`, 'info');
}

/**
 * Limpa o filtro de análise
 */
function clearAnalysisFilter() {
    if (!auditoriaComparativaTable) return;

    // Mostrar todas as linhas
    auditoriaComparativaTable.rows().every(function() {
        $(this.node()).show();
    });

    // Limpar informações de filtro
    const filtroInfo = document.getElementById('filtros-ativos-info');
    if (filtroInfo) {
        filtroInfo.innerHTML = '';
    }

    // Remover destaque dos cards
    document.querySelectorAll('.analysis-card').forEach(card => {
        card.classList.remove('analysis-card-active');
    });

    showMessage('Filtro de análise removido', 'info');
}

/**
 * Retorna a classe CSS para destacar valores SPED problemáticos
 */
function getSpedValueClass(auditoria, field) {
    // Verificar se as regras estão disponíveis
    if (!window.FiscalAnalysisRules) {
        return '';
    }

    const rules = window.FiscalAnalysisRules;
    const spedCfop = auditoria.sped_cfop;
    const xmlCfop = auditoria.xml_cfop;
    const spedCst = auditoria.tributos?.icms?.cst;
    const xmlCst = auditoria.xml_cst || auditoria.xml_data?.cst;
    const tipoItem = auditoria.tipo_produto;
    const spedOrigem = auditoria.sped_origem;
    const xmlOrigem = auditoria.xml_origem || auditoria.xml_data?.origem;
    const regimeParceiro = auditoria.regime_parceiro;

    let hasError = false;

    switch (field) {
        case 'cfop':
            // Verificar se CFOP tem problema
            hasError = rules.isCfopInconsistent(spedCfop, xmlCfop);
            break;

        case 'cst':
            // Verificar se CST tem problema (CFOP x CST ou tipo de produto)
            if (regimeParceiro !== 'Simples Nacional' && !auditoria.parceiro_simples_nacional) {
                hasError = rules.isCfopCstInconsistent(spedCfop, spedCst, xmlCst);
            }
            if (!hasError) {
                hasError = rules.isProductTypeInconsistent(spedCfop, spedCst, tipoItem);
            }
            break;

        case 'origem':
            // Verificar se origem tem problema
            hasError = rules.isOriginInconsistent(spedOrigem, xmlOrigem);
            break;

        case 'aliquota':
            // Verificar se alíquota tem problema
            const currentTributo = auditoriaComparativaData.currentTributo || 'icms';
            let spedAliquota, xmlAliquota;

            switch (currentTributo) {
                case 'icms':
                    spedAliquota = auditoria.tributos?.icms?.aliquota;
                    xmlAliquota = auditoria.xml_data?.icms_aliquota || auditoria.xml_icms_aliquota;
                    break;
                case 'icms_st':
                    spedAliquota = auditoria.tributos?.icms_st?.aliquota;
                    xmlAliquota = auditoria.xml_data?.icms_st_aliquota || auditoria.xml_icms_st_aliquota;
                    break;
                case 'ipi':
                    spedAliquota = auditoria.tributos?.ipi?.aliquota;
                    xmlAliquota = auditoria.xml_data?.ipi_aliquota || auditoria.xml_ipi_aliquota;
                    break;
                case 'pis':
                    spedAliquota = auditoria.tributos?.pis?.aliquota;
                    xmlAliquota = auditoria.xml_data?.pis_aliquota || auditoria.xml_pis_aliquota;
                    break;
                case 'cofins':
                    spedAliquota = auditoria.tributos?.cofins?.aliquota;
                    xmlAliquota = auditoria.xml_data?.cofins_aliquota || auditoria.xml_cofins_aliquota;
                    break;
                case 'pis_cofins':
                    // Para PIS/COFINS, usar PIS como referência
                    spedAliquota = auditoria.tributos?.pis?.aliquota;
                    xmlAliquota = auditoria.xml_data?.pis_aliquota || auditoria.xml_pis_aliquota;
                    break;
            }

            hasError = rules.isAliquotaInconsistent(spedAliquota, xmlAliquota, tipoItem);
            break;
    }

    return hasError ? 'text-danger fw-bold' : '';
}

// Inicializar quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    // Verificar se estamos na página de auditoria comparativa
    if (window.location.pathname.includes('/auditoria/entrada/auditoria')) {
        initAuditoriaComparativaIntegrada();

        // Configurar WebSocket
        setupAuditoriaWebSocket();
    }
});
