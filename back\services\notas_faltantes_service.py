"""
Serviço para identificação de notas faltantes por numeração e comparação XML vs SPED
"""
from typing import List, Dict, Set, Tuple
from models import db, ImportacaoXML, NotaEntrada, NotasFaltantes, Empresa
from sqlalchemy import func, and_, or_
from datetime import datetime
import re


class NotasFaltantesService:
    """
    Serviço para identificação de notas faltantes
    """

    def __init__(self, empresa_id: int, escritorio_id: int):
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id

    def identificar_notas_faltantes_completo(self, mes: int, ano: int, tipo_nota: str = '0') -> Dict:
        """
        Identifica notas faltantes de forma completa:
        1. Notas que estão no XML mas não no SPED (ou vice-versa)
        2. Pulos na numeração das notas
        
        Args:
            mes (int): Mês de referência
            ano (int): Ano de referência  
            tipo_nota (str): '0' para entrada, '1' para saída
            
        Returns:
            Dict: Resultado da identificação
        """
        try:
            # 1. Identificar notas faltantes XML vs SPED (lógica atual)
            faltantes_xml_sped = self._identificar_faltantes_xml_sped(mes, ano, tipo_nota)
            
            # 2. Identificar pulos na numeração
            pulos_numeracao = self._identificar_pulos_numeracao(mes, ano, tipo_nota)
            
            # 3. Consolidar resultados
            total_faltantes = len(faltantes_xml_sped) + len(pulos_numeracao)
            
            return {
                'success': True,
                'total_faltantes': total_faltantes,
                'faltantes_xml_sped': faltantes_xml_sped,
                'pulos_numeracao': pulos_numeracao,
                'resumo': {
                    'xml_vs_sped': len(faltantes_xml_sped),
                    'pulos_numeracao': len(pulos_numeracao)
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Erro ao identificar notas faltantes: {str(e)}',
                'total_faltantes': 0
            }

    def _identificar_faltantes_xml_sped(self, mes: int, ano: int, tipo_nota: str) -> List[Dict]:
        """
        Identifica notas que estão no XML mas não no SPED ou vice-versa
        REGRA: Apenas para notas de entrada (tipo_nota = '0')
        Para notas de saída, retorna lista vazia pois não há SPED de saída
        """
        faltantes = []

        # Para notas de saída, não há SPED, então não há comparação XML vs SPED
        if tipo_nota == '1':
            return faltantes

        try:
            # Buscar XMLs do período
            xmls = db.session.query(ImportacaoXML).filter(
                ImportacaoXML.empresa_id == self.empresa_id,
                ImportacaoXML.tipo_nota == tipo_nota,
                func.extract('month', ImportacaoXML.data_entrada) == mes,
                func.extract('year', ImportacaoXML.data_entrada) == ano,
                ImportacaoXML.status_validacao != 'cancelado'
            ).all()

            # Buscar notas SPED do período
            notas_sped = db.session.query(NotaEntrada).filter(
                NotaEntrada.empresa_id == self.empresa_id,
                NotaEntrada.ind_oper == tipo_nota,
                func.extract('month', NotaEntrada.data_entrada_saida) == mes,
                func.extract('year', NotaEntrada.data_entrada_saida) == ano
            ).all()

            # Criar sets de chaves para comparação
            chaves_xml = {xml.chave_nf for xml in xmls if xml.chave_nf}
            chaves_sped = {nota.chave_nf for nota in notas_sped if nota.chave_nf}

            # Identificar faltantes
            faltantes_sped = chaves_xml - chaves_sped  # No XML mas não no SPED
            faltantes_xml = chaves_sped - chaves_xml   # No SPED mas não no XML

            # Processar XMLs sem SPED
            for chave in faltantes_sped:
                xml = next((x for x in xmls if x.chave_nf == chave), None)
                if xml:
                    faltantes.append({
                        'tipo': 'xml_sem_sped',
                        'chave_nf': chave,
                        'numero_nf': xml.numero_nf,
                        'data_emissao': xml.data_emissao.isoformat() if xml.data_emissao else None,
                        'data_entrada': xml.data_entrada.isoformat() if xml.data_entrada else None,
                        'origem': 'XML',
                        'observacao': 'Nota presente no XML mas ausente no SPED'
                    })

            # Processar SPEDs sem XML
            for chave in faltantes_xml:
                nota = next((n for n in notas_sped if n.chave_nf == chave), None)
                if nota:
                    faltantes.append({
                        'tipo': 'sped_sem_xml',
                        'chave_nf': chave,
                        'numero_nf': nota.numero_nf,
                        'data_emissao': nota.data_documento.isoformat() if nota.data_documento else None,
                        'data_entrada': nota.data_entrada_saida.isoformat() if nota.data_entrada_saida else None,
                        'origem': 'SPED',
                        'observacao': 'Nota presente no SPED mas ausente no XML'
                    })

        except Exception as e:
            print(f"Erro ao identificar faltantes XML vs SPED: {str(e)}")

        return faltantes

    def _identificar_pulos_numeracao(self, mes: int, ano: int, tipo_nota: str) -> List[Dict]:
        """
        Identifica pulos na numeração das notas
        REGRA: Apenas para notas de saída (tipo_nota = '1')
        Para notas de entrada, retorna lista vazia pois numeração é aleatória
        """
        pulos = []

        # Para notas de entrada, não calcular pulos pois numeração é aleatória
        if tipo_nota == '0':
            return pulos

        try:
            # Buscar apenas notas XML de saída (não usar SPED para saída)
            numeros_xml = self._obter_numeros_notas_xml(mes, ano, tipo_nota)

            # Remover duplicatas e ordenar
            todos_numeros = sorted(set(numeros_xml))

            if len(todos_numeros) < 2:
                return pulos

            # Verificar se é a primeira importação da empresa
            # Se o menor número for muito alto e não há histórico anterior, não considerar como pulos
            if self._is_primeira_importacao_empresa(todos_numeros[0], tipo_nota):
                # Para primeira importação, só considerar pulos dentro do range importado
                pulos_encontrados = self._encontrar_pulos_sequencia(todos_numeros)
            else:
                # Para importações subsequentes, considerar todos os pulos
                pulos_encontrados = self._encontrar_pulos_sequencia(todos_numeros)

            # Converter para formato de retorno
            for inicio, fim in pulos_encontrados:
                if inicio == fim:
                    pulos.append({
                        'tipo': 'pulo_numeracao',
                        'numero_faltante': inicio,
                        'observacao': f'Nota {inicio} faltante na sequência'
                    })
                else:
                    pulos.append({
                        'tipo': 'pulo_numeracao',
                        'numeros_faltantes': list(range(inicio, fim + 1)),
                        'observacao': f'Notas {inicio} a {fim} faltantes na sequência'
                    })

        except Exception as e:
            print(f"Erro ao identificar pulos de numeração: {str(e)}")

        return pulos

    def _is_primeira_importacao_empresa(self, menor_numero: int, tipo_nota: str) -> bool:
        """
        Verifica se é a primeira importação da empresa baseado no histórico
        Se não há notas anteriores com números menores, considera primeira importação
        """
        try:
            # Buscar se há notas anteriores com números menores
            count = db.session.query(ImportacaoXML).filter(
                ImportacaoXML.empresa_id == self.empresa_id,
                ImportacaoXML.tipo_nota == tipo_nota,
                ImportacaoXML.numero_nf.isnot(None),
                func.cast(func.regexp_replace(ImportacaoXML.numero_nf, r'\D', '', 'g'), db.Integer) < menor_numero
            ).count()

            return count == 0

        except Exception as e:
            print(f"Erro ao verificar primeira importação: {str(e)}")
            # Em caso de erro, assumir que não é primeira importação (mais conservador)
            return False

    def _obter_numeros_notas_xml(self, mes: int, ano: int, tipo_nota: str) -> List[int]:
        """
        Obtém números das notas XML do período
        """
        xmls = db.session.query(ImportacaoXML.numero_nf).filter(
            ImportacaoXML.empresa_id == self.empresa_id,
            ImportacaoXML.tipo_nota == tipo_nota,
            func.extract('month', ImportacaoXML.data_entrada) == mes,
            func.extract('year', ImportacaoXML.data_entrada) == ano,
            ImportacaoXML.status_validacao != 'cancelado',
            ImportacaoXML.numero_nf.isnot(None)
        ).all()
        
        numeros = []
        for xml in xmls:
            try:
                # Extrair apenas números da string
                numero_limpo = re.sub(r'\D', '', str(xml.numero_nf))
                if numero_limpo:
                    numeros.append(int(numero_limpo))
            except (ValueError, TypeError):
                continue
                
        return numeros

    def _obter_numeros_notas_sped(self, mes: int, ano: int, tipo_nota: str) -> List[int]:
        """
        Obtém números das notas SPED do período
        """
        notas = db.session.query(NotaEntrada.numero_nf).filter(
            NotaEntrada.empresa_id == self.empresa_id,
            NotaEntrada.ind_oper == tipo_nota,
            func.extract('month', NotaEntrada.data_entrada_saida) == mes,
            func.extract('year', NotaEntrada.data_entrada_saida) == ano,
            NotaEntrada.numero_nf.isnot(None)
        ).all()
        
        numeros = []
        for nota in notas:
            try:
                # Extrair apenas números da string
                numero_limpo = re.sub(r'\D', '', str(nota.numero_nf))
                if numero_limpo:
                    numeros.append(int(numero_limpo))
            except (ValueError, TypeError):
                continue
                
        return numeros

    def _encontrar_pulos_sequencia(self, numeros_ordenados: List[int]) -> List[Tuple[int, int]]:
        """
        Encontra pulos em uma sequência de números
        
        Args:
            numeros_ordenados: Lista de números ordenados
            
        Returns:
            Lista de tuplas (inicio, fim) dos pulos encontrados
        """
        pulos = []
        
        for i in range(len(numeros_ordenados) - 1):
            atual = numeros_ordenados[i]
            proximo = numeros_ordenados[i + 1]
            
            # Se há um pulo (diferença > 1)
            if proximo - atual > 1:
                inicio_pulo = atual + 1
                fim_pulo = proximo - 1
                pulos.append((inicio_pulo, fim_pulo))
                
        return pulos

    def marcar_nota_como_encontrada(self, nota_faltante_id: int) -> Dict:
        """
        Marca uma nota faltante como encontrada
        """
        try:
            nota_faltante = db.session.get(NotasFaltantes, nota_faltante_id)
            if not nota_faltante:
                return {'success': False, 'message': 'Nota faltante não encontrada'}
                
            nota_faltante.status = 'encontrado'
            nota_faltante.data_resolucao = datetime.now()
            db.session.commit()
            
            return {'success': True, 'message': 'Nota marcada como encontrada'}
            
        except Exception as e:
            db.session.rollback()
            return {'success': False, 'message': f'Erro ao marcar nota: {str(e)}'}

    def excluir_nota_faltante_completa(self, nota_faltante_id: int) -> Dict:
        """
        Exclui uma nota faltante completamente do sistema
        """
        try:
            nota_faltante = db.session.get(NotasFaltantes, nota_faltante_id)
            if not nota_faltante:
                return {'success': False, 'message': 'Nota faltante não encontrada'}
                
            chave_nf = nota_faltante.chave_nf
            
            # Excluir XML relacionado se existir
            xml_relacionado = db.session.query(ImportacaoXML).filter(
                ImportacaoXML.empresa_id == nota_faltante.empresa_id,
                ImportacaoXML.chave_nf == chave_nf
            ).first()
            
            if xml_relacionado:
                db.session.delete(xml_relacionado)
                
            # Excluir nota SPED relacionada se existir
            nota_sped = db.session.query(NotaEntrada).filter(
                NotaEntrada.empresa_id == nota_faltante.empresa_id,
                NotaEntrada.chave_nf == chave_nf
            ).first()
            
            if nota_sped:
                db.session.delete(nota_sped)
                
            # Excluir a nota faltante
            db.session.delete(nota_faltante)
            db.session.commit()
            
            return {'success': True, 'message': 'Nota excluída completamente do sistema'}
            
        except Exception as e:
            db.session.rollback()
            return {'success': False, 'message': f'Erro ao excluir nota: {str(e)}'}
