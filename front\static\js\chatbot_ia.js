/**
 * Chatbot IA - Sistema de Auditoria Fiscal
 * Interface inteligente para consultas sobre auditorias
 */

class ChatbotIA {
  constructor() {
    this.isOpen = false;
    this.isTyping = false;
    this.currentConversationId = null;
    this.suggestions = [];
    this.registroEmpresaEmAndamento = false;
    this.proximoCampo = null;
    this.dadosEmpresa = {};

    this.init();
  }

  init() {
    this.createChatbotHTML();
    this.bindEvents();
    this.loadSuggestions();
    this.checkStatus();
  }

  createChatbotHTML() {
    const chatbotHTML = `
            <div class="chatbot-container">
                <!-- Bo<PERSON><PERSON> flutuante -->
                <button class="chatbot-toggle" id="chatbotToggle">
                    <i class="fas fa-robot"></i>
                </button>

                <!-- <PERSON><PERSON> do chatbot -->
                <div class="chatbot-window" id="chatbotWindow">
                    <!-- Header -->
                    <div class="chatbot-header">
                        <button class="chatbot-close" id="chatbotClose">
                            <i class="fas fa-times"></i>
                        </button>
                        <h3>Assistente IA</h3>
                        <div class="subtitle">Pergunte sobre suas auditorias</div>
                    </div>

                    <!-- Mensagens -->
                    <div class="chatbot-messages" id="chatbotMessages">
                        <div class="message bot">
                            <div class="message-content">
                                Olá! 👋 Sou seu assistente de auditoria fiscal.

                                Posso ajudar com: notas fiscais, cenários tributários, relatórios e estatísticas.

                                Como posso ajudar você hoje?
                            </div>
                        </div>
                    </div>

                    <!-- Indicador de digitação -->
                    <div class="typing-indicator" id="typingIndicator">
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>

                    <!-- Área de input -->
                    <div class="chatbot-input-area">
                        <div class="chatbot-input-container">
                            <textarea
                                class="chatbot-input"
                                id="chatbotInput"
                                placeholder="Digite sua pergunta..."
                                rows="1"
                            ></textarea>
                            <button class="chatbot-send" id="chatbotSend">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>

                        <!-- Sugestões -->
                        <div class="chatbot-suggestions" id="chatbotSuggestions"></div>
                    </div>

                    <!-- Status -->
                    <div class="chatbot-status" id="chatbotStatus">
                        <span class="status-indicator">🟢 Online</span>
                    </div>
                </div>
            </div>
        `;

    document.body.insertAdjacentHTML('beforeend', chatbotHTML);
  }

  bindEvents() {
    const toggle = document.getElementById('chatbotToggle');
    const close = document.getElementById('chatbotClose');
    const input = document.getElementById('chatbotInput');
    const send = document.getElementById('chatbotSend');

    toggle.addEventListener('click', () => this.toggleChatbot());
    close.addEventListener('click', () => this.closeChatbot());
    send.addEventListener('click', () => this.sendMessage());

    input.addEventListener('keypress', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.sendMessage();
      }
    });

    input.addEventListener('input', () => this.autoResize(input));
  }

  toggleChatbot() {
    const window = document.getElementById('chatbotWindow');
    const toggle = document.getElementById('chatbotToggle');

    if (this.isOpen) {
      this.closeChatbot();
    } else {
      window.style.display = 'flex';
      toggle.classList.add('active');
      this.isOpen = true;

      // Focar no input
      setTimeout(() => {
        document.getElementById('chatbotInput').focus();
      }, 300);
    }
  }

  closeChatbot() {
    const window = document.getElementById('chatbotWindow');
    const toggle = document.getElementById('chatbotToggle');

    window.style.display = 'none';
    toggle.classList.remove('active');
    this.isOpen = false;
  }

  async sendMessage() {
    const input = document.getElementById('chatbotInput');
    const message = input.value.trim();

    if (!message || this.isTyping) return;

    // Adicionar mensagem do usuário
    this.addMessage(message, 'user');
    input.value = '';
    this.autoResize(input);

    // Mostrar indicador de digitação
    this.showTyping();


    try {
      // Verificar se está no meio de um registro de empresa
      if (this.registroEmpresaEmAndamento || this.proximoCampo) {
        const response = await this.processarRegistroEmpresa(message);
        if (response) {
          this.addMessage(response.resposta, 'bot', response);
          
          // Atualizar estado do registro de empresa
          if (response.dados) {
            if (response.dados.registro_concluido || response.erro) {
              // Se o registro foi concluído ou houve erro, limpa o estado
              this.registroEmpresaEmAndamento = false;
              this.proximoCampo = null;
              this.dadosEmpresa = {};
            } else if (response.dados.proximo_campo) {
              // Se houver próximo campo, continua o registro
              this.registroEmpresaEmAndamento = true;
              this.proximoCampo = response.dados.proximo_campo;
              this.dadosEmpresa = { ...this.dadosEmpresa, ...(response.dados.dados_empresa || {}) };
            }
          }
          
          return;
        }
      }

      // Verificar se é uma solicitação para cadastrar empresa
      if (message.toLowerCase().includes('cadastrar empresa') || 
          message.toLowerCase().includes('nova empresa') ||
          message.toLowerCase().includes('registrar empresa')) {
        
        const response = await this.processarRegistroEmpresa(message);
        if (response) {
          this.addMessage(response.resposta, 'bot', response);
          
          // Se a resposta contém dados de registro, atualiza o estado
          if (response.dados) {
            this.registroEmpresaEmAndamento = !!response.dados.proximo_campo;
            this.proximoCampo = response.dados.proximo_campo || null;
            this.dadosEmpresa = { ...(response.dados.dados_empresa || {}) };
          }
          
          return;
        }
      }

      // Enviar para a API
      const response = await this.callAPI('/chatbot/pergunta', {
        pergunta: message,
        empresa_id: this.getCurrentEmpresaId(),
      });

      if (response.success) {
        this.addMessage(response.resposta, 'bot', response);
        this.currentConversationId = response.conversa_id;
        
        // Verificar se é o início de um registro de empresa
        if (response.dados && response.dados.proximo_campo) {
          this.registroEmpresaEmAndamento = true;
          this.proximoCampo = response.dados.proximo_campo;
          this.dadosEmpresa = { ...(response.dados.dados_empresa || {}) };
        }
      } else {
        this.addMessage(
          response.resposta || 'Desculpe, não consegui processar sua pergunta.',
          'bot',
          response
        );
      }
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      this.addMessage(
        'Ops! Algo deu errado. Tente novamente em alguns instantes.',
        'bot',
      );
    } finally {
      this.hideTyping();
    }
  }

  addMessage(content, type, data = null) {
    const messagesContainer = document.getElementById('chatbotMessages');

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;

    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.textContent = content;

    messageDiv.appendChild(contentDiv);

    // Adicionar avaliação para mensagens do bot
    if (type === 'bot' && data && data.conversa_id) {
      const ratingDiv = this.createRatingElement(data.conversa_id);
      messageDiv.appendChild(ratingDiv);
    }

    messagesContainer.appendChild(messageDiv);
    this.scrollToBottom();
  }

  createRatingElement(conversaId) {
    const ratingDiv = document.createElement('div');
    ratingDiv.className = 'message-rating';
    ratingDiv.innerHTML = `
            <span style="font-size: 12px; margin-right: 8px;">Útil?</span>
            ${[1, 2, 3, 4, 5]
              .map(
                (i) =>
                  `<span class="rating-star" data-rating="${i}" data-conversa="${conversaId}">⭐</span>`,
              )
              .join('')}
        `;

    // Bind eventos de avaliação
    ratingDiv.querySelectorAll('.rating-star').forEach((star) => {
      star.addEventListener('click', (e) => {
        const rating = parseInt(e.target.dataset.rating);
        const conversaId = e.target.dataset.conversa;
        this.rateMessage(conversaId, rating, ratingDiv);
      });
    });

    return ratingDiv;
  }

  async rateMessage(conversaId, rating, ratingElement) {
    try {
      await this.callAPI('/chatbot/avaliar', {
        conversa_id: conversaId,
        avaliacao: rating,
      });

      // Atualizar visual
      const stars = ratingElement.querySelectorAll('.rating-star');
      stars.forEach((star, index) => {
        star.classList.toggle('active', index < rating);
      });

      // Mostrar agradecimento
      setTimeout(() => {
        ratingElement.innerHTML =
          '<span style="font-size: 12px; color: #28a745;">Obrigado pelo feedback!</span>';
      }, 1000);
    } catch (error) {
      console.error('Erro ao avaliar mensagem:', error);
    }
  }

  showTyping() {
    this.isTyping = true;
    const indicator = document.getElementById('typingIndicator');
    const send = document.getElementById('chatbotSend');

    indicator.style.display = 'block';
    send.disabled = true;
    this.scrollToBottom();
  }

  hideTyping() {
    this.isTyping = false;
    const indicator = document.getElementById('typingIndicator');
    const send = document.getElementById('chatbotSend');

    indicator.style.display = 'none';
    send.disabled = false;
  }

  scrollToBottom() {
    const messages = document.getElementById('chatbotMessages');
    setTimeout(() => {
      messages.scrollTop = messages.scrollHeight;
    }, 100);
  }

  autoResize(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
  }

  async loadSuggestions() {
    try {
      const empresaId = this.getCurrentEmpresaId();
      const url = empresaId
        ? `/chatbot/sugestoes?empresa_id=${empresaId}`
        : '/chatbot/sugestoes';

      const response = await this.callAPI(url);
      if (response.success) {
        this.suggestions = response.sugestoes;
        this.renderSuggestions();
      }
    } catch (error) {
      console.error('Erro ao carregar sugestões:', error);
      // Fallback para sugestões padrão
      this.suggestions = [
        'Qual o resumo geral das auditorias?',
        'Quantas inconsistências foram encontradas?',
        'Quais são os produtos mais vendidos?',
        'Como está a conformidade fiscal?',
      ];
      this.renderSuggestions();
    }
  }

  renderSuggestions() {
    const container = document.getElementById('chatbotSuggestions');

    if (!this.suggestions || this.suggestions.length === 0) {
      container.innerHTML = '';
      return;
    }

    // Mostrar apenas 4 sugestões (sem embaralhar para manter contexto)
    const suggestionsToShow = this.suggestions.slice(0, 4);

    const suggestionsHTML = suggestionsToShow
      .map((suggestion) => {
        // Escapar aspas para evitar problemas no onclick
        const escapedSuggestion = suggestion.replace(/'/g, "\\'");
        // Truncar sugestões muito longas
        const displayText =
          suggestion.length > 45
            ? suggestion.substring(0, 42) + '...'
            : suggestion;

        return `<div class="suggestion-chip"
                     onclick="window.chatbot.useSuggestion('${escapedSuggestion}')"
                     title="${suggestion}">
                  ${displayText}
                </div>`;
      })
      .join('');

    container.innerHTML = `
      <div class="suggestions-header">💡 Sugestões:</div>
      <div class="suggestions-grid">${suggestionsHTML}</div>
    `;
  }

  useSuggestion(suggestion) {
    const input = document.getElementById('chatbotInput');
    input.value = suggestion;
    input.focus();
    this.autoResize(input);
  }

  async checkStatus() {
    try {
      const response = await this.callAPI('/chatbot/status');
      const statusElement = document.getElementById('chatbotStatus');

      if (response.success) {
        statusElement.innerHTML =
          '<span class="status-online">🟢 Online</span>';
      } else {
        statusElement.innerHTML =
          '<span class="status-offline">🔴 Offline</span>';
      }
    } catch (error) {
      const statusElement = document.getElementById('chatbotStatus');
      statusElement.innerHTML =
        '<span class="status-offline">🔴 Offline</span>';
    }
  }

  async processarRegistroEmpresa(mensagem) {
    try {
      // Se não houver próximo campo definido, inicia um novo registro
      if (!this.proximoCampo) {
        return await this.iniciarRegistroEmpresa();
      }

      // Atualizar o valor do campo atual
      if (mensagem.toLowerCase() === 'pular' && this.dadosEmpresa[this.proximoCampo]?.opcional) {
        // Se o usuário digitou 'pular' e o campo é opcional, define como null
        this.dadosEmpresa[this.proximoCampo] = null;
      } else {
        // Caso contrário, armazena o valor fornecido
        this.dadosEmpresa[this.proximoCampo] = mensagem;
      }

      // Enviar os dados para o servidor
      const response = await this.callAPI('/chatbot/pergunta', {
        pergunta: mensagem,
        empresa_id: this.getCurrentEmpresaId(),
        registro_empresa: true,
        proximo_campo: this.proximoCampo,
        dados_empresa: this.dadosEmpresa
      });
      
      if (response.success) {
        // Atualizar estado com base na resposta
        if (response.dados) {
          // Se houver um próximo campo, atualiza o estado
          if (response.dados.proximo_campo) {
            this.proximoCampo = response.dados.proximo_campo;
            this.dadosEmpresa = { ...this.dadosEmpresa, ...(response.dados.dados_empresa || {}) };
          } else if (response.dados.registro_concluido) {
            // Se o registro foi concluído, limpa o estado
            this.registroEmpresaEmAndamento = false;
            this.proximoCampo = null;
            this.dadosEmpresa = {};
          }
        }
        
        return response;
      } else if (response.erro) {
        // Se houver um erro na resposta, retorna a mensagem de erro
        return response;
      }
      
      // Caso de erro genérico
      return {
        resposta: 'Ocorreu um erro ao processar os dados da empresa. Por favor, tente novamente.',
        erro: true
      };
    } catch (error) {
      console.error('Erro ao processar registro de empresa:', error);
      return {
        resposta: 'Ocorreu um erro ao processar o registro da empresa. Tente novamente mais tarde.',
        erro: true,
        dados: {
          erro: true,
          mensagem: error.message
        }
      };
    }
  }
  
  async iniciarRegistroEmpresa() {
    try {
      const response = await this.callAPI('/chatbot/pergunta', {
        pergunta: 'cadastrar empresa',
        empresa_id: this.getCurrentEmpresaId()
      });
      
      if (response.success) {
        this.registroEmpresaEmAndamento = true;
        this.proximoCampo = response.dados?.proximo_campo || 'cnpj';
        this.dadosEmpresa = response.dados?.dados_empresa || {};
        return response;
      }
      
      return {
        resposta: response.resposta || 'Não foi possível iniciar o cadastro da empresa. Tente novamente.',
        erro: true
      };
    } catch (error) {
      console.error('Erro ao iniciar registro de empresa:', error);
      return {
        resposta: 'Ocorreu um erro ao iniciar o cadastro da empresa. Tente novamente mais tarde.',
        erro: true
      };
    }
  }

  getCurrentEmpresaId() {
    // Tentar obter empresa_id do contexto atual
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('empresa_id') || null;
  }

  async callAPI(endpoint, data = null) {
    const token = localStorage.getItem('token');

    const options = {
      method: data ? 'POST' : 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(endpoint, options);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    return await response.json();
  }
}

// Inicializar chatbot quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
  // Verificar se o usuário está logado
  const token = localStorage.getItem('token');
  if (token) {
    window.chatbot = new ChatbotIA();
  }
});
