# Correção do Bug do CRT e Implementação Completa do Simples Nacional

## Problema Identificado

O CRT do emitente estava vindo como `None` porque quando o sistema detectava que a empresa era o destinatário (fallback), ele trocava os dados do emitente e destinatário, mas **perdia o CRT original** do emitente.

### Exemplo do Problema:
```
DEBUG - Nota de entrada detectada. CRT do emitente: None
```

Mas o XML tinha:
```xml
<emit>
    <CNPJ>50140453000191</CNPJ>
    <xNome>RVN METALURGICA INDUSTRIA E COMERCIO LTDA</xNome>
    <CRT>1</CRT>  <!-- CRT estava presente! -->
</emit>
```

## Correções Implementadas

### 1. **XMLImportService** (Importação Individual)
```python
# ANTES (linha 90-92):
emitente_original = emitente.copy()
emitente = destinatario.copy()
destinatario = emitente_original

# DEPOIS (linha 90-95):
emitente_original = emitente.copy()
crt_original = emitente_original.get('crt')  # Preservar CRT
emitente = destinatario.copy()
emitente['crt'] = crt_original  # Restaurar CRT no novo emitente
destinatario = emitente_original
```

### 2. **OptimizedXMLImportService** (Importação em Lote)
```python
# ANTES (linha 565-568):
emitente_original = emitente.copy()
emitente_final = destinatario.copy()
destinatario_final = emitente_original

# DEPOIS (linha 566-571):
emitente_original = emitente.copy()
crt_original = emitente_original.get('crt')  # Preservar CRT
emitente_final = destinatario.copy()
emitente_final['crt'] = crt_original  # Restaurar CRT no novo emitente
destinatario_final = emitente_original
```

### 3. **Processamento Completo do Simples Nacional**

#### **XMLProcessor** (já estava implementado):
- ✅ Extração do CRT do emitente
- ✅ Suporte para tags ICMSSN (101, 102, 103, 201, 202, 203, 300, 400, 500, 900)
- ✅ Extração de CSOSN, pCredSN, vCredICMSSN

#### **XMLImportService** (já estava implementado):
- ✅ Detecção de notas de entrada com CRT
- ✅ Atualização do cliente com status Simples Nacional
- ✅ Salvamento dos dados do Simples Nacional nos tributos

#### **OptimizedXMLImportService** (ADICIONADO):
- ✅ Detecção de notas de entrada com CRT
- ✅ Atualização do cliente com status Simples Nacional
- ✅ Salvamento dos dados do Simples Nacional nos tributos
- ✅ Dados informativos na importação XML

## Fluxo Corrigido

### Para Notas de Entrada com Fallback:

1. **Sistema detecta** que empresa é destinatário
2. **Preserva CRT original** do emitente antes da troca
3. **Troca emitente/destinatário** para processamento
4. **Restaura CRT** no novo emitente
5. **Detecta Simples Nacional** baseado no CRT
6. **Atualiza cliente** com `simples_nacional = true/false`
7. **Salva dados ICMS** do Simples Nacional nos tributos

### Resultado Esperado:
```
DEBUG - Nota de entrada detectada. CRT do emitente: 1
DEBUG - Cliente RVN METALURGICA atualizado: Simples Nacional = true (CRT: 1)
```

## Dados Salvos

### **Cliente**:
- `simples_nacional = true` (se CRT = '1')

### **Tributo**:
- `icms_csosn` = '101', '102', etc.
- `icms_p_cred_sn` = percentual de crédito
- `icms_v_cred_sn` = valor do crédito

### **ImportacaoXML**:
- `crt_emitente` = '1'
- `csosn` = CSOSN do primeiro produto (informativo)

## Migração Necessária

Execute o script SQL para adicionar as novas colunas:

```sql
-- Adicionar campos à tabela importacao_xml
ALTER TABLE importacao_xml 
ADD COLUMN IF NOT EXISTS csosn VARCHAR(3),
ADD COLUMN IF NOT EXISTS crt_emitente VARCHAR(1);

-- Adicionar campos à tabela tributo
ALTER TABLE tributo 
ADD COLUMN IF NOT EXISTS icms_csosn VARCHAR(3),
ADD COLUMN IF NOT EXISTS icms_p_cred_sn NUMERIC(10, 4),
ADD COLUMN IF NOT EXISTS icms_v_cred_sn NUMERIC(10, 2);
```

## Arquivos Modificados

1. **back/services/xml_import_service.py** - Correção do CRT + Simples Nacional
2. **back/services/optimized_xml_import_service.py** - Correção do CRT + Simples Nacional
3. **back/models/importacao_xml.py** - Novos campos
4. **back/models/tributo.py** - Novos campos
5. **back/utils/xml_processor.py** - Extração do CRT e dados SN

## Teste

Agora ao importar o XML com:
```xml
<emit>
    <CRT>1</CRT>
</emit>
<imposto>
    <ICMS>
        <ICMSSN101>
            <orig>0</orig>
            <CSOSN>101</CSOSN>
            <pCredSN>10.5400</pCredSN>
            <vCredICMSSN>18.97</vCredICMSSN>
        </ICMSSN101>
    </ICMS>
</imposto>
```

O sistema deve:
1. ✅ Detectar CRT = '1' corretamente
2. ✅ Marcar cliente como Simples Nacional
3. ✅ Salvar CSOSN = '101', pCredSN = 10.54%, vCredICMSSN = 18.97
4. ✅ Funcionar tanto para importação individual quanto em lote

## Status

- ✅ **Bug do CRT corrigido** em ambos os serviços
- ✅ **Simples Nacional implementado** completamente
- ⏳ **Migração do banco** pendente
- ⏳ **Teste com XML real** pendente

A correção resolve o problema identificado e implementa completamente o suporte ao Simples Nacional conforme solicitado.
