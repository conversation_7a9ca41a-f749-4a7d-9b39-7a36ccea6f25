/**
 * tributo_functions.js - Auditoria Fiscal
 * Funções para gerenciar operações de tributos
 */

/**
 * Salva as alterações feitas em um tributo
 * @param {number} tributoId - ID do tributo
 */
function saveTributoChanges(tributoId) {
  console.log('Salvando alterações do tributo ID:', tributoId);

  // Obter o tipo de tributo atual
  const tipoTributo = window.cenariosDetalhes.currentTipoTributo;
  if (!tipoTributo) {
    alert('Tipo de tributo não definido.');
    return;
  }

  // Coletar dados do formulário com base no tipo de tributo
  const tributoData = {
    tipo_tributo: tipoTributo,
  };

  // Adicionar dados específicos do tipo de tributo
  switch (tipoTributo) {
    case 'icms':
      tributoData.icms_origem = document.getElementById('icms-origem')?.value;
      tributoData.icms_cst = document.getElementById('icms-cst')?.value;
      tributoData.icms_mod_bc = document.getElementById('icms-mod-bc')?.value;
      tributoData.icms_p_red_bc =
        parseFloat(document.getElementById('icms-p-red-bc')?.value) || null;
      tributoData.icms_vbc =
        parseFloat(document.getElementById('icms-vbc')?.value) || null;
      tributoData.icms_aliquota =
        parseFloat(document.getElementById('icms-aliquota')?.value) || null;
      tributoData.icms_valor =
        parseFloat(document.getElementById('icms-valor')?.value) || null;
      tributoData.icms_v_op =
        parseFloat(document.getElementById('icms-v-op')?.value) || null;
      tributoData.icms_p_dif =
        parseFloat(document.getElementById('icms-p-dif')?.value) || null;
      tributoData.icms_v_dif =
        parseFloat(document.getElementById('icms-v-dif')?.value) || null;
      tributoData.status = document.getElementById('icms-status')?.value;
      break;
    case 'icms_st':
      tributoData.icms_st_mod_bc =
        document.getElementById('icms-st-mod-bc')?.value;
      tributoData.icms_st_p_mva =
        parseFloat(document.getElementById('icms-st-p-mva')?.value) || null;
      tributoData.icms_st_vbc =
        parseFloat(document.getElementById('icms-st-vbc')?.value) || null;
      tributoData.icms_st_aliquota =
        parseFloat(document.getElementById('icms-st-aliquota')?.value) || null;
      tributoData.icms_st_valor =
        parseFloat(document.getElementById('icms-st-valor')?.value) || null;
      tributoData.status = document.getElementById('icms-st-status')?.value;
      // Também incluir dados de ICMS normal
      tributoData.icms_origem = document.getElementById('icms-origem')?.value;
      tributoData.icms_cst = document.getElementById('icms-cst')?.value;
      tributoData.icms_aliquota =
        parseFloat(document.getElementById('icms-aliquota')?.value) || null;
      tributoData.icms_p_red_bc =
        parseFloat(document.getElementById('icms-p-red-bc')?.value) || null;
      break;
    case 'ipi':
      tributoData.ipi_cst = document.getElementById('ipi-cst')?.value;
      tributoData.ipi_vbc =
        parseFloat(document.getElementById('ipi-vbc')?.value) || null;
      tributoData.ipi_aliquota =
        parseFloat(document.getElementById('ipi-aliquota')?.value) || null;
      tributoData.ipi_valor =
        parseFloat(document.getElementById('ipi-valor')?.value) || null;
      tributoData.ipi_codigo_enquadramento = document.getElementById(
        'ipi-codigo-enquadramento',
      )?.value;
      tributoData.status = document.getElementById('ipi-status')?.value;
      break;
    case 'pis':
      tributoData.pis_cst = document.getElementById('pis-cst')?.value;
      tributoData.pis_vbc =
        parseFloat(document.getElementById('pis-vbc')?.value) || null;
      tributoData.pis_aliquota =
        parseFloat(document.getElementById('pis-aliquota')?.value) || null;
      tributoData.pis_valor =
        parseFloat(document.getElementById('pis-valor')?.value) || null;
      tributoData.status = document.getElementById('pis-status')?.value;
      break;
    case 'cofins':
      tributoData.cofins_cst = document.getElementById('cofins-cst')?.value;
      tributoData.cofins_vbc =
        parseFloat(document.getElementById('cofins-vbc')?.value) || null;
      tributoData.cofins_aliquota =
        parseFloat(document.getElementById('cofins-aliquota')?.value) || null;
      tributoData.cofins_valor =
        parseFloat(document.getElementById('cofins-valor')?.value) || null;
      tributoData.status = document.getElementById('cofins-status')?.value;
      break;
    case 'difal':
      tributoData.difal_vbc =
        parseFloat(document.getElementById('difal-vbc')?.value) || null;
      tributoData.difal_p_fcp_uf_dest =
        parseFloat(document.getElementById('difal-p-fcp-uf-dest')?.value) ||
        null;
      tributoData.difal_p_icms_uf_dest =
        parseFloat(document.getElementById('difal-p-icms-uf-dest')?.value) ||
        null;
      tributoData.difal_p_icms_inter =
        parseFloat(document.getElementById('difal-p-icms-inter')?.value) ||
        null;
      tributoData.difal_p_icms_inter_part =
        parseFloat(document.getElementById('difal-p-icms-inter-part')?.value) ||
        null;
      tributoData.difal_v_fcp_uf_dest =
        parseFloat(document.getElementById('difal-v-fcp-uf-dest')?.value) ||
        null;
      tributoData.difal_v_icms_uf_dest =
        parseFloat(document.getElementById('difal-v-icms-uf-dest')?.value) ||
        null;
      tributoData.difal_v_icms_uf_remet =
        parseFloat(document.getElementById('difal-v-icms-uf-remet')?.value) ||
        null;
      tributoData.status = document.getElementById('difal-status')?.value;
      break;
    default:
      alert('Tipo de tributo não suportado.');
      return;
  }

  // Coletar dados do cliente
  const clienteData = {
    razao_social: document.getElementById('cliente-razao-social')?.value,
    cnpj: document.getElementById('cliente-cnpj')?.value,
    inscricao_estadual: document.getElementById('cliente-inscricao-estadual')
      ?.value,
    uf: document.getElementById('cliente-uf')?.value,
    municipio: document.getElementById('cliente-municipio')?.value,
    cnae: document.getElementById('cliente-cnae')?.value,
    atividade: document.getElementById('cliente-atividade')?.value,
    destinacao: document.getElementById('cliente-destinacao')?.value,
    simples_nacional: document.getElementById('cliente-simples-nacional')
      ?.checked,
  };

  // Coletar dados do produto
  const produtoData = {
    codigo: document.getElementById('produto-codigo')?.value,
    descricao: document.getElementById('produto-descricao')?.value,
    ncm: document.getElementById('produto-ncm')?.value,
    ex: document.getElementById('produto-ex')?.value,
    cest: document.getElementById('produto-cest')?.value,
    cfop: document.getElementById('produto-cfop')?.value,
    unidade_comercial: document.getElementById('produto-unidade-comercial')
      ?.value,
    codigo_ean: document.getElementById('produto-codigo-ean')?.value,
    codigo_ean_tributavel: document.getElementById(
      'produto-codigo-ean-tributavel',
    )?.value,
  };

  // Adicionar dados do cliente e produto ao objeto de dados
  tributoData.cliente = clienteData;
  tributoData.produto = produtoData;

  console.log('Dados do tributo a serem enviados:', tributoData);

  // Enviar os dados para a API
  fetch(`/fiscal/api/tributos/${tributoId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    body: JSON.stringify(tributoData),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.message) {
        alert(data.message);

        // Fechar o modal
        const modalElement = document.getElementById('tributo-modal');
        if (modalElement) {
          const modalInstance = bootstrap.Modal.getInstance(modalElement);
          if (modalInstance) {
            modalInstance.hide();
          }
        }

        // Recarregar os dados para todas as abas
        loadTributoData('novo');
        loadTributoData('producao');
        loadTributoData('inconsistente');
      } else {
        alert('Erro ao atualizar tributo.');
      }
    })
    .catch((error) => {
      console.error('Erro ao atualizar tributo:', error);
      alert('Erro ao atualizar tributo.');
    });
}

/**
 * Carrega o histórico de alterações de um tributo
 * @param {number} tributoId - ID do tributo
 */
function loadTributoHistorico(tributoId) {
  const container = document.getElementById('historico-content-data');
  const loadingIndicator = document.getElementById('historico-loading');

  if (!container || !loadingIndicator) return;

  // Mostrar indicador de carregamento
  loadingIndicator.style.display = 'block';
  container.innerHTML = '';

  // Fazer requisição para obter o histórico
  fetch(`/fiscal/api/tributos/${tributoId}/historico`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      // Esconder indicador de carregamento
      loadingIndicator.style.display = 'none';

      if (data.historico && data.historico.length > 0) {
        // Usar a função do arquivo tributo_historico.js
        renderHistoricoTable(container, data.historico);
      } else {
        container.innerHTML =
          '<div class="alert alert-info">Nenhum histórico encontrado para este tributo.</div>';
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar histórico:', error);
      loadingIndicator.style.display = 'none';
      container.innerHTML = `<div class="alert alert-danger">Erro ao carregar histórico: ${error.message}</div>`;
    });
}

/**
 * Retorna a classe CSS para o badge de status
 * @param {string} status - Status do tributo
 * @returns {string} Classe CSS
 */
function getStatusBadgeClass(status) {
  switch (status) {
    case 'novo':
      return 'bg-primary';
    case 'producao':
      return 'bg-success';
    case 'inconsistente':
      return 'bg-warning';
    case 'conforme':
      return 'bg-info';
    default:
      return 'bg-secondary';
  }
}
