#!/usr/bin/env python3
"""
Script para executar a migração de precisão decimal
Corrige campos DECIMAL(5,4) que causam estouro numérico
"""

import sys
import os

# Adicionar o diretório back ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'back'))

from sqlalchemy import text
from models import db
from app import create_app

def run_migration():
    """Executa a migração de precisão decimal"""
    app = create_app()
    
    with app.app_context():
        try:
            print("Iniciando migração de precisão decimal...")
            
            # 1. Corrigir campos na tabela auditoria_comparativa_impostos
            print("1. Corrigindo auditoria_comparativa_impostos...")
            db.session.execute(text(
                "ALTER TABLE auditoria_comparativa_impostos "
                "ALTER COLUMN sped_icms_credito TYPE DECIMAL(6,4)"
            ))
            
            # 2. Corrigir campos na tabela item_nota_entrada
            print("2. Corrigindo item_nota_entrada...")
            db.session.execute(text(
                "ALTER TABLE item_nota_entrada "
                "ALTER COLUMN p_red_icms TYPE DECIMAL(6,4), "
                "ALTER COLUMN p_red_icms_st TYPE DECIMAL(6,4), "
                "ALTER COLUMN p_mva_icms_st TYPE DECIMAL(6,4), "
                "ALTER COLUMN p_red_ipi TYPE DECIMAL(6,4), "
                "ALTER COLUMN p_red_cofins TYPE DECIMAL(6,4)"
            ))
            
            # 3. Corrigir campos na tabela historico_auditoria_entrada (se existir)
            print("3. Verificando historico_auditoria_entrada...")
            result = db.session.execute(text(
                "SELECT 1 FROM information_schema.tables "
                "WHERE table_name = 'historico_auditoria_entrada'"
            )).fetchone()
            
            if result:
                print("   Corrigindo historico_auditoria_entrada...")
                db.session.execute(text(
                    "ALTER TABLE historico_auditoria_entrada "
                    "ALTER COLUMN pis_aliquota_padrao TYPE DECIMAL(6,4), "
                    "ALTER COLUMN cofins_aliquota_padrao TYPE DECIMAL(6,4)"
                ))
            else:
                print("   Tabela historico_auditoria_entrada não existe, pulando...")
            
            # 4. Corrigir campos na tabela historico_matching_aprendizado
            print("4. Corrigindo historico_matching_aprendizado...")
            db.session.execute(text(
                "ALTER TABLE historico_matching_aprendizado "
                "ALTER COLUMN confidence_score_original TYPE DECIMAL(6,4)"
            ))
            
            # 5. Adicionar comentários atualizados
            print("5. Adicionando comentários...")
            comments = [
                "COMMENT ON COLUMN auditoria_comparativa_impostos.sped_icms_credito IS 'Percentual de crédito ICMS Simples Nacional (0.0000 a 99.9999)'",
                "COMMENT ON COLUMN item_nota_entrada.p_red_icms IS 'Percentual de redução ICMS (0.0000 a 99.9999)'",
                "COMMENT ON COLUMN item_nota_entrada.p_red_icms_st IS 'Percentual de redução ICMS-ST (0.0000 a 99.9999)'",
                "COMMENT ON COLUMN item_nota_entrada.p_mva_icms_st IS 'Percentual MVA ICMS-ST (0.0000 a 99.9999)'",
                "COMMENT ON COLUMN item_nota_entrada.p_red_ipi IS 'Percentual de redução IPI (0.0000 a 99.9999)'",
                "COMMENT ON COLUMN item_nota_entrada.p_red_cofins IS 'Percentual de redução COFINS (0.0000 a 99.9999)'",
                "COMMENT ON COLUMN historico_matching_aprendizado.confidence_score_original IS 'Score de confiança original (0.0000 a 99.9999)'"
            ]
            
            for comment in comments:
                try:
                    db.session.execute(text(comment))
                except Exception as e:
                    print(f"   Aviso: {e}")
            
            # Commit das alterações
            db.session.commit()
            print("\n✅ Migração concluída com sucesso!")
            print("Todos os campos DECIMAL(5,4) foram atualizados para DECIMAL(6,4)")
            
        except Exception as e:
            print(f"\n❌ Erro durante a migração: {e}")
            db.session.rollback()
            return False
    
    return True

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)
