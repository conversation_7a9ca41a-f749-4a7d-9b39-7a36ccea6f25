# Sistema de Auditoria Fiscal - Documentação Detalhada

## Visão Geral

O sistema de auditoria fiscal é responsável por comparar os valores de tributos calculados automaticamente com base em cenários de produção versus os valores informados nas notas fiscais importadas. O processo envolve múltiplos tipos de tributos (ICMS, ICMS-ST, DIFAL, IPI, PIS e COFINS) e segue uma ordem específica de cálculo devido às dependências entre eles.

## Arquitetura do Sistema

### Componentes Principais

1. **AuditoriaService** (`back/services/auditoria_service.py`)

   - Orquestra todo o processo de auditoria
   - Gerencia a ordem de execução dos cálculos
   - Atualiza as tabelas de resultado e sumário

2. **TributoCalculationService** (`back/services/tributo_calculation_service.py`)

   - Contém as fórmulas específicas de cálculo para cada tributo
   - Implementa as regras tributárias brasileiras

3. **Modelos de Cenários** (`back/models/cenario.py`)
   - Armazenam as configurações tributárias por empresa/cliente/produto

## Ordem de Execução da Auditoria

A auditoria segue uma ordem específica devido às dependências entre os tributos:

```
1. IPI (primeiro, pois afeta outros tributos)
2. ICMS (depende do IPI em alguns casos)
3. ICMS-ST (depende do ICMS e IPI)
4. PIS (depende do ICMS)
5. COFINS (depende do ICMS)
6. DIFAL (independente)
```

## Processo de Auditoria por Tributo

### 1. IPI (Imposto sobre Produtos Industrializados)

#### Busca do Cenário

- Busca cenário vigente na tabela `cenario_ipi`
- Filtros: empresa_id, cliente_id, produto_id, status='producao', ativo=True
- Verifica se a data de emissão está dentro do período de vigência

#### Cálculo

```python
# Condições para tributação
if cenario_ipi.cst == '50':  # Tributado
    base_calculo = valor_total_mercadoria
    valor_ipi = (base_calculo * aliquota) / 100
else:
    valor_ipi = 0.00
```

#### Atualização na Tabela Tributo

```sql
UPDATE tributo SET
    cenario_ipi_id = cenario.id,
    cenario_ipi_vbc = base_calculo,
    cenario_ipi_valor = valor_ipi
WHERE id = tributo_id;
```

### 2. ICMS (Imposto sobre Circulação de Mercadorias e Serviços)

#### Busca do Cenário

- Busca cenário vigente na tabela `cenario_icms`
- Mesmo processo de filtros do IPI

#### Cálculo

```python
# CSTs tributados: 00, 10, 20, 70
if cenario_icms.cst in ['00', '10', '20', '70']:
    base_calculo = valor_total_mercadoria

    # Adiciona IPI se cliente for uso/consumo ou ativo imobilizado
    if cliente_uso_consumo_ativo and valor_ipi:
        base_calculo += valor_ipi

    # Aplica redução da base de cálculo se houver
    if cenario_icms.p_red_bc > 0:
        base_calculo -= (base_calculo * p_red_bc / 100)

    valor_icms = (base_calculo * aliquota) / 100
else:
    valor_icms = 0.00
```

#### Atualização na Tabela Tributo

```sql
UPDATE tributo SET
    cenario_icms_id = cenario.id,
    cenario_icms_vbc = base_calculo,
    cenario_icms_valor = valor_icms
WHERE id = tributo_id;
```

### 3. ICMS-ST (Substituição Tributária)

#### Busca do Cenário

- Busca cenário vigente na tabela `cenario_icms_st`

#### Cálculo

```python
# CSTs com ST: 10, 70
if cenario_icms_st.cst in ['10', '70']:
    base_calculo = valor_total_mercadoria

    # Adiciona IPI se cliente for uso/consumo ou ativo imobilizado
    if cliente_uso_consumo_ativo and valor_ipi:
        base_calculo += valor_ipi

    # Aplica MVA (Margem de Valor Agregado)
    if icms_st_p_mva > 0:
        base_calculo = base_calculo * (1 + icms_st_p_mva / 100)

    # Aplica redução da BC se CST != 10
    if cst != '10' and p_red_bc > 0:
        base_calculo -= (base_calculo * p_red_bc / 100)

    valor_icms_st_total = (base_calculo * icms_st_aliquota) / 100
    valor_icms_st = valor_icms_st_total - valor_icms_proprio
else:
    valor_icms_st = 0.00
```

#### Atualização na Tabela Tributo

```sql
UPDATE tributo SET
    cenario_icms_st_id = cenario.id,
    cenario_icms_st_vbc = base_calculo,
    cenario_icms_st_valor = valor_icms_st
WHERE id = tributo_id;
```

### 4. PIS (Programa de Integração Social)

#### Busca do Cenário

- Busca cenário vigente na tabela `cenario_pis`

#### Cálculo

```python
# CSTs tributados: 01, 02, 1, 2
if cenario_pis.cst in ['01', '02', '1', '2']:
    base_calculo = valor_total_mercadoria

    # Subtrai ICMS da base de cálculo
    if valor_icms:
        base_calculo -= valor_icms

    # Aplica redução da base de cálculo se houver
    if p_red_bc > 0:
        base_calculo = base_calculo * (1 - p_red_bc / 100)

    valor_pis = (base_calculo * aliquota) / 100
else:
    valor_pis = 0.00
```

#### Atualização na Tabela Tributo

```sql
UPDATE tributo SET
    cenario_pis_id = cenario.id,
    cenario_pis_vbc = base_calculo,
    cenario_pis_valor = valor_pis
WHERE id = tributo_id;
```

### 5. COFINS (Contribuição para Financiamento da Seguridade Social)

#### Busca do Cenário

- Busca cenário vigente na tabela `cenario_cofins`

#### Cálculo

```python
# Mesmo cálculo do PIS, mas com alíquota diferente
# CSTs tributados: 01, 02, 1, 2
if cenario_cofins.cst in ['01', '02', '1', '2']:
    base_calculo = valor_total_mercadoria

    # Subtrai ICMS da base de cálculo
    if valor_icms:
        base_calculo -= valor_icms

    # Aplica redução da base de cálculo se houver
    if p_red_bc > 0:
        base_calculo = base_calculo * (1 - p_red_bc / 100)

    valor_cofins = (base_calculo * aliquota) / 100
else:
    valor_cofins = 0.00
```

#### Atualização na Tabela Tributo

```sql
UPDATE tributo SET
    cenario_cofins_id = cenario.id,
    cenario_cofins_vbc = base_calculo,
    cenario_cofins_valor = valor_cofins
WHERE id = tributo_id;
```

### 6. DIFAL (Diferencial de Alíquota)

#### Busca do Cenário

- Busca cenário vigente na tabela `cenario_difal`

#### Cálculo

```python
# Implementação parcial - em desenvolvimento
# TODO: Implementar cálculo completo do DIFAL
valor_difal = 0.00
```

#### Atualização na Tabela Tributo

```sql
UPDATE tributo SET
    cenario_difal_id = cenario.id,
    cenario_difal_valor = valor_difal
WHERE id = tributo_id;
```

## Alimentação da Tabela auditoria_resultado

Após cada cálculo de tributo, o sistema compara o valor calculado com o valor da nota fiscal:

### Processo de Comparação

```python
if tributo.nota_fiscal_item_id:  # Se vinculado a uma nota fiscal
    valor_nota = tributo.{tributo}_valor  # Valor da nota
    valor_calculado = valor_cenario       # Valor calculado

    # Tolerância de R$ 0,01 para arredondamento
    if abs(valor_nota - valor_calculado) <= 0.01:
        status = 'conforme'
    else:
        status = 'inconsistente'
```

### Inserção no auditoria_resultado

```sql
INSERT INTO auditoria_resultado (
    empresa_id,
    escritorio_id,
    tributo_id,
    nota_fiscal_item_id,
    cenario_id,
    tipo_tributo,
    valor_nota,
    valor_calculado,
    base_calculo_nota,
    base_calculo_calculada,
    status,
    data_auditoria
) VALUES (...);
```

## Alimentação da Tabela auditoria_sumario

O sumário é atualizado após cada auditoria de tributo:

### Estrutura do Sumário

```sql
CREATE TABLE auditoria_sumario (
    empresa_id INTEGER,
    escritorio_id INTEGER,
    ano INTEGER,
    mes INTEGER,
    tipo_tributo VARCHAR(20),  -- 'icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal'
    total_notas INTEGER,
    total_produtos INTEGER,
    valor_total_notas DECIMAL(15,2),
    valor_total_cenarios DECIMAL(15,2),
    valor_total_tributo DECIMAL(15,2),
    total_conforme INTEGER,
    total_inconsistente INTEGER,
    valor_inconsistente_maior DECIMAL(15,2),
    valor_inconsistente_menor DECIMAL(15,2)
);
```

### Processo de Atualização

```python
def _atualizar_sumario_auditoria(tributo, tipo_tributo, status):
    # Busca ou cria sumário para empresa/ano/mês/tipo_tributo
    sumario = buscar_ou_criar_sumario(
        empresa_id=tributo.empresa_id,
        ano=tributo.data_emissao.year,
        mes=tributo.data_emissao.month,
        tipo_tributo=tipo_tributo
    )

    # Atualiza contadores
    if status == 'conforme':
        sumario.total_conforme += 1
    else:
        sumario.total_inconsistente += 1

        # Calcula diferenças
        diferenca = valor_calculado - valor_nota
        if diferenca > 0:
            sumario.valor_inconsistente_maior += diferenca
        else:
            sumario.valor_inconsistente_menor += abs(diferenca)

    # Atualiza valores totais
    sumario.valor_total_notas += valor_nota
    sumario.valor_total_cenarios += valor_calculado
```

## Fluxo Completo de Auditoria

1. **Recebimento da Requisição**

   - Endpoint: `/api/auditoria/executar` ou `/api/auditoria/tributos`
   - Parâmetros: empresa_id, tipo_tributo, tipo_operacao, year, month

2. **Busca dos Tributos**

   - Filtra tributos por empresa, tipo de operação, ano e mês
   - Obtém lista de IDs dos tributos para auditoria

3. **Execução da Auditoria**

   - Para cada tributo, executa auditoria na ordem: IPI → ICMS → ICMS-ST → PIS → COFINS → DIFAL
   - Cada tributo atualiza suas colunas específicas na tabela `tributo`

4. **Comparação e Registro**

   - Compara valores calculados vs. valores da nota
   - Insere resultados na tabela `auditoria_resultado`
   - Atualiza sumários na tabela `auditoria_sumario`

5. **Retorno dos Resultados**
   - Retorna estatísticas da auditoria realizada
   - Inclui contadores por tipo de tributo e status

## Estruturas de Dados dos Cenários

### Cenário ICMS

```sql
CREATE TABLE cenario_icms (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id),
    cliente_id INTEGER REFERENCES cliente(id),
    produto_id INTEGER REFERENCES produto(id),

    -- Campos específicos ICMS
    origem VARCHAR(2),           -- Origem da mercadoria (0-Nacional, 1-Estrangeira, etc.)
    cst VARCHAR(3),             -- Código de Situação Tributária
    mod_bc VARCHAR(2),          -- Modalidade de determinação da BC
    p_red_bc DECIMAL(10,4),     -- Percentual de redução da BC
    aliquota DECIMAL(10,4),     -- Alíquota do ICMS
    p_dif DECIMAL(10,4),        -- Percentual do diferimento

    -- Campos comuns
    status VARCHAR(20) DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE
);
```

### Cenário ICMS-ST

```sql
CREATE TABLE cenario_icms_st (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id),
    cliente_id INTEGER REFERENCES cliente(id),
    produto_id INTEGER REFERENCES produto(id),

    -- Campos ICMS (também usados no ST)
    origem VARCHAR(2),
    cst VARCHAR(3),
    mod_bc VARCHAR(2),
    p_red_bc DECIMAL(10,4),
    aliquota DECIMAL(10,4),

    -- Campos específicos ICMS-ST
    icms_st_mod_bc VARCHAR(2),      -- Modalidade de determinação da BC do ST
    icms_st_aliquota DECIMAL(10,4), -- Alíquota do ICMS-ST
    icms_st_p_mva DECIMAL(10,4),    -- Percentual da Margem de Valor Agregado

    -- Campos comuns
    status VARCHAR(20) DEFAULT 'novo',
    ativo BOOLEAN DEFAULT FALSE
);
```

### Cenário IPI

```sql
CREATE TABLE cenario_ipi (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id),
    cliente_id INTEGER REFERENCES cliente(id),
    produto_id INTEGER REFERENCES produto(id),

    -- Campos específicos IPI
    cst VARCHAR(3),             -- Código de Situação Tributária
    aliquota DECIMAL(10,4),     -- Alíquota do IPI
    ex VARCHAR(3),              -- Código EX da TIPI

    -- Campos comuns
    status VARCHAR(20) DEFAULT 'novo',
    ativo BOOLEAN DEFAULT FALSE
);
```

### Cenário PIS

```sql
CREATE TABLE cenario_pis (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id),
    cliente_id INTEGER REFERENCES cliente(id),
    produto_id INTEGER REFERENCES produto(id),

    -- Campos específicos PIS
    cst VARCHAR(3),             -- Código de Situação Tributária
    aliquota DECIMAL(10,4),     -- Alíquota do PIS
    p_red_bc DECIMAL(10,4),     -- Percentual de redução da BC

    -- Campos comuns
    status VARCHAR(20) DEFAULT 'novo',
    ativo BOOLEAN DEFAULT FALSE
);
```

### Cenário COFINS

```sql
CREATE TABLE cenario_cofins (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id),
    cliente_id INTEGER REFERENCES cliente(id),
    produto_id INTEGER REFERENCES produto(id),

    -- Campos específicos COFINS
    cst VARCHAR(3),             -- Código de Situação Tributária
    aliquota DECIMAL(10,4),     -- Alíquota do COFINS
    p_red_bc DECIMAL(10,4),     -- Percentual de redução da BC

    -- Campos comuns
    status VARCHAR(20) DEFAULT 'novo',
    ativo BOOLEAN DEFAULT FALSE
);
```

## Colunas de Cenário na Tabela Tributo

A tabela `tributo` possui colunas específicas para armazenar os resultados dos cálculos:

```sql
-- Referências aos cenários utilizados
cenario_icms_id INTEGER REFERENCES cenario_icms(id),
cenario_icms_st_id INTEGER REFERENCES cenario_icms_st(id),
cenario_ipi_id INTEGER REFERENCES cenario_ipi(id),
cenario_pis_id INTEGER REFERENCES cenario_pis(id),
cenario_cofins_id INTEGER REFERENCES cenario_cofins(id),
cenario_difal_id INTEGER REFERENCES cenario_difal(id),

-- Valores calculados ICMS
cenario_icms_vbc DECIMAL(10,2),     -- Base de cálculo calculada
cenario_icms_valor DECIMAL(10,2),   -- Valor do ICMS calculado

-- Valores calculados ICMS-ST
cenario_icms_st_vbc DECIMAL(10,2),  -- Base de cálculo ST calculada
cenario_icms_st_valor DECIMAL(10,2), -- Valor do ICMS-ST calculado

-- Valores calculados IPI
cenario_ipi_vbc DECIMAL(10,2),      -- Base de cálculo IPI calculada
cenario_ipi_valor DECIMAL(10,2),    -- Valor do IPI calculado

-- Valores calculados PIS
cenario_pis_vbc DECIMAL(10,2),      -- Base de cálculo PIS calculada
cenario_pis_valor DECIMAL(10,2),    -- Valor do PIS calculado

-- Valores calculados COFINS
cenario_cofins_vbc DECIMAL(10,2),   -- Base de cálculo COFINS calculada
cenario_cofins_valor DECIMAL(10,2), -- Valor do COFINS calculado

-- Valores calculados DIFAL
cenario_difal_valor DECIMAL(10,2),  -- Valor do DIFAL calculado

-- Status de auditoria por tributo
auditoria_icms_status VARCHAR(20) DEFAULT 'pendente',
auditoria_icms_st_status VARCHAR(20) DEFAULT 'pendente',
auditoria_ipi_status VARCHAR(20) DEFAULT 'pendente',
auditoria_pis_status VARCHAR(20) DEFAULT 'pendente',
auditoria_cofins_status VARCHAR(20) DEFAULT 'pendente',
auditoria_difal_status VARCHAR(20) DEFAULT 'pendente',

-- Datas de auditoria por tributo
auditoria_icms_data TIMESTAMP,
auditoria_icms_st_data TIMESTAMP,
auditoria_ipi_data TIMESTAMP,
auditoria_pis_data TIMESTAMP,
auditoria_cofins_data TIMESTAMP,
auditoria_difal_data TIMESTAMP
```

## Exemplo Prático de Auditoria

### Cenário: Produto Industrial com IPI e ICMS

**Dados do Tributo:**

- Valor total da mercadoria: R$ 1.000,00
- Cliente: Uso e Consumo (cliente_uso_consumo_ativo = true)

**Cenário IPI:**

- CST: 50 (Tributado)
- Alíquota: 10%

**Cenário ICMS:**

- CST: 00 (Tributado integralmente)
- Alíquota: 18%
- Redução BC: 0%

### Execução dos Cálculos:

#### 1. Cálculo do IPI

```python
# IPI é calculado primeiro
base_calculo_ipi = 1000.00
valor_ipi = (1000.00 * 10) / 100 = 100.00

# Atualização na tabela tributo
cenario_ipi_id = 123
cenario_ipi_vbc = 1000.00
cenario_ipi_valor = 100.00
```

#### 2. Cálculo do ICMS

```python
# ICMS considera o IPI para cliente uso/consumo
base_calculo_icms = 1000.00 + 100.00 = 1100.00  # Inclui IPI
valor_icms = (1100.00 * 18) / 100 = 198.00

# Atualização na tabela tributo
cenario_icms_id = 456
cenario_icms_vbc = 1100.00
cenario_icms_valor = 198.00
```

#### 3. Comparação com Nota Fiscal

```python
# Valores da nota fiscal
nota_ipi_valor = 95.00      # Valor informado na NFe
nota_icms_valor = 200.00    # Valor informado na NFe

# Comparação IPI
diferenca_ipi = abs(100.00 - 95.00) = 5.00
status_ipi = 'inconsistente'  # > 0.01

# Comparação ICMS
diferenca_icms = abs(198.00 - 200.00) = 2.00
status_icms = 'inconsistente'  # > 0.01
```

#### 4. Inserção em auditoria_resultado

```sql
-- Resultado IPI
INSERT INTO auditoria_resultado (
    empresa_id, tributo_id, tipo_tributo,
    valor_nota, valor_calculado,
    base_calculo_nota, base_calculo_calculada,
    status
) VALUES (
    1, 789, 'ipi',
    95.00, 100.00,
    1000.00, 1000.00,
    'inconsistente'
);

-- Resultado ICMS
INSERT INTO auditoria_resultado (
    empresa_id, tributo_id, tipo_tributo,
    valor_nota, valor_calculado,
    base_calculo_nota, base_calculo_calculada,
    status
) VALUES (
    1, 789, 'icms',
    200.00, 198.00,
    1100.00, 1100.00,
    'inconsistente'
);
```

#### 5. Atualização do Sumário

```sql
-- Sumário IPI (Janeiro/2024)
UPDATE auditoria_sumario SET
    total_inconsistente = total_inconsistente + 1,
    valor_total_notas = valor_total_notas + 95.00,
    valor_total_cenarios = valor_total_cenarios + 100.00,
    valor_inconsistente_menor = valor_inconsistente_menor + 5.00
WHERE empresa_id = 1 AND ano = 2024 AND mes = 1 AND tipo_tributo = 'ipi';

-- Sumário ICMS (Janeiro/2024)
UPDATE auditoria_sumario SET
    total_inconsistente = total_inconsistente + 1,
    valor_total_notas = valor_total_notas + 200.00,
    valor_total_cenarios = valor_total_cenarios + 198.00,
    valor_inconsistente_maior = valor_inconsistente_maior + 2.00
WHERE empresa_id = 1 AND ano = 2024 AND mes = 1 AND tipo_tributo = 'icms';
```

## Considerações Importantes

- **Dependências**: IPI deve ser calculado primeiro pois afeta ICMS e ICMS-ST
- **Tolerância**: Diferenças de até R$ 0,01 são consideradas conformes (arredondamento)
- **Cenários Vigentes**: Apenas cenários com status 'producao' e ativo=true são utilizados
- **Período de Vigência**: Data de emissão deve estar dentro do período de vigência do cenário
- **Clientes Especiais**: Uso/consumo e ativo imobilizado têm regras específicas para ICMS
- **Ordem de Execução**: Sempre IPI → ICMS → ICMS-ST → PIS → COFINS → DIFAL
- **Atomicidade**: Cada tributo é auditado independentemente, permitindo auditoria parcial
- **Histórico**: Todas as auditorias são registradas com timestamp para rastreabilidade
