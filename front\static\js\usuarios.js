/**
 * Usuarios.js - Auditoria Fiscal
 * Funções para gerenciar a página de usuários
 */

// Variáveis globais
let usuariosTable = null;
let currentUsuario = null;
let empresasDisponiveis = [];

document.addEventListener('DOMContentLoaded', function () {
  console.log('DOM carregado - usuarios.js');

  // Inicializar a página de usuários
  initUsuariosPage();
});

/**
 * Inicializa a página de usuários
 */
function initUsuariosPage() {
  console.log('Inicializando página de usuários');

  // Verificar se estamos na página de usuários
  if (window.location.pathname.includes('/usuarios')) {
    // Configurar o botão de adicionar usuário
    setupAddUsuarioButton();

    // Carregar dados de usuários
    loadUsuariosData();
  }
}

/**
 * Configura o botão de adicionar usuário
 */
function setupAddUsuarioButton() {
  const addButton = document.getElementById('new-usuario-btn');
  if (!addButton) return;

  addButton.addEventListener('click', function () {
    // Carregar empresas disponíveis antes de mostrar o modal
    carregarEmpresasDisponiveis().then(() => {
      showUsuarioModal();
    });
  });
}

/**
 * Carrega dados de usuários
 */
function loadUsuariosData() {
  console.log('Carregando dados de usuários...');

  // Mostrar indicador de carregamento
  const usuariosContent = document.getElementById('usuarios-content');
  if (!usuariosContent) return;

  usuariosContent.innerHTML =
    '<div class="text-center my-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">Carregando usuários...</p></div>';

  // Buscar usuários da API
  fetch('/fiscal/api/usuarios', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      renderUsuariosTable(data.usuarios || []);
    })
    .catch((error) => {
      console.error('Erro ao carregar usuários:', error);
      usuariosContent.innerHTML =
        '<div class="alert alert-danger">Erro ao carregar usuários. Tente novamente mais tarde.</div>';
    });
}

/**
 * Renderiza a tabela de usuários
 * @param {Array} usuarios - Lista de usuários
 */
function renderUsuariosTable(usuarios) {
  const usuariosContent = document.getElementById('usuarios-content');
  if (!usuariosContent) return;

  if (usuarios.length === 0) {
    usuariosContent.innerHTML =
      '<div class="alert alert-info">Nenhum usuário cadastrado.</div>';
    return;
  }

  // Criar tabela
  let html = `
    <div class="table-responsive">
      <table id="usuarios-table" class="table table-striped table-hover">
        <thead>
          <tr>
            <th>Nome</th>
            <th>Tipo</th>
            <th>Empresas Permitidas</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Adicionar linhas
  usuarios.forEach((usuario) => {
    const tipoUsuario =
      usuario.tipo_usuario === 'admin'
        ? 'Administrador'
        : usuario.tipo_usuario === 'escritorio'
        ? 'Escritório'
        : 'Usuário';

    const empresasCount = usuario.empresas_permitidas
      ? usuario.empresas_permitidas.length
      : 0;
    const empresasText =
      empresasCount > 0 ? `${empresasCount} empresa(s)` : 'Nenhuma';

    html += `
      <tr>
        <td>${usuario.nome || '-'}</td>
        <td>${tipoUsuario}</td>
        <td>${empresasText}</td>
        <td>
          <button class="btn btn-sm btn-primary edit-usuario-btn" data-id="${
            usuario.id
          }">
            <i class="fas fa-edit"></i>
          </button>
        </td>
      </tr>
    `;
  });

  html += `
        </tbody>
      </table>
    </div>
  `;

  // Atualizar o conteúdo
  usuariosContent.innerHTML = html;

  // Inicializar DataTable
  try {
    usuariosTable = new DataTable('#usuarios-table', {
      language: {
        url: '/fiscal/static/js/vendor/datatables/pt-BR.json',
      },
      responsive: true,
    });
  } catch (error) {
    console.error('Erro ao inicializar DataTable:', error);
  }

  // Configurar botões de edição
  document.querySelectorAll('.edit-usuario-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const usuarioId = this.getAttribute('data-id');

      // Carregar empresas disponíveis antes de editar o usuário
      carregarEmpresasDisponiveis().then(() => {
        editUsuario(usuarioId);
      });
    });
  });
}

/**
 * Carrega as empresas disponíveis para o usuário atual
 * @returns {Promise} - Promise que resolve quando as empresas forem carregadas
 */
function carregarEmpresasDisponiveis() {
  return new Promise((resolve, reject) => {
    fetch('/fiscal/api/empresas', {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    })
      .then((response) => response.json())
      .then((data) => {
        empresasDisponiveis = data.empresas || [];
        resolve();
      })
      .catch((error) => {
        console.error('Erro ao carregar empresas:', error);
        empresasDisponiveis = [];
        resolve(); // Resolve mesmo com erro para não bloquear o fluxo
      });
  });
}

/**
 * Renderiza os checkboxes das empresas disponíveis
 * @param {Array} empresasPermitidas - IDs das empresas permitidas
 * @returns {string} - HTML dos checkboxes
 */
function renderEmpresasCheckboxes(empresasPermitidas) {
  if (empresasDisponiveis.length === 0) {
    return '<div class="alert alert-info">Nenhuma empresa disponível.</div>';
  }

  let html = '';

  empresasDisponiveis.forEach((empresa) => {
    const checked = empresasPermitidas.includes(empresa.id) ? 'checked' : '';

    html += `
      <div class="form-check">
        <input class="form-check-input empresa-checkbox" type="checkbox" id="empresa-${empresa.id}"
               value="${empresa.id}" ${checked}>
        <label class="form-check-label" for="empresa-${empresa.id}">
          ${empresa.razao_social} (${empresa.cnpj})
        </label>
      </div>
    `;
  });

  return html;
}

/**
 * Mostra o modal para adicionar/editar usuário
 * @param {Object} usuario - Dados do usuário (opcional, para edição)
 */
function showUsuarioModal(usuario = null) {
  currentUsuario = usuario;

  const modalTitle = document.getElementById('modalTitle');
  const modalBody = document.getElementById('modalBody');

  modalTitle.textContent = usuario ? 'Editar Usuário' : 'Novo Usuário';

  const podeEditarTipo =
    currentUser.is_admin || currentUser.tipo_usuario === 'admin';

    if (usuario) {
      // Modal simplificado para edição: apenas empresas permitidas
      modalBody.innerHTML = `
        <div id="form-message"></div>
        <form id="usuario-form" class="needs-validation" novalidate>
          <div class="mb-3">
            <label class="form-label">Empresas Permitidas</label>
            <div class="empresas-container border rounded p-3" style="max-height: 200px; overflow-y: auto;">
              ${renderEmpresasCheckboxes(usuario.empresas_permitidas || [])}
            </div>
          </div>
  
          <div class="mt-4 d-flex justify-content-end">
            <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">Cancelar</button>
            <button type="submit" class="btn btn-primary" id="save-usuario-btn">Salvar</button>
          </div>
        </form>
      `;
    } else {
      // Formulário completo para criação de usuário (opção atualmente oculta)
      modalBody.innerHTML = `
        <div id="form-message"></div>
        <form id="usuario-form" class="needs-validation" novalidate>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="nome" class="form-label">Nome *</label>
              <input type="text" class="form-control" id="nome" name="nome" required value="${usuario ? usuario.nome || '' : ''}">
              <div class="invalid-feedback">Nome é obrigatório</div>
            </div>
            <div class="col-md-6 mb-3">
              <label for="email" class="form-label">Email *</label>
              <input type="email" class="form-control" id="email" name="email" required value="${usuario ? usuario.email || '' : ''}">
              <div class="invalid-feedback">Email válido é obrigatório</div>
            </div>
          </div>
  
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="senha" class="form-label">${usuario ? 'Nova Senha (deixe em branco para manter a atual)' : 'Senha *'}</label>
              <input type="password" class="form-control" id="senha" name="senha" ${usuario ? '' : 'required'}>
              <div class="invalid-feedback">Senha é obrigatória</div>
            </div>
            <div class="col-md-6 mb-3">
              <label for="tipo_usuario" class="form-label">Tipo de Usuário</label>
              <select class="form-select" id="tipo_usuario" name="tipo_usuario" ${podeEditarTipo ? '' : 'disabled'}>
                <option value="usuario" ${usuario && usuario.tipo_usuario === 'usuario' ? 'selected' : ''}>Usuário</option>
                ${podeEditarTipo ? `<option value="escritorio" ${usuario && usuario.tipo_usuario === 'escritorio' ? 'selected' : ''}>Escritório</option>
                <option value="admin" ${usuario && usuario.tipo_usuario === 'admin' ? 'selected' : ''}>Administrador</option>` : ''}
              </select>
            </div>
          </div>
  
          <div class="mb-3">
            <label class="form-label">Empresas Permitidas</label>
            <div class="empresas-container border rounded p-3" style="max-height: 200px; overflow-y: auto;">
              ${renderEmpresasCheckboxes(usuario ? usuario.empresas_permitidas || [] : [])}
            </div>
          </div>
  
          <div class="mt-4 d-flex justify-content-end">
            <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">Cancelar</button>
            <button type="submit" class="btn btn-primary" id="save-usuario-btn">Salvar</button>
          </div>
        </form>
      `;
    }
  
    // Mostrar o modal
    const modal = new bootstrap.Modal(document.getElementById('formModal'));
    modal.show();
  
    // Configurar envio do formulário
    setupUsuarioForm();
  }
  
  /**
   * Configura o envio do formulário de usuário
   */
  function setupUsuarioForm() {
    const form = document.getElementById('usuario-form');
    if (!form) return;
  
    form.addEventListener('submit', function (event) {
      event.preventDefault();
  
      // Validar formulário
      if (!form.checkValidity()) {
        event.stopPropagation();
        form.classList.add('was-validated');
        return;
      }
  
      // Desabilitar botão de salvar
      const saveButton = document.getElementById('save-usuario-btn');
      saveButton.disabled = true;
      saveButton.innerHTML =
        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Salvando...';
  
      // Obter empresas selecionadas
      const empresasPermitidas = [];
      document
        .querySelectorAll('.empresa-checkbox:checked')
        .forEach((checkbox) => {
          empresasPermitidas.push(parseInt(checkbox.value));
        });
  
      // Preparar dados
      const formData = {
        empresas_permitidas: empresasPermitidas,
      };
  
      const nomeEl = document.getElementById('nome');
      if (nomeEl) formData.nome = nomeEl.value;
  
      const emailEl = document.getElementById('email');
      if (emailEl) formData.email = emailEl.value;
  
      const senhaEl = document.getElementById('senha');
      if (senhaEl && senhaEl.value) formData.senha = senhaEl.value;
  
      const tipoEl = document.getElementById('tipo_usuario');
      if (tipoEl) formData.tipo_usuario = tipoEl.value;
  
      // Se for admin, adicionar escritório_id
      if (currentUser.is_admin || currentUser.tipo_usuario === 'admin') {
        formData.escritorio_id = currentUser.escritorio_id;
      }

    // Determinar se é criação ou atualização
    const isUpdate = currentUsuario !== null;
    const url = isUpdate
      ? `/fiscal/api/usuarios/${currentUsuario.id}`
      : '/fiscal/api/usuarios';
    const method = isUpdate ? 'PUT' : 'POST';

    // Enviar requisição
    fetch(url, {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify(formData),
    })
      .then(async (response) => {
        const data = await response.json();
        const formMessage = document.getElementById('form-message');

        if (!response.ok) {
          // Erro
          formMessage.innerHTML = `<div class="alert alert-danger">${data.message || 'Erro ao processar a requisição'}</div>`;
          saveButton.disabled = false;
          saveButton.innerHTML = 'Salvar';
          return;
        }

        // Sucesso
        formMessage.innerHTML = `<div class="alert alert-success">${
          isUpdate
            ? 'Usuário atualizado com sucesso!'
            : 'Usuário criado com sucesso!'
        }</div>`;

        // Fechar modal após 2 segundos
        setTimeout(() => {
          const modal = bootstrap.Modal.getInstance(
            document.getElementById('formModal'),
          );
          modal.hide();

          // Recarregar dados
          loadUsuariosData();
        }, 2000);
      })
      .catch((error) => {
        console.error('Erro ao salvar usuário:', error);
        const formMessage = document.getElementById('form-message');
        formMessage.innerHTML =
          '<div class="alert alert-danger">Erro ao salvar usuário. Tente novamente.</div>';

        // Restaurar botão
        saveButton.disabled = false;
        saveButton.innerHTML = 'Salvar';
      });
  });
}

/**
 * Edita um usuário existente
 * @param {number} usuarioId - ID do usuário
 */
function editUsuario(usuarioId) {
  // Buscar dados do usuário
  fetch(`/fiscal/api/usuarios/${usuarioId}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.usuario) {
        showUsuarioModal(data.usuario);
      } else {
        alert('Erro ao carregar dados do usuário.');
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar usuário:', error);
      alert('Erro ao carregar dados do usuário. Tente novamente.');
    });
}
