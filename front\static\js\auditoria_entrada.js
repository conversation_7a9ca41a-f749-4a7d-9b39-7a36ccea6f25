/**
 * auditoria_entrada.js - Sistema de Auditoria de Entrada
 * Gerencia XMLs importados, notas faltantes e auditoria de entrada
 */

// Estado global da página
let currentTab = 'entrada';
let currentData = [];
let selectedItems = [];
let currentSummary = {};

document.addEventListener('DOMContentLoaded', function() {
    // Verificar se estamos nas páginas de auditoria de entrada
    const currentPath = window.location.pathname;
    if (currentPath === '/gestao-xmls' || currentPath === '/auditoria/entrada/auditoria') {
        initAuditoriaEntrada();
    }
});

/**
 * Inicializa a página de auditoria de entrada
 */
function initAuditoriaEntrada() {
    const currentPath = window.location.pathname;

    if (currentPath === '/gestao-xmls') {
        initGestaoXMLs();
    } else if (currentPath === '/auditoria/entrada/auditoria') {
        initAuditoriaComparativa();
    }
}

/**
 * Inicializa a página de gestão de XMLs
 */
function initGestaoXMLs() {
    setupTabNavigation();
    setupFilters();
    setupBulkActions();
    loadInitialData();
}

/**
 * Inicializa a página de auditoria comparativa
 */
function initAuditoriaComparativa() {
    setupTributoNavigation();
    setupAuditoriaFilters();
    setupAuditoriaActions();
    loadAuditoriaData();
}

/**
 * Configura a navegação por abas
 */
function setupTabNavigation() {
    const tabs = document.querySelectorAll('.nav-tabs .nav-link');
    tabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remover classe active de todas as abas
            tabs.forEach(t => t.classList.remove('active'));
            
            // Adicionar classe active na aba clicada
            this.classList.add('active');
            
            // Atualizar aba atual
            currentTab = this.getAttribute('data-tab');
            
            // Carregar dados da aba
            loadTabData(currentTab);
        });
    });
}

/**
 * Configura os filtros da página
 */
function setupFilters() {
    console.log('Configurando filtros de auditoria de entrada...');

    // Escutar mudanças nos filtros globais
    window.addEventListener('company-changed', function(event) {
        console.log('Empresa alterada:', event.detail.companyId);
        if (currentTab) {
            loadTabData(currentTab);
        } else {
            loadAuditoriaData();
        }
    });

    // Configurar filtros de mês e ano se existirem
    const anoSelect = document.getElementById('year-select');
    const mesSelect = document.getElementById('month-select');

    if (anoSelect) {
        anoSelect.addEventListener('change', function() {
            localStorage.setItem('selectedYear', this.value);
            if (currentTab) {
                loadTabData(currentTab);
            } else {
                loadAuditoriaData();
            }
        });
    }

    if (mesSelect) {
        mesSelect.addEventListener('change', function() {
            localStorage.setItem('selectedMonth', this.value);
            if (currentTab) {
                loadTabData(currentTab);
            } else {
                loadAuditoriaData();
            }
        });
    }
}

/**
 * Configura ações em massa
 */
function setupBulkActions() {
    // Botão de alterar data em massa
    const btnAlterarData = document.getElementById('btn-alterar-data-massa');
    if (btnAlterarData) {
        btnAlterarData.addEventListener('click', showAlterarDataModal);
    }
    
    // Botão de excluir em massa
    const btnExcluir = document.getElementById('btn-excluir-massa');
    if (btnExcluir) {
        btnExcluir.addEventListener('click', showExcluirModal);
    }
    
    // Botão de identificar faltantes
    const btnIdentificarFaltantes = document.getElementById('btn-identificar-faltantes');
    if (btnIdentificarFaltantes) {
        btnIdentificarFaltantes.addEventListener('click', identificarNotasFaltantes);
    }
    
    // Botão de gerar auditoria
    const btnGerarAuditoria = document.getElementById('btn-gerar-auditoria');
    if (btnGerarAuditoria) {
        btnGerarAuditoria.addEventListener('click', gerarAuditoria);
    }
}

/**
 * Carrega dados iniciais
 */
function loadInitialData() {
    const empresaId = localStorage.getItem('selectedCompany');
    if (empresaId) {
        loadTabData(currentTab);
    }
}

/**
 * Carrega dados de uma aba específica
 */
function loadTabData(tab) {
    // Tentar obter filtros do header primeiro, depois do localStorage
    const empresaId = document.getElementById('empresa-select')?.value || localStorage.getItem('selectedCompany');
    const ano = document.getElementById('year-select')?.value || localStorage.getItem('selectedYear');
    const mes = document.getElementById('month-select')?.value || localStorage.getItem('selectedMonth');
    const tolerancia = document.getElementById('tolerancia-escrituracao')?.value || localStorage.getItem('toleranciaEscrituracao');

    console.log('DEBUG - Filtros loadTabData:', { empresaId, ano, mes, tab });

    if (!empresaId) {
        showMessage('Selecione uma empresa para visualizar os dados', 'warning');
        return;
    }
    
    showLoading();
    
    let url = '';
    let params = new URLSearchParams({
        empresa_id: empresaId,
        tipo: tab
    });
    
    if (ano) params.append('ano', ano);
    if (mes) params.append('mes', mes);
    
    url = `/api/auditoria-entrada/xmls?${params.toString()}`;
    
    fetch(url, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            currentData = data.xmls || data.notas || [];
            currentSummary = {
                primeira_nota: data.primeira_nota,
                ultima_nota: data.ultima_nota
            };
            renderTabContent(tab, currentData, currentSummary);
        } else {
            showMessage(data.message || 'Erro ao carregar dados', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao carregar dados:', error);
        showMessage('Erro ao carregar dados', 'error');
    });
}

/**
 * Renderiza o conteúdo de uma aba
 */
function renderTabContent(tab, data, summary) {
    const container = document.getElementById('tab-content');
    
    if (tab === 'faltantes-entrada' || tab === 'faltantes-saida') {
        // Usar nova função para notas faltantes
        if (typeof renderNotasFaltantesNovo === 'function') {
            renderNotasFaltantesNovo(container, data, tab);
        } else {
            renderNotasFaltantes(container, data);
        }
    } else if (tab === 'faltantes') {
        renderNotasFaltantes(container, data);
    } else {
        renderXMLsList(container, data, tab, summary);
    }
}

/**
 * Renderiza lista de XMLs
 */
function renderXMLsList(container, xmls, tipo, summary) {
    let html = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5>${tipo === 'entrada' ? 'XMLs de Entrada' : 'XMLs de Saída'}</h5>
            <div class="btn-group">
                <button class="btn btn-info btn-sm" id="btn-identificar-faltantes">
                    <i class="fas fa-search"></i> Identificar Faltantes
                </button>
            </div>
        </div>
        <div class="mb-3">
            <small class="text-muted">
                Primeira Nota: ${summary?.primeira_nota ? `${summary.primeira_nota.numero_nf || '-'}, ${summary.primeira_nota.data ? formatarDataParaBR(summary.primeira_nota.data) : '-'}` : '-'}
            </small><br>
            <small class="text-muted">
                Última Nota: ${summary?.ultima_nota ? `${summary.ultima_nota.numero_nf || '-'}, ${summary.ultima_nota.data ? formatarDataParaBR(summary.ultima_nota.data) : '-'}` : '-'}
            </small>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="xmls-table">
                <thead>
                    <tr>
                        <th>Número NF</th>
                        <th>Data Emissão</th>
                        <th>Data Entrada</th>
                        <th>Emitente</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    xmls.forEach(xml => {
        const dataEmissao = xml.data_emissao ? formatarDataParaBR(xml.data_emissao) : '-';
        const dataEntrada = xml.data_entrada ? formatarDataParaBR(xml.data_entrada) : dataEmissao;
        const statusClass = xml.status_validacao === 'validado' ? 'success' : 
                           xml.status_validacao === 'cancelado' ? 'danger' : 'warning';
        
        html += `
            <tr>
                <td>${xml.numero_nf || '-'}</td>
                <td>${dataEmissao}</td>
                <td>${dataEntrada}</td>
                <td>
                    <div>
                        <strong>${xml.razao_social_emitente || '-'}</strong><br>
                        <small class="">${xml.cnpj_emitente || '-'}</small>
                    </div>
                </td>
                <td>
                    <span class="badge bg-${statusClass}">
                        ${xml.status_validacao || 'Pendente'}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-info" onclick="verDetalhesXML(${xml.id})" title="Detalhes">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="excluirXML(${xml.id})" title="Excluir">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = html;
    
    // Reconfigurar event listeners
    setupBulkActions();
    
    // Inicializar DataTable
    if ($.fn.DataTable) {
        $('#xmls-table').DataTable({
            language: {
                url: '/static/js/vendor/datatables/pt-BR.json'
            },
            responsive: true,
            order: [[3, 'desc']] // Ordenar por data de emissão
        });
    }
}

/**
 * Renderiza lista de notas faltantes
 */
function renderNotasFaltantes(container, notas) {
    let html = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5>Notas Faltantes</h5>
            <div class="btn-group">
                <button class="btn btn-warning" id="btn-identificar-faltantes">
                    <i class="fas fa-search"></i> Identificar Faltantes
                </button>
                <button class="btn btn-primary" id="btn-alterar-data-massa" ${selectedItems.length === 0 ? 'disabled' : ''}>
                    <i class="fas fa-calendar-alt"></i> Alterar Data
                </button>
                <button class="btn btn-danger" id="btn-excluir-massa" ${selectedItems.length === 0 ? 'disabled' : ''}>
                    <i class="fas fa-trash"></i> Cancelar
                </button>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="faltantes-table">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="select-all" onchange="toggleSelectAll(this)">
                        </th>
                        <th>Número NF</th>
                        <th>Data Emissão</th>
                        <th>Data Entrada</th>
                        <th>Origem</th>
                        <th>Status</th>
                        <th>Data Identificação</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    notas.forEach(nota => {
        const dataEmissao = nota.data_emissao ? formatarDataParaBR(nota.data_emissao) : '-';
        const dataEntrada = nota.data_entrada ? formatarDataParaBR(nota.data_entrada) : '-';
        const dataIdentificacao = new Date(nota.data_identificacao).toLocaleDateString('pt-BR');
        const statusClass = nota.status === 'encontrado' ? 'success' : 
                           nota.status === 'cancelado' ? 'danger' : 'warning';
        
        html += `
            <tr>
                <td>
                    <input type="checkbox" class="item-checkbox" value="${nota.id}"
                           onchange="toggleItemSelection(this)">
                </td>
                <td>${nota.numero_nf || '-'}</td>
                <td>${dataEmissao}</td>
                <td>${dataEntrada}</td>
                <td>
                    <span class="badge bg-${nota.origem === 'XML' ? 'primary' : 'info'}">
                        ${nota.origem}
                    </span>
                </td>
                <td>
                    <span class="badge bg-${statusClass}">
                        ${nota.status}
                    </span>
                </td>
                <td>${dataIdentificacao}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editarNotaFaltante(${nota.id})" title="Editar">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="marcarEncontrado(${nota.id})" title="Marcar como Encontrado">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="marcarCancelado(${nota.id})" title="Marcar como Cancelado">
                            <i class="fas fa-times"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="excluirNotaFaltante(${nota.id})" title="Excluir">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="abrirModalAlterarData(${nota.id})" title="Alterar Data">
                            <i class="fas fa-calendar-alt"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = html;
    
    // Reconfigurar event listeners
    setupBulkActions();
    
    // Inicializar DataTable
    if ($.fn.DataTable) {
        $('#faltantes-table').DataTable({
            language: {
                url: '/static/js/vendor/datatables/pt-BR.json'
            },
            responsive: true,
            order: [[6, 'desc']] // Ordenar por data de identificação
        });
    }
}

/**
 * Toggle seleção de todos os itens
 */
function toggleSelectAll(checkbox) {
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    selectedItems = [];
    
    itemCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
        if (checkbox.checked) {
            selectedItems.push(parseInt(cb.value));
        }
    });
    
    updateBulkActionButtons();
}

/**
 * Toggle seleção de item individual
 */
function toggleItemSelection(checkbox) {
    const itemId = parseInt(checkbox.value);
    
    if (checkbox.checked) {
        if (!selectedItems.includes(itemId)) {
            selectedItems.push(itemId);
        }
    } else {
        selectedItems = selectedItems.filter(id => id !== itemId);
    }
    
    // Atualizar checkbox "selecionar todos"
    const selectAllCheckbox = document.getElementById('select-all');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
    
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = checkedCount === itemCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < itemCheckboxes.length;
    }
    
    updateBulkActionButtons();
}

/**
 * Atualiza estado dos botões de ação em massa
 */
function updateBulkActionButtons() {
    const btnAlterarData = document.getElementById('btn-alterar-data-massa');
    const btnExcluir = document.getElementById('btn-excluir-massa');
    
    const hasSelection = selectedItems.length > 0;
    
    if (btnAlterarData) {
        btnAlterarData.disabled = !hasSelection;
    }
    
    if (btnExcluir) {
        btnExcluir.disabled = !hasSelection;
    }
}

/**
 * Identifica notas faltantes
 */
function identificarNotasFaltantes() {
    // Tentar obter filtros do header primeiro, depois do localStorage
    let empresaId = document.getElementById('empresa-select')?.value || localStorage.getItem('selectedCompany');
    let ano = document.getElementById('year-select')?.value || localStorage.getItem('selectedYear');
    let mes = document.getElementById('month-select')?.value || localStorage.getItem('selectedMonth');

    console.log('DEBUG - Filtros identificar faltantes:', { empresaId, ano, mes });

    if (!empresaId || !ano || !mes) {
        showMessage('Selecione empresa, ano e mês para identificar notas faltantes', 'warning');
        return;
    }
    
    showLoading();
    
    fetch('/api/auditoria-entrada/identificar-faltantes', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            empresa_id: parseInt(empresaId),
            mes: parseInt(mes),
            ano: parseInt(ano)
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            showMessage(`Identificação concluída! ${data.total_faltantes} notas faltantes encontradas.`, 'success');
            loadTabData('faltantes');
        } else {
            showMessage(data.message || 'Erro ao identificar notas faltantes', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao identificar notas faltantes:', error);
        showMessage('Erro ao identificar notas faltantes', 'error');
    });
}

// Funções auxiliares
function getToken() {
    return localStorage.getItem('token');
}

function showLoading() {
    // Implementar loading
}

function hideLoading() {
    // Implementar hide loading
}

function showMessage(message, type) {
    // Implementar sistema de mensagens
    console.log(`${type.toUpperCase()}: ${message}`);
}

/**
 * Gera auditoria completa para os dados selecionados (XML x SPED x Cenários)
 */
function gerarAuditoria() {
    // Tentar obter filtros do header primeiro, depois do localStorage
    const empresaId = document.getElementById('empresa-select')?.value || localStorage.getItem('selectedCompany');
    const ano = document.getElementById('year-select')?.value || localStorage.getItem('selectedYear');
    const mes = document.getElementById('month-select')?.value || localStorage.getItem('selectedMonth');

    console.log('DEBUG - Filtros gerarAuditoria:', { empresaId, ano, mes });

    if (!empresaId || !ano || !mes) {
        showMessage('Selecione empresa, ano e mês para gerar auditoria', 'warning');
        return;
    }

    showLoading();

    fetch('/api/auditoria-entrada/gerar-auditoria', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            empresa_id: parseInt(empresaId),
            mes: parseInt(mes),
            ano: parseInt(ano)
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage(`Auditoria completa gerada com sucesso! ${data.total_auditorias} registros processados.`, 'success');
            loadAuditoriaData();
        } else {
            showMessage(data.message || 'Erro ao gerar auditoria', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao gerar auditoria:', error);
        showMessage('Erro ao gerar auditoria', 'error');
    });
}

/**
 * Gera auditoria de escrituração (comparação de valores totais XML vs SPED)
 */
function gerarAuditoriaEscrituracao() {
    // Tentar obter filtros do header primeiro, depois do localStorage
    const empresaId = document.getElementById('empresa-select')?.value || localStorage.getItem('selectedCompany');
    const ano = document.getElementById('year-select')?.value || localStorage.getItem('selectedYear');
    const mes = document.getElementById('month-select')?.value || localStorage.getItem('selectedMonth');
    const tolerancia = document.getElementById('tolerancia-escrituracao')?.value ||
        localStorage.getItem('toleranciaEscrituracao');

        console.log('DEBUG - Filtros gerarAuditoriaEscrituracao:', { empresaId, ano, mes, tolerancia });

    if (!empresaId || !ano || !mes) {
        showMessage('Selecione empresa, ano e mês para gerar auditoria de escrituração', 'warning');
        return;
    }

    showLoading();

    fetch('/api/auditoria-entrada/gerar-escrituracao', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            empresa_id: parseInt(empresaId),
            mes: parseInt(mes),
            ano: parseInt(ano),
            forcar_recalculo: true,
            tolerancia: parseFloat(tolerancia || 0.01)
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage(data.message || `Auditoria de escrituração gerada com sucesso! ${data.total_auditorias} registros processados.`, 'success');
            loadAuditoriaData();
        } else {
            showMessage(data.message || 'Erro ao gerar auditoria de escrituração', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao gerar auditoria de escrituração:', error);
        showMessage('Erro ao gerar auditoria de escrituração', 'error');
    });
}

/**
 * Exclui um XML importado
 */
function excluirXML(xmlId) {
    if (!confirm('Tem certeza que deseja excluir este XML? Esta ação não pode ser desfeita.')) {
        return;
    }

    showLoading();

    fetch(`/api/auditoria-entrada/xmls/${xmlId}`, {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage('XML excluído com sucesso', 'success');
            loadTabData(currentTab);
        } else {
            showMessage(data.message || 'Erro ao excluir XML', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao excluir XML:', error);
        showMessage('Erro ao excluir XML', 'error');
    });
}

/**
 * Exclui uma nota faltante
 */
function excluirNotaFaltante(notaId) {
    if (!confirm('Tem certeza que deseja excluir esta nota faltante? Esta ação não pode ser desfeita.')) {
        return;
    }

    showLoading();

    fetch(`/api/auditoria-entrada/notas-faltantes/${notaId}`, {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage('Nota faltante excluída com sucesso', 'success');
            loadTabData('faltantes');
        } else {
            showMessage(data.message || 'Erro ao excluir nota faltante', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao excluir nota faltante:', error);
        showMessage('Erro ao excluir nota faltante', 'error');
    });
}

/**
 * Ver detalhes de um XML
 */
function verDetalhesXML(xmlId) {
    // Implementar modal ou página de detalhes
    console.log('Ver detalhes do XML:', xmlId);
    showMessage('Funcionalidade de detalhes em desenvolvimento', 'info');
}

/**
 * Abre modal para alterar data de nota faltante
 */
function abrirModalAlterarData(notaId) {
    // Criar modal dinamicamente
    const modalHtml = `
        <div class="modal fade" id="modalAlterarData" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Alterar Data da Nota</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="formAlterarData">
                            <div class="form-group">
                                <label for="novaDataEmissao">Nova Data de Emissão:</label>
                                <input type="date" class="form-control" id="novaDataEmissao" required>
                            </div>
                            <div class="form-group">
                                <label for="novaDataEntrada">Nova Data de Entrada:</label>
                                <input type="date" class="form-control" id="novaDataEntrada" required>
                            </div>
                            <div class="form-group">
                                <label for="observacoes">Observações:</label>
                                <textarea class="form-control" id="observacoes" rows="3" placeholder="Motivo da alteração..."></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-primary" onclick="salvarAlteracaoData(${notaId})">Salvar</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remover modal existente se houver
    $('#modalAlterarData').remove();

    // Adicionar modal ao DOM
    $('body').append(modalHtml);

    // Mostrar modal
    $('#modalAlterarData').modal('show');
}

/**
 * Salva alteração de data da nota faltante
 */
function salvarAlteracaoData(notaId) {
    const novaDataEmissao = document.getElementById('novaDataEmissao').value;
    const novaDataEntrada = document.getElementById('novaDataEntrada').value;
    const observacoes = document.getElementById('observacoes').value;

    if (!novaDataEmissao || !novaDataEntrada) {
        showMessage('Preencha todas as datas obrigatórias', 'warning');
        return;
    }

    // Converter para formato brasileiro
    const dataEmissaoBR = formatarDataParaBR(novaDataEmissao);
    const dataEntradaBR = formatarDataParaBR(novaDataEntrada);

    showLoading();

    fetch('/api/auditoria-entrada/alterar-data-nota-faltante', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            nota_id: notaId,
            nova_data_emissao: dataEmissaoBR,
            nova_data_entrada: dataEntradaBR,
            observacoes: observacoes
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage('Data alterada com sucesso', 'success');
            $('#modalAlterarData').modal('hide');
            loadTabData('faltantes');
        } else {
            showMessage(data.message || 'Erro ao alterar data', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao alterar data:', error);
        showMessage('Erro ao alterar data', 'error');
    });
}

/**
 * Converte data do formato YYYY-MM-DD para DD/MM/YYYY
 */
function formatarDataParaBR(dataISO) {
    if (!dataISO) return '';

    const [ano, mes, dia] = dataISO.split('T')[0].split('-');
    return `${dia}/${mes}/${ano}`;
}

/**
 * Abre modal para alteração em massa de datas
 */
function abrirModalAlteracaoMassa() {
    const notasSelecionadas = getNotasFaltantesSelecionadas();

    if (notasSelecionadas.length === 0) {
        showMessage('Selecione pelo menos uma nota para alterar', 'warning');
        return;
    }

    // Criar modal para alteração em massa
    const modalHtml = `
        <div class="modal fade" id="modalAlteracaoMassa" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Alteração em Massa - ${notasSelecionadas.length} notas selecionadas</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="formAlteracaoMassa">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="novaDataEmissaoMassa">Nova Data de Emissão:</label>
                                        <input type="date" class="form-control" id="novaDataEmissaoMassa">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="novaDataEntradaMassa">Nova Data de Entrada:</label>
                                        <input type="date" class="form-control" id="novaDataEntradaMassa">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="observacoesMassa">Observações:</label>
                                <textarea class="form-control" id="observacoesMassa" rows="3" placeholder="Motivo da alteração em massa..."></textarea>
                            </div>
                            <div class="alert alert-info">
                                <strong>Atenção:</strong> Esta operação irá alterar as datas de ${notasSelecionadas.length} notas selecionadas.
                                Deixe em branco os campos que não deseja alterar.
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-primary" onclick="salvarAlteracaoMassa()">Alterar ${notasSelecionadas.length} Notas</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remover modal existente se houver
    $('#modalAlteracaoMassa').remove();

    // Adicionar modal ao DOM
    $('body').append(modalHtml);

    // Mostrar modal
    $('#modalAlteracaoMassa').modal('show');
}

/**
 * Obtém notas faltantes selecionadas
 */
function getNotasFaltantesSelecionadas() {
    const checkboxes = document.querySelectorAll('input[name="notaFaltante"]:checked');
    return Array.from(checkboxes).map(cb => parseInt(cb.value));
}

/**
 * Salva alteração em massa de datas
 */
function salvarAlteracaoMassa() {
    const notasSelecionadas = getNotasFaltantesSelecionadas();
    const novaDataEmissao = document.getElementById('novaDataEmissaoMassa').value;
    const novaDataEntrada = document.getElementById('novaDataEntradaMassa').value;
    const observacoes = document.getElementById('observacoesMassa').value;

    if (!novaDataEmissao && !novaDataEntrada) {
        showMessage('Preencha pelo menos uma data para alterar', 'warning');
        return;
    }

    const dados = {
        notas_ids: notasSelecionadas,
        observacoes: observacoes
    };

    if (novaDataEmissao) {
        dados.nova_data_emissao = formatarDataParaBR(novaDataEmissao);
    }

    if (novaDataEntrada) {
        dados.nova_data_entrada = formatarDataParaBR(novaDataEntrada);
    }

    showLoading();

    fetch('/api/auditoria-entrada/alterar-data-massa', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(dados)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage(`${data.total_alteradas} notas alteradas com sucesso`, 'success');
            $('#modalAlteracaoMassa').modal('hide');
            loadTabData('faltantes');
        } else {
            showMessage(data.message || 'Erro ao alterar datas', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao alterar datas em massa:', error);
        showMessage('Erro ao alterar datas em massa', 'error');
    });
}

/**
 * Configura navegação por tributos (auditoria comparativa)
 */
function setupTributoNavigation() {
    const tributoButtons = document.querySelectorAll('[data-tributo]');
    tributoButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remover classe active de todos
            tributoButtons.forEach(btn => btn.classList.remove('active'));

            // Adicionar classe active no clicado
            this.classList.add('active');

            // Carregar dados do tributo
            const tributo = this.getAttribute('data-tributo');

            // NOVA LÓGICA: Para tributos que não são escrituração, usar o novo sistema
            if (tributo !== 'escrituracao') {
                // Usar a função da auditoria comparativa se disponível
                if (typeof carregarAuditoriaComparativaTributo === 'function') {
                    carregarAuditoriaComparativaTributo(tributo);
                } else {
                    loadTributoData(tributo);
                }
            } else {
                loadTributoData(tributo);
            }
        });
    });
}

/**
 * Configura filtros da auditoria
 */
function setupAuditoriaFilters() {
    const toleranciaInput = document.getElementById('tolerancia-escrituracao');
    if (toleranciaInput) {
        const saved = localStorage.getItem('toleranciaEscrituracao');
        if (saved) {
            toleranciaInput.value = saved;
        }
        toleranciaInput.addEventListener('change', function() {
            localStorage.setItem('toleranciaEscrituracao', this.value);
        });
    }
}

/**
 * Configura ações da auditoria
 */
function setupAuditoriaActions() {
    const btnGerarAuditoria = document.getElementById('btn-gerar-auditoria');
    if (btnGerarAuditoria) {
        btnGerarAuditoria.addEventListener('click', gerarAuditoria);
    }

    const btnAprovarAuditoria = document.getElementById('btn-aprovar-auditoria');
    if (btnAprovarAuditoria) {
        btnAprovarAuditoria.addEventListener('click', aprovarAuditoriaSelecionados);
    }
}

/**
 * Carrega dados da auditoria
 */
function loadAuditoriaData() {
    const empresaId = localStorage.getItem('selectedCompany');
    if (empresaId) {
        loadTributoData('escrituracao'); // Carregar aba padrão
    }
}

/**
 * Carrega dados de um tributo específico
 */
function loadTributoData(tributo) {
    const empresaId = localStorage.getItem('selectedCompany');
    const ano = localStorage.getItem('selectedYear');
    const mes = localStorage.getItem('selectedMonth');

    if (!empresaId) {
        showMessage('Selecione uma empresa para visualizar os dados', 'warning');
        return;
    }

    showLoading();

    let url;
    const tolerancia = document.getElementById('tolerancia-escrituracao')?.value || localStorage.getItem('toleranciaEscrituracao');
    if (tributo === 'escrituracao') {
        // Usar rota específica para escrituração
        url = `/api/auditoria-entrada/escrituracao?empresa_id=${empresaId}`;
        if (ano) url += `&ano=${ano}`;
        if (mes) url += `&mes=${mes}`;
        if (tolerancia) url += `&tolerancia=${tolerancia}`;
    } else {
        // Usar rota geral para outros tributos
        url = `/api/auditoria-entrada/listar?empresa_id=${empresaId}`;
        if (ano) url += `&ano=${ano}`;
        if (mes) url += `&mes=${mes}`;
        url += `&tributo=${tributo}`;
    }

    fetch(url, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            renderAuditoriaContent(tributo, data.auditorias || [], data.resumo);
        } else {
            showMessage(data.message || 'Erro ao carregar dados de auditoria', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao carregar dados de auditoria:', error);
        showMessage('Erro ao carregar dados de auditoria', 'error');
    });
}

/**
 * Renderiza conteúdo da auditoria
 */
function renderAuditoriaContent(tributo, auditorias, resumo) {
    const container = document.getElementById('auditoria-content');

    if (tributo === 'escrituracao') {
        renderEscrituracao(container, auditorias, resumo);
    } else {
        renderTributoAuditoria(container, tributo, auditorias);
    }
}

/**
 * Renderiza aba de escrituração
 */
function renderEscrituracao(container, auditorias, resumo) {
    // Cabeçalho com ações em massa
    let cards = '';
    if (resumo) {
        const totalXml = parseFloat(resumo.total_xml || 0).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'});
        const totalSped = parseFloat(resumo.total_sped || 0).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'});
        const totalDif = parseFloat(resumo.valor_divergente || 0).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'});
        cards = `
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="card text-bg-light">
                    <div class="card-body text-center">
                        <h5 class="card-title">XML</h5>
                        <p class="card-text fs-4"><strong>${resumo.notas_xml}</strong> notas<br>${totalXml}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-bg-light">
                    <div class="card-body text-center">
                        <h5 class="card-title">SPED</h5>
                        <p class="card-text fs-4"><strong>${resumo.notas_sped}</strong> notas<br>${totalSped}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-bg-light border-danger">
                    <div class="card-body text-center text-danger">
                        <h5 class="card-title">Divergentes</h5>
                        <p class="card-text fs-4"><strong>${resumo.divergentes}</strong> notas<br>${totalDif}</p>
                    </div>
                </div>
            </div>
        </div>`;
    }

    let html = `
        ${cards}
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5><i class="fas fa-file-invoice"></i> Auditoria de Escrituração</h5>
            <div class="btn-group">
                <button class="btn btn-success btn-sm" id="btn-aprovar-massa" disabled>
                    <i class="fas fa-check"></i> Aprovar Selecionados
                </button>
                <button class="btn btn-info btn-sm" onclick="gerarAuditoriaEscrituracao()">
                    <i class="fas fa-sync"></i> Gerar Auditoria de Escrituração
                </button>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-hover" id="escrituracao-table">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="select-all-escrituracao" onchange="toggleSelectAllEscrituracao(this)">
                        </th>
                        <th>Número NF</th>
                        <th>Participante</th>
                        <th>Data Entrada</th>
                        <th>Valor XML</th>
                        <th>Valor SPED</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
    `;

    if (auditorias && auditorias.length > 0) {
        auditorias.forEach(auditoria => {
            const dataEntrada = auditoria.xml_data_entrada ?
            formatarDataParaBR(auditoria.xml_data_entrada) : '-';

            const valorXML = auditoria.xml_valor_total_formatado ||
                (auditoria.xml_valor_total_nota ? parseFloat(auditoria.xml_valor_total_nota).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'}) : '-');

            const valorSPED = auditoria.sped_valor_total_formatado ||
                (auditoria.sped_valor_total_nota ? parseFloat(auditoria.sped_valor_total_nota).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'}) : '-');

            // Determinar status e cor
            let statusClass = 'secondary';
            let statusText = 'Pendente';
            let statusIcon = 'fas fa-clock';

            if (auditoria.status_conformidade === 'conforme') {
                statusClass = 'success';
                statusText = 'Conforme';
                statusIcon = 'fas fa-check';
            } else if (auditoria.status_conformidade === 'divergente') {
                if (auditoria.status_escrituracao === 'aprovado') {
                    statusClass = 'warning';
                    statusText = 'Aprovado';
                    statusIcon = 'fas fa-check-circle';
                } else {
                    statusClass = 'danger';
                    statusText = 'Divergente';
                    statusIcon = 'fas fa-exclamation-triangle';
                }
            }

            // Cores apenas nos valores, não na linha toda
            let valorXMLClass = '';
            let valorSPEDClass = '';
            if (auditoria.status_conformidade === 'conforme') {
                valorXMLClass = 'text-success';
                valorSPEDClass = 'text-success';
            } else if (auditoria.status_conformidade === 'divergente' && auditoria.status_escrituracao !== 'aprovado') {
                valorXMLClass = 'text-danger';
                valorSPEDClass = 'text-danger';
            }

            html += `
                <tr>
                    <td>
                        <input type="checkbox" class="escrituracao-checkbox" value="${auditoria.id}"
                               onchange="toggleEscrituracaoSelection(this)">
                    </td>
                    <td><strong>${auditoria.numero_nf || '-'}</strong></td>
                    <td>
                        <div>
                            <strong>${auditoria.participante_nome || 'N/A'}</strong><br>
                            <small class="text-muted">${auditoria.participante_cnpj || '-'}</small>
                        </div>
                    </td>
                    <td>${dataEntrada}</td>
                    <td><strong class="${valorXMLClass}">${valorXML}</strong></td>
                    <td><strong class="${valorSPEDClass}">${valorSPED}</strong></td>
                    <td>
                        <span class="badge bg-${statusClass}">
                            <i class="${statusIcon}"></i> ${statusText}
                        </span>
                        ${(() => {const t = parseFloat(localStorage.getItem('toleranciaEscrituracao') || 0.01); return auditoria.percentual_divergencia && Math.abs(auditoria.divergencia_valor) > t ? `<br><small class="text-muted">${auditoria.percentual_divergencia.toFixed(2)}% divergência</small>` : ''})()}
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-info" onclick="verDetalhesComparativo(${auditoria.id})" title="Ver Detalhes">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${auditoria.status_conformidade === 'divergente' && auditoria.status_escrituracao !== 'aprovado' ?
                                `<button class="btn btn-outline-success" onclick="aprovarEscrituracao([${auditoria.id}])" title="Aprovar">
                                    <i class="fas fa-check"></i>
                                </button>` : ''}
                        </div>
                    </td>
                </tr>
            `;
        });
    } else {
        html += `
            <tr>
                <td colspan="8" class="text-center text-muted">
                    <i class="fas fa-info-circle"></i> Nenhuma auditoria de escrituração encontrada
                </td>
            </tr>
        `;
    }

    html += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = html;

    // Configurar eventos
    setupEscrituracaoEvents();

    // Inicializar DataTable com configurações específicas
    if ($.fn.DataTable && auditorias && auditorias.length > 0) {
        $('#escrituracao-table').DataTable({
            language: {
                url: '/static/js/vendor/datatables/pt-BR.json'
            },
            responsive: true,
            order: [[3, 'desc']], // Ordenar por data de entrada
            pageLength: 25,
            columnDefs: [
                { orderable: false, targets: [0, 7] }, // Checkbox e ações não ordenáveis
                { searchable: false, targets: [0, 7] }
            ]
        });
    }
}

/**
 * Renderiza auditoria de tributo específico
 */
function renderTributoAuditoria(container, tributo, auditorias) {
    // Implementar renderização específica por tributo
    container.innerHTML = `
        <div class="alert alert-info">
            <h5>Auditoria de ${tributo.toUpperCase()}</h5>
            <p>Funcionalidade em desenvolvimento. ${auditorias.length} registros encontrados.</p>
        </div>
    `;
}

/**
 * Aprova auditorias selecionadas
 */
function aprovarAuditoriaSelecionados() {
    const selectedIds = Array.from(document.querySelectorAll('.auditoria-checkbox:checked'))
                            .map(cb => parseInt(cb.value));

    if (selectedIds.length === 0) {
        showMessage('Selecione pelo menos uma auditoria para aprovar', 'warning');
        return;
    }

    // Implementar aprovação
    console.log('Aprovando auditorias:', selectedIds);
    showMessage(`${selectedIds.length} auditorias aprovadas`, 'success');
}

/**
 * Toggle seleção de todas as auditorias
 */
function toggleSelectAllAuditoria(checkbox) {
    const auditoriaCheckboxes = document.querySelectorAll('.auditoria-checkbox');
    auditoriaCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    updateAuditoriaActionButtons();
}

/**
 * Toggle seleção de auditoria individual
 */
function toggleAuditoriaSelection(checkbox) {
    updateAuditoriaActionButtons();
}

/**
 * Atualiza botões de ação da auditoria
 */
function updateAuditoriaActionButtons() {
    const selectedCount = document.querySelectorAll('.auditoria-checkbox:checked').length;
    const btnAprovar = document.getElementById('btn-aprovar-auditoria');

    if (btnAprovar) {
        btnAprovar.disabled = selectedCount === 0;
        btnAprovar.textContent = selectedCount > 0 ?
            `Aprovar Selecionados (${selectedCount})` :
            'Aprovar Selecionados';
    }
}

/**
 * Funções que estavam faltando
 */

// Função para ver detalhes do XML
function verDetalhesXML(xmlId) {
    console.log('Ver detalhes do XML:', xmlId);

    showLoading();

    fetch(`/api/auditoria-entrada/xml-detalhes/${xmlId}`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showDetalhesModal(data);
        } else {
            showMessage(data.message || 'Erro ao carregar detalhes do XML', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao carregar detalhes do XML:', error);
        showMessage('Erro ao carregar detalhes do XML', 'error');
    });
}

// Função para mostrar modal de detalhes
function showDetalhesModal(data) {
    const xml = data.xml;
    const produtos = data.produtos;
    const totais = data.totais;
    const estatisticas = data.estatisticas;

    // Criar modal dinamicamente
    const modalHtml = `
        <div class="modal fade" id="detalhesXMLModal" tabindex="-1" aria-labelledby="detalhesXMLModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="detalhesXMLModalLabel">
                            <i class="fas fa-file-alt"></i> Detalhes do XML - NF ${xml.numero_nf || 'N/A'}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <!-- Informações Gerais -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-info-circle"></i> Informações da Nota</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>Número:</strong> ${xml.numero_nf || 'N/A'}</p>
                                        <p><strong>Data Emissão:</strong> ${xml.data_emissao ? formatarDataParaBR(xml.data_emissao) : 'N/A'}</p>
                                        <p><strong>Data Entrada:</strong> ${xml.data_entrada ? formatarDataParaBR(xml.data_entrada) : 'N/A'}</p>
                                        <p><strong>Tipo:</strong> <span class="badge bg-${xml.tipo_nota === '0' ? 'success' : 'primary'}">${xml.tipo_nota === '0' ? 'Entrada' : 'Saída'}</span></p>
                                        <p><strong>Status:</strong> <span class="badge bg-info">${xml.status_validacao || 'Pendente'}</span></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-building"></i> Emitente</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>Razão Social:</strong> ${xml.razao_social_emitente || 'N/A'}</p>
                                        <p><strong>CNPJ:</strong> ${xml.cnpj_emitente || 'N/A'}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Totais -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-calculator"></i> Totais da Nota</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <div class="text-center">
                                                    <h5 class="text-primary">${totais.valor_total_nota.toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})}</h5>
                                                    <small class="">Valor Total</small>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="text-center">
                                                    <h6 class="text-info">${totais.total_icms.toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})}</h6>
                                                    <small class="">ICMS</small>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="text-center">
                                                    <h6 class="text-success">${totais.total_icms_st.toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})}</h6>
                                                    <small class="">ICMS-ST</small>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="text-center">
                                                    <h6 class="text-warning">${totais.total_ipi.toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})}</h6>
                                                    <small class="">IPI</small>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="text-center">
                                                    <h6 class="text-danger">${totais.total_pis.toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})}</h6>
                                                    <small class="">PIS</small>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="text-center">
                                                    <h6 class="text-secondary">${totais.total_cofins.toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})}</h6>
                                                    <small class="">COFINS</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Produtos -->
                        <div class="">
                            <div class="card-header">
                                <h6><i class="fas fa-boxes"></i> Produtos (${estatisticas.total_produtos})</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>Produto</th>
                                                <th>CFOP</th>
                                                <th>NCM</th>
                                                <th>Qtd</th>
                                                <th>Valor Unit.</th>
                                                <th>Valor Total</th>
                                                <th>ICMS</th>
                                                <th>IPI</th>
                                                <th>PIS</th>
                                                <th>COFINS</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${produtos.map(produto => `
                                                <tr>
                                                    <td>
                                                        <strong>${produto.produto_nome}</strong><br>
                                                        <small class="text-muted">${produto.cliente_nome}</small>
                                                    </td>
                                                    <td>${produto.item.cfop || '-'}</td>
                                                    <td>${produto.item.ncm || '-'}</td>
                                                    <td>${produto.item.quantidade || '-'}</td>
                                                    <td>${produto.item.valor_unitario ? parseFloat(produto.item.valor_unitario).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'}) : '-'}</td>
                                                    <td>${produto.item.valor_total ? parseFloat(produto.item.valor_total).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'}) : '-'}</td>
                                                    <td>
                                                        ${produto.tributo && produto.tributo.icms_valor ?
                                                            `${parseFloat(produto.tributo.icms_valor).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})}<br><small>(${produto.tributo.icms_aliquota || 0}%)</small>` :
                                                            '-'
                                                        }
                                                    </td>
                                                    <td>
                                                        ${produto.tributo && produto.tributo.ipi_valor ?
                                                            `${parseFloat(produto.tributo.ipi_valor).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})}<br><small>(${produto.tributo.ipi_aliquota || 0}%)</small>` :
                                                            '-'
                                                        }
                                                    </td>
                                                    <td>
                                                        ${produto.tributo && produto.tributo.pis_valor ?
                                                            `${parseFloat(produto.tributo.pis_valor).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})}<br><small>(${produto.tributo.pis_aliquota || 0}%)</small>` :
                                                            '-'
                                                        }
                                                    </td>
                                                    <td>
                                                        ${produto.tributo && produto.tributo.cofins_valor ?
                                                            `${parseFloat(produto.tributo.cofins_valor).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})}<br><small>(${produto.tributo.cofins_aliquota || 0}%)</small>` :
                                                            '-'
                                                        }
                                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                        <button type="button" class="btn btn-primary" onclick="exportarDetalhesXML(${xml.id})">
                            <i class="fas fa-download"></i> Exportar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remover modal existente se houver
    const existingModal = document.getElementById('detalhesXMLModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Adicionar modal ao DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Mostrar modal
    const modal = new bootstrap.Modal(document.getElementById('detalhesXMLModal'));
    modal.show();

    // Remover modal do DOM quando fechado
    document.getElementById('detalhesXMLModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Função para exportar detalhes do XML
function exportarDetalhesXML(xmlId) {
    console.log('Exportar detalhes do XML:', xmlId);
    showMessage('Funcionalidade de exportação em desenvolvimento', 'info');
}

// Função para editar nota faltante
function editarNotaFaltante(notaId) {
    console.log('Editar nota faltante:', notaId);
    showAlterarDataModal([notaId]);
}

// Função para marcar nota como encontrada
function marcarEncontrado(notaId) {
    console.log('Marcar como encontrado:', notaId);
    showMessage('Nota marcada como encontrada', 'success');
}

// Função para marcar nota como cancelada
function marcarCancelado(notaId) {
    console.log('Marcar como cancelado:', notaId);
    showMessage('Nota marcada como cancelada', 'warning');
}

// Função para mostrar modal de alteração de data
function showAlterarDataModal(ids = null) {
    const idsParaAlterar = ids || selectedItems;

    if (idsParaAlterar.length === 0) {
        showMessage('Selecione pelo menos um item para alterar a data', 'warning');
        return;
    }

    const novaData = prompt(`Alterar data de ${idsParaAlterar.length} item(s).\nDigite a nova data (YYYY-MM-DD):`);

    if (novaData) {
        alterarDataItems(idsParaAlterar, novaData);
    }
}

// Função para mostrar modal de exclusão
function showExcluirModal() {
    if (selectedItems.length === 0) {
        showMessage('Selecione pelo menos um item para cancelar', 'warning');
        return;
    }

    const motivo = prompt(`Cancelar ${selectedItems.length} item(s).\nDigite o motivo do cancelamento:`);

    if (motivo) {
        cancelarItems(selectedItems, motivo);
    }
}

// Função para alterar data dos itens
function alterarDataItems(ids, novaData) {
    showLoading();

    fetch('/api/auditoria-entrada/alterar-data-xml', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            xml_ids: ids,
            nova_data: novaData,
            motivo: 'Alteração via interface de gestão'
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage(data.message, 'success');
            loadTabData(currentTab); // Recarregar dados
            selectedItems = []; // Limpar seleção
            updateBulkActionButtons();
        } else {
            showMessage(data.message || 'Erro ao alterar datas', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao alterar datas:', error);
        showMessage('Erro ao alterar datas', 'error');
    });
}

// Função para cancelar itens
function cancelarItems(ids, motivo) {
    showLoading();

    fetch('/api/auditoria-entrada/excluir-xml', {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            xml_ids: ids,
            motivo: motivo
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage(data.message, 'success');
            loadTabData(currentTab); // Recarregar dados
            selectedItems = []; // Limpar seleção
            updateBulkActionButtons();
        } else {
            showMessage(data.message || 'Erro ao cancelar itens', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao cancelar itens:', error);
        showMessage('Erro ao cancelar itens', 'error');
    });
}

// Função para mostrar loading toast
function showLoading() {
    const toastElement = document.querySelector('#loading-toast .toast');
    if (toastElement) {
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
    }
}

// Função para esconder loading toast
function hideLoading() {
    const toastElement = document.querySelector('#loading-toast .toast');
    if (toastElement) {
        const toast = bootstrap.Toast.getInstance(toastElement);
        if (toast) {
            toast.hide();
        }
    }
}

// ===== FUNÇÕES ESPECÍFICAS PARA ESCRITURAÇÃO =====

/**
 * Configura eventos específicos da escrituração
 */
function setupEscrituracaoEvents() {
    // Botão de aprovação em massa
    const btnAprovarMassa = document.getElementById('btn-aprovar-massa');
    if (btnAprovarMassa) {
        btnAprovarMassa.addEventListener('click', aprovarEscrituracaoMassa);
    }
}

/**
 * Toggle de seleção individual de escrituração
 */
function toggleEscrituracaoSelection(checkbox) {
    updateEscrituracaoButtons();
}

/**
 * Toggle de seleção de todos os itens de escrituração
 */
function toggleSelectAllEscrituracao(masterCheckbox) {
    const checkboxes = document.querySelectorAll('.escrituracao-checkbox');
    checkboxes.forEach(cb => {
        cb.checked = masterCheckbox.checked;
    });
    updateEscrituracaoButtons();
}

/**
 * Atualiza estado dos botões baseado na seleção
 */
function updateEscrituracaoButtons() {
    const selectedCheckboxes = document.querySelectorAll('.escrituracao-checkbox:checked');
    const btnAprovarMassa = document.getElementById('btn-aprovar-massa');

    if (btnAprovarMassa) {
        btnAprovarMassa.disabled = selectedCheckboxes.length === 0;
        btnAprovarMassa.textContent = selectedCheckboxes.length > 0 ?
            `Aprovar ${selectedCheckboxes.length} Selecionados` : 'Aprovar Selecionados';
    }
}

/**
 * Obtém IDs das auditorias selecionadas
 */
function getSelectedEscrituracaoIds() {
    const checkboxes = document.querySelectorAll('.escrituracao-checkbox:checked');
    return Array.from(checkboxes).map(cb => parseInt(cb.value));
}

/**
 * Aprova escrituração em massa
 */
function aprovarEscrituracaoMassa() {
    const selectedIds = getSelectedEscrituracaoIds();
    if (selectedIds.length === 0) {
        showMessage('Selecione pelo menos uma auditoria para aprovar', 'warning');
        return;
    }

    aprovarEscrituracao(selectedIds);
}

/**
 * Aprova escrituração (individual ou em massa)
 */
function aprovarEscrituracao(auditoriaIds) {
    // Verificar se há divergências que precisam de justificativa
    const auditoriasDivergentes = [];

    auditoriaIds.forEach(id => {
        const row = document.querySelector(`input[value="${id}"]`).closest('tr');
        const statusBadge = row.querySelector('.badge');
        if (statusBadge && statusBadge.textContent.includes('Divergente')) {
            auditoriasDivergentes.push(id);
        }
    });

    if (auditoriasDivergentes.length > 0) {
        // Mostrar modal de justificativa
        showJustificativaModal(auditoriaIds);
    } else {
        // Aprovar diretamente
        executarAprovacao(auditoriaIds, '');
    }
}

/**
 * Mostra modal de justificativa para divergências
 */
function showJustificativaModal(auditoriaIds) {
    const modalHtml = `
        <div class="modal fade" id="modalJustificativa" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Justificativa para Aprovação</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Atenção:</strong> Foram encontradas divergências entre os valores XML e SPED.
                            É necessário fornecer uma justificativa para a aprovação.
                        </div>
                        <div class="form-group">
                            <label for="justificativaTexto">Justificativa:</label>
                            <textarea class="form-control" id="justificativaTexto" rows="4"
                                      placeholder="Digite a justificativa para aprovação das divergências..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-success" onclick="confirmarAprovacao(${JSON.stringify(auditoriaIds)})">
                            <i class="fas fa-check"></i> Aprovar com Justificativa
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remover modal existente
    const existingModal = document.getElementById('modalJustificativa');
    if (existingModal) {
        existingModal.remove();
    }

    // Adicionar modal ao DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Mostrar modal
    const modal = new bootstrap.Modal(document.getElementById('modalJustificativa'));
    modal.show();
}

/**
 * Confirma aprovação com justificativa
 */
function confirmarAprovacao(auditoriaIds) {
    const justificativa = document.getElementById('justificativaTexto').value.trim();

    if (!justificativa) {
        showMessage('Justificativa é obrigatória para divergências', 'warning');
        return;
    }

    // Fechar modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('modalJustificativa'));
    modal.hide();

    // Executar aprovação
    executarAprovacao(auditoriaIds, justificativa);
}

/**
 * Executa a aprovação das auditorias
 */
function executarAprovacao(auditoriaIds, justificativa) {
    showLoading();

    fetch('/api/auditoria-entrada/aprovar-escrituracao', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            auditoria_ids: auditoriaIds,
            justificativa: justificativa
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage(data.message, 'success');
            // Recarregar dados
            loadTributoData('escrituracao');
        } else {
            showMessage(data.message || 'Erro ao aprovar escrituração', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao aprovar escrituração:', error);
        showMessage('Erro ao aprovar escrituração', 'error');
    });
}

/**
 * Ver detalhes comparativos entre XML e SPED
 */
function verDetalhesComparativo(auditoriaId) {
    showLoading();

    fetch(`/api/auditoria-entrada/detalhes-comparativo/${auditoriaId}`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showDetalhesComparativoModal(data);
        } else {
            showMessage(data.message || 'Erro ao carregar detalhes', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao carregar detalhes:', error);
        showMessage('Erro ao carregar detalhes', 'error');
    });
}

/**
 * Mostra modal com detalhes comparativos XML vs SPED
 */
function showDetalhesComparativoModal(data) {
    const auditoria = data.auditoria;
    const xml = data.xml;
    const sped = data.sped;
    const empresa = data.empresa;
    
    // Debug: Verificar estrutura dos dados recebidos
    console.log('Dados XML recebidos:', xml);
    console.log('Dados SPED recebidos:', sped);

    // Calcular totais
    const xmlTotal = xml.dados_gerais && xml.dados_gerais.valor_total_nf
        ? parseFloat(xml.dados_gerais.valor_total_nf)
        : xml.itens.reduce((sum, item) => sum + (parseFloat(item.valor_total) || 0), 0);
    const spedTotal = sped.dados_gerais && sped.dados_gerais.valor_documento
        ? parseFloat(sped.dados_gerais.valor_documento)
        : sped.itens.reduce((sum, item) => sum + (parseFloat(item.valor_item) || 0), 0);
    const divergencia = Math.abs(xmlTotal - spedTotal);
    const percentualDivergencia = xmlTotal > 0 ? (divergencia / xmlTotal * 100) : 0;

    const modalHtml = `
        <div class="modal fade" id="modalDetalhesComparativo" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-balance-scale"></i>
                            Detalhes Comparativos - NF ${auditoria.numero_nf}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <!-- Informações Gerais -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0"><i class="fas fa-file-alt"></i> Dados Gerais</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>Empresa:</strong> ${empresa.nome}</p>
                                        <p><strong>CNPJ:</strong> ${empresa.cnpj}</p>
                                        <p><strong>Chave NF:</strong> ${auditoria.chave_nf}</p>
                                        <p><strong>Número NF:</strong> ${auditoria.numero_nf}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header ${divergencia <= 0.01 ? 'bg-success' : 'bg-danger'} text-white">
                                        <h6 class="mb-0"><i class="fas fa-calculator"></i> Resumo Comparativo</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>Valor Total XML:</strong> ${xmlTotal.toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})}</p>
                                        <p><strong>Valor Total SPED:</strong> ${spedTotal.toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})}</p>
                                        <p><strong>Divergência:</strong> ${divergencia.toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})}</p>
                                        <p><strong>Percentual:</strong> ${percentualDivergencia.toFixed(2)}%</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Comparação Lado a Lado -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-file-code"></i> Dados XML</h6>
                                    </div>
                                    <div class="card-body">
                                        ${xml.dados_gerais ? `
                                            <p><strong>Data Emissão:</strong> ${xml.dados_gerais.data_emissao ? formatarDataParaBR(xml.dados_gerais.data_emissao) : '-'}</p>
                                            <p><strong>Data Entrada:</strong> ${xml.dados_gerais.data_entrada ? formatarDataParaBR(xml.dados_gerais.data_entrada) : '-'}</p>
                                            <p><strong>Emitente:</strong> ${xml.dados_gerais.razao_social_emitente || '-'}</p>
                                        ` : '<p class="text-muted">Dados XML não disponíveis</p>'}

                                        <h6 class="mt-3">Itens (${xml.itens.length}):</h6>
                                        <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Produto</th>
                                                        <th>Qtd</th>
                                                        <th>Valor Unit</th>
                                                        <th>Valor Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    ${xml.itens.map(item => `
                                                        <tr>
                                                            <td>${item.descricao_produto || '-'}</td>
                                                            <td>${item.quantidade || '-'}</td>
                                                            <td>${item.valor_unitario ? parseFloat(item.valor_unitario).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'}) : '-'}</td>
                                                            <td>${item.valor_total ? parseFloat(item.valor_total).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'}) : '-'}</td>
                                                        </tr>
                                                    `).join('')}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0"><i class="fas fa-database"></i> Dados SPED</h6>
                                    </div>
                                    <div class="card-body">
                                        ${sped.dados_gerais ? `
                                            <p><strong>Data Documento:</strong> ${sped.dados_gerais.data_documento ? formatarDataParaBR(sped.dados_gerais.data_documento) : '-'}</p>
                                            <p><strong>Data Entrada/Saída:</strong> ${sped.dados_gerais.data_entrada_saida ? formatarDataParaBR(sped.dados_gerais.data_entrada_saida) : '-'}</p>
                                            <p><strong>Emitente:</strong> ${sped.dados_gerais.razao_social || '-'}</p>
                                        ` : '<p class="text-muted">Dados SPED não disponíveis</p>'}

                                        <h6 class="mt-3">Itens (${sped.itens.length}):</h6>
                                        <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Produto</th>
                                                        <th>Qtd</th>
                                                        <th>Valor Unit</th>
                                                        <th>Valor Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    ${sped.itens.map(item => `
                                                        <tr>
                                                            <td>${item.descricao || '-'}</td>
                                                            <td>${item.quantidade || '-'}</td>
                                                            <td>${item.valor_unitario ? parseFloat(item.valor_unitario).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'}) : '-'}</td>
                                                            <td>${item.valor_item ? parseFloat(item.valor_item).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'}) : '-'}</td>
                                                        </tr>
                                                    `).join('')}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        ${divergencia > 0.01 ? `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="card border-warning">
                                        <div class="card-header bg-warning text-dark">
                                            <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Justificativa para Aprovação</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-group">
                                                <textarea class="form-control" id="justificativaModal" rows="3"
                                                          placeholder="Digite a justificativa para aprovação desta divergência..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                        ${divergencia > 0.01 ? `
                            <button type="button" class="btn btn-success" onclick="aprovarEscrituracaoModal(${auditoria.id})">
                                <i class="fas fa-check"></i> Aprovar com Justificativa
                            </button>
                        ` : `
                            <button type="button" class="btn btn-success" onclick="aprovarEscrituracaoModal(${auditoria.id})">
                                <i class="fas fa-check"></i> Aprovar
                            </button>
                        `}
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remover modal existente
    const existingModal = document.getElementById('modalDetalhesComparativo');
    if (existingModal) {
        existingModal.remove();
    }

    // Adicionar modal ao DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Mostrar modal
    const modal = new bootstrap.Modal(document.getElementById('modalDetalhesComparativo'));
    modal.show();
}

/**
 * Aprova escrituração a partir do modal de detalhes
 */
function aprovarEscrituracaoModal(auditoriaId) {
    const justificativaElement = document.getElementById('justificativaModal');
    const justificativa = justificativaElement ? justificativaElement.value.trim() : '';

    // Fechar modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('modalDetalhesComparativo'));
    modal.hide();

    // Executar aprovação
    executarAprovacao([auditoriaId], justificativa);
}
