# Otimização da Importação de XMLs

## Situação Atual

- **Problema**: 800 arquivos XML demoraram 20 minutos para importação (1,5 segundos por arquivo)
- **Meta**: Reduzir significativamente o tempo de importação para grandes volumes
- **Contexto**: Empresas podem ter até 1000+ XMLs por mês

## Análise do Processo Atual

### Gargalos Identificados

1. **Processamento Sequencial**: Cada XML é processado individualmente
2. **Múltiplas Consultas ao Banco**: Para cada XML são feitas várias consultas:
   - Verificação de empresa existente
   - Busca/criação de cliente
   - Busca/criação de produto
   - Criação de cenários (múltiplas consultas por tributo)
   - Inserção de tributos
3. **Transações Individuais**: Cada XML gera uma transação separada
4. **Validações Repetitivas**: Mesmas validações são executadas para cada arquivo
5. **Criação de Cenários**: Processo complexo que envolve múltiplas consultas e comparações

## Opções de Otimização

### 1. **Processamento em Lote (Batch Processing)** ⭐⭐⭐⭐⭐
**Impacto**: Alto | **Complexidade**: Média

#### Implementação:
- Processar múltiplos XMLs em uma única transação
- Agrupar inserções por tipo (clientes, produtos, tributos)
- Usar `bulk_insert_mappings()` do SQLAlchemy

#### Benefícios:
- Redução de 80-90% no tempo de transação
- Menor overhead de conexão com banco
- Melhor utilização de recursos

#### Código Exemplo:
```python
# Em vez de inserir um por vez
for xml in xmls:
    tributo = Tributo(...)
    db.session.add(tributo)
    db.session.commit()

# Inserir em lote
tributos_data = []
for xml in xmls:
    tributos_data.append({
        'empresa_id': xml.empresa_id,
        'cliente_id': xml.cliente_id,
        # ... outros campos
    })
db.session.bulk_insert_mappings(Tributo, tributos_data)
db.session.commit()
```

### 2. **Cache de Entidades Frequentes** ⭐⭐⭐⭐⭐
**Impacto**: Alto | **Complexidade**: Baixa

#### Implementação:
- Cache em memória para clientes e produtos já processados
- Cache de cenários existentes
- Evitar consultas repetitivas ao banco

#### Benefícios:
- Redução de 70-80% nas consultas SELECT
- Processamento mais rápido de XMLs similares

#### Código Exemplo:
```python
class XMLImportService:
    def __init__(self):
        self.cache_clientes = {}  # {cnpj: cliente_id}
        self.cache_produtos = {}  # {(codigo, descricao): produto_id}
        self.cache_cenarios = {}  # {hash_cenario: cenario_id}
```

### 3. **Processamento Paralelo** ⭐⭐⭐⭐
**Impacto**: Alto | **Complexidade**: Alta

#### Implementação:
- Usar ThreadPoolExecutor ou ProcessPoolExecutor
- Dividir XMLs em chunks para processamento paralelo
- Controlar concorrência para evitar deadlocks

#### Benefícios:
- Utilização de múltiplos cores do processador
- Redução proporcional ao número de threads/processos

#### Considerações:
- Cuidado com locks no banco de dados
- Gerenciamento de conexões de banco
- Controle de memória

### 4. **Otimização de Consultas SQL** ⭐⭐⭐⭐
**Impacto**: Médio-Alto | **Complexidade**: Média

#### Implementação:
- Usar JOINs em vez de consultas separadas
- Índices otimizados para consultas frequentes
- Consultas preparadas (prepared statements)

#### Benefícios:
- Redução de 50-60% no tempo de consulta
- Menor tráfego de rede com banco

#### Índices Sugeridos:
```sql
-- Para busca de clientes
CREATE INDEX idx_cliente_cnpj_empresa ON cliente(cnpj, empresa_id);

-- Para busca de produtos
CREATE INDEX idx_produto_codigo_empresa ON produto(codigo, empresa_id);

-- Para cenários
CREATE INDEX idx_cenario_lookup ON cenario_icms(cliente_id, produto_id, cfop, ncm);
```

### 5. **Pré-processamento e Validação** ⭐⭐⭐
**Impacto**: Médio | **Complexidade**: Baixa

#### Implementação:
- Validar todos os XMLs antes de iniciar importação
- Separar XMLs válidos dos inválidos
- Processar apenas XMLs válidos

#### Benefícios:
- Evita rollbacks custosos
- Processamento mais limpo
- Melhor controle de erros

### 6. **Otimização da Criação de Cenários** ⭐⭐⭐⭐
**Impacto**: Alto | **Complexidade**: Média

#### Problema Atual:
- Para cada tributo, são feitas múltiplas consultas para verificar cenários existentes
- Criação individual de cada cenário

#### Solução:
- Cache de cenários existentes
- Criação em lote de novos cenários
- Otimização das consultas de verificação

### 7. **Configuração de Banco de Dados** ⭐⭐⭐
**Impacto**: Médio | **Complexidade**: Baixa

#### Implementação:
- Ajustar parâmetros do PostgreSQL:
  - `shared_buffers`
  - `work_mem`
  - `maintenance_work_mem`
  - `checkpoint_segments`

#### Benefícios:
- Melhor performance geral do banco
- Menos I/O de disco

## Recomendações Prioritárias

### Fase 1 - Implementação Imediata (Ganho: 60-70%)
1. **Cache de Entidades** - Implementar cache para clientes, produtos e cenários
2. **Processamento em Lote** - Agrupar inserções por tipo
3. **Otimização de Consultas** - Adicionar índices essenciais

### Fase 2 - Implementação Média (Ganho adicional: 20-30%)
4. **Pré-processamento** - Validar XMLs antes da importação
5. **Otimização de Cenários** - Melhorar processo de criação de cenários

### Fase 3 - Implementação Avançada (Ganho adicional: 10-20%)
6. **Processamento Paralelo** - Implementar threads/processos paralelos
7. **Configuração de Banco** - Otimizar parâmetros do PostgreSQL

## Estimativa de Resultados

### Cenário Atual:
- 800 XMLs = 20 minutos (1,5s por XML)

### Após Otimizações:
- **Fase 1**: 800 XMLs = 6-8 minutos (0,45-0,6s por XML)
- **Fase 2**: 800 XMLs = 4-5 minutos (0,3-0,4s por XML)
- **Fase 3**: 800 XMLs = 2-3 minutos (0,15-0,2s por XML)

## Implementação Sugerida

### 1. Começar com Cache (Mais Simples)
```python
class OptimizedXMLImportService:
    def __init__(self):
        self.cliente_cache = {}
        self.produto_cache = {}
        self.cenario_cache = {}
    
    def get_or_create_cliente(self, cnpj, dados_cliente):
        if cnpj in self.cliente_cache:
            return self.cliente_cache[cnpj]
        # ... lógica de criação
        self.cliente_cache[cnpj] = cliente
        return cliente
```

### 2. Implementar Processamento em Lote
```python
def import_xml_batch(self, xml_files):
    # Processar todos os XMLs
    tributos_data = []
    for xml_file in xml_files:
        # ... processar XML
        tributos_data.extend(xml_tributos)
    
    # Inserir tudo de uma vez
    db.session.bulk_insert_mappings(Tributo, tributos_data)
    db.session.commit()
```

## Alterações Implementadas

### 1. **Correções no Sistema de Notas Faltantes** ✅

#### Notas Faltantes por Pulos de Numeração:
- **ANTES**: Calculava pulos para entrada e saída
- **DEPOIS**: Apenas para notas de saída (tipo_nota = '1')
- **MOTIVO**: Notas de entrada têm numeração aleatória

#### Notas Faltantes XML vs SPED:
- **ANTES**: Comparava XML vs SPED para entrada e saída
- **DEPOIS**: Apenas para notas de entrada (tipo_nota = '0')
- **MOTIVO**: Não existe SPED de saída

#### Primeira Importação:
- **NOVO**: Detecta se é primeira importação da empresa
- **LÓGICA**: Se não há notas anteriores com números menores, não considera como pulos
- **EXEMPLO**: Primeira nota 21254 não gera 21253 notas faltantes

### 2. **Otimização na Criação de Cenários** ✅

#### Cenários apenas para Saída:
- **ANTES**: Criava cenários para entrada e saída
- **DEPOIS**: Apenas para notas de saída (tipo_operacao = '1')
- **MOTIVO**: Notas de entrada são apenas para controle
- **IMPACTO**: Redução de ~50% na criação de cenários

## Monitoramento

### Métricas a Acompanhar:
- Tempo total de importação
- Tempo por XML
- Número de consultas SQL
- Uso de memória
- Uso de CPU

### Ferramentas:
- Logs detalhados com timestamps
- Profiling do SQLAlchemy
- Monitoramento de recursos do sistema

## Próximos Passos para Otimização

### Implementação Prioritária (Próxima Sprint):

1. **Cache de Entidades** - Implementar imediatamente
2. **Processamento em Lote** - Maior impacto na performance
3. **Índices Otimizados** - Melhoria rápida e efetiva

### Código de Exemplo - Cache Simples:
```python
class OptimizedXMLImportService(XMLImportService):
    def __init__(self, empresa_id, escritorio_id, usuario_id):
        super().__init__(empresa_id, escritorio_id, usuario_id)
        self.cache_clientes = {}
        self.cache_produtos = {}

    def get_or_create_cliente_cached(self, cnpj, dados_cliente):
        if cnpj in self.cache_clientes:
            return self.cache_clientes[cnpj]

        cliente = self.get_or_create_cliente(cnpj, dados_cliente)
        self.cache_clientes[cnpj] = cliente
        return cliente
```
