/*
 * Dark Mode Enhancements CSS - Auditoria Fiscal
 * Estilos específicos para melhorar o contraste e a aparência do modo escuro
 */

/* Variáveis de cores para o modo escuro */
body.dark-theme {
    /* Cores de fundo principais */
    --dark-bg-primary: #121212;
    --dark-bg-secondary: #1e1e1e;
    --dark-bg-tertiary: #2d2d2d;

    /* Cores de texto */
    --dark-text-primary: #e0e0e0;
    --dark-text-secondary: #b0b0b0;
    --dark-text-muted: #909090;

    /* Cores de borda */
    --dark-border-primary: #444444;
    --dark-border-secondary: #333333;

    /* Cores de destaque */
    --dark-highlight-primary: rgba(59, 130, 246, 0.2);
    --dark-highlight-secondary: rgba(59, 130, 246, 0.1);

    /* Cores de hover */
    --dark-hover-bg: #333333;

    /* Cores específicas para tabelas */
    --dark-table-header-bg: #252525;
    --dark-table-row-odd: #2a2a2a;
    --dark-table-row-even: #1e1e1e;
    --dark-table-row-hover: #333333;
}

/* Estilos base para o modo escuro */
body.dark-theme {
    color: var(--dark-text-primary);
    background-color: var(--dark-bg-primary);
}

/* Melhorias para tabelas no modo escuro */
body.dark-theme .table {
    color: var(--dark-text-primary);
    border-color: var(--dark-border-primary);
    background-color: var(--dark-bg-secondary);
}

body.dark-theme .table th,
body.dark-theme .table td {
    border-color: var(--dark-border-primary);
    color: var(--dark-text-primary);
    background-color: var(--dark-bg-secondary);
}

body.dark-theme .table thead th {
    color: var(--dark-text-primary);
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
}

body.dark-theme .table tbody tr {
    background-color: var(--dark-bg-secondary);
}

body.dark-theme .table tbody tr:nth-of-type(odd) {
    background-color: var(--dark-bg-tertiary);
}

body.dark-theme .table tbody tr:hover {
    background-color: var(--dark-hover-bg) !important;
}

/* Estilos específicos para DataTables */
body.dark-theme .dataTables_wrapper .dataTables_length,
body.dark-theme .dataTables_wrapper .dataTables_filter,
body.dark-theme .dataTables_wrapper .dataTables_info,
body.dark-theme .dataTables_wrapper .dataTables_processing,
body.dark-theme .dataTables_wrapper .dataTables_paginate {
    color: var(--dark-text-primary);
}

body.dark-theme .dataTables_wrapper .dataTables_paginate .paginate_button {
    color: var(--dark-text-primary) !important;
}

body.dark-theme .dataTables_wrapper .dataTables_paginate .paginate_button.current,
body.dark-theme .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    color: var(--dark-text-primary) !important;
    background: var(--dark-highlight-primary);
    border-color: var(--dark-border-primary);
}

body.dark-theme .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    color: var(--dark-text-primary) !important;
    background: var(--dark-hover-bg);
    border-color: var(--dark-border-primary);
}

body.dark-theme .dataTables_wrapper .dataTables_length select,
body.dark-theme .dataTables_wrapper .dataTables_filter input {
    background-color: var(--dark-bg-tertiary);
    color: var(--dark-text-primary);
    border-color: var(--dark-border-primary);
}

/* Forçar cores de fundo para tabelas DataTables */
body.dark-theme table.dataTable tbody tr {
    background-color: var(--dark-table-row-even);
}

body.dark-theme table.dataTable tbody tr:nth-of-type(odd) {
    background-color: var(--dark-table-row-odd);
}

body.dark-theme table.dataTable tbody tr:hover {
    background-color: var(--dark-table-row-hover) !important;
}

body.dark-theme table.dataTable thead th,
body.dark-theme table.dataTable thead td {
    background-color: var(--dark-table-header-bg) !important;
    color: var(--dark-text-primary) !important;
    border-color: var(--dark-border-primary) !important;
}

body.dark-theme table.dataTable.stripe tbody tr.odd,
body.dark-theme table.dataTable.display tbody tr.odd {
    background-color: var(--dark-table-row-odd);
}

body.dark-theme table.dataTable.stripe tbody tr.even,
body.dark-theme table.dataTable.display tbody tr.even {
    background-color: var(--dark-table-row-even);
}

body.dark-theme table.dataTable.hover tbody tr:hover,
body.dark-theme table.dataTable.display tbody tr:hover {
    background-color: var(--dark-table-row-hover) !important;
}

body.dark-theme table.dataTable.row-border tbody th,
body.dark-theme table.dataTable.row-border tbody td,
body.dark-theme table.dataTable.display tbody th,
body.dark-theme table.dataTable.display tbody td {
    border-top: 1px solid var(--dark-border-primary) !important;
}

/* Melhorias para linhas alternadas em tabelas */
body.dark-theme .table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--dark-table-row-odd);
}

body.dark-theme .table-striped tbody tr:nth-of-type(even) {
    background-color: var(--dark-table-row-even);
}

body.dark-theme .table-striped tbody tr:hover {
    background-color: var(--dark-table-row-hover) !important;
}

/* Melhorias para tabelas com bordas */
body.dark-theme .table-bordered {
    border-color: var(--dark-border-primary);
}

body.dark-theme .table-bordered th,
body.dark-theme .table-bordered td {
    border-color: var(--dark-border-primary);
}

/* Estilos específicos para tabelas Bootstrap */
body.dark-theme .table-striped > tbody > tr:nth-of-type(odd) {
    background-color: var(--dark-table-row-odd);
}

body.dark-theme .table-striped > tbody > tr:nth-of-type(even) {
    background-color: var(--dark-table-row-even);
}

body.dark-theme .table-hover > tbody > tr:hover {
    background-color: var(--dark-table-row-hover) !important;
}

/* Melhorias para filtros de tabela */
body.dark-theme .filters th {
    background-color: var(--dark-table-header-bg) !important;
}

body.dark-theme .filters .column-filter {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
    color: var(--dark-text-primary);
}

/* Estilos específicos para tabelas com DataTables Bootstrap 5 */
body.dark-theme div.dataTables_wrapper div.dataTables_info {
    color: var(--dark-text-secondary) !important;
    padding-top: 0.85em;
}

body.dark-theme .dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: var(--primary-dark) !important;
    color: white !important;
}

/* Estilos para tabelas específicas */
body.dark-theme #produtos-table,
body.dark-theme #clientes-table,
body.dark-theme #cenario-novo-table,
body.dark-theme #cenario-producao-table,
body.dark-theme #cenario-inconsistente-table,
body.dark-theme #importacoes-table,
body.dark-theme #historico-table,
body.dark-theme #empresas-table,
body.dark-theme #usuarios-table,
body.dark-theme #escritorios-table {
    background-color: var(--dark-table-row-even);
    color: var(--dark-text-primary) !important;
}

body.dark-theme #produtos-table thead th,
body.dark-theme #clientes-table thead th,
body.dark-theme #cenario-novo-table thead th,
body.dark-theme #cenario-producao-table thead th,
body.dark-theme #cenario-inconsistente-table thead th,
body.dark-theme #importacoes-table thead th,
body.dark-theme #historico-table thead th,
body.dark-theme #empresas-table thead th,
body.dark-theme #usuarios-table thead th,
body.dark-theme #escritorios-table thead th {
    background-color: var(--dark-table-header-bg) !important;
    color: var(--dark-text-primary) !important;
}

/* Melhorias para cards */
body.dark-theme .card {
    background-color: var(--dark-bg-secondary);
    border-color: var(--dark-border-primary);
}

body.dark-theme .card-header {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
}

body.dark-theme .card-footer {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
}

/* body.dark-theme .card-title {
    color: var(--dark-text-primary);
}

body.dark-theme .card-subtitle {
    color: var(--dark-text-secondary);
} */

/* Melhorias para formulários */
body.dark-theme .form-control,
body.dark-theme .form-select {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
    color: var(--dark-text-primary);
}

body.dark-theme .form-control:focus,
body.dark-theme .form-select:focus {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--primary-color);
    color: var(--dark-text-primary);
    box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
}

body.dark-theme .form-control::placeholder {
    color: var(--dark-text-muted);
}

/* Melhorias para modais */
body.dark-theme .modal-content {
    background-color: var(--dark-bg-secondary);
    border-color: var(--dark-border-primary);
    color: var(--dark-text-primary);
}

body.dark-theme .modal-header {
    border-color: var(--dark-border-primary);
    color: var(--dark-text-primary);
}

body.dark-theme .modal-footer {
    border-color: var(--dark-border-primary);
}

body.dark-theme .modal-title {
    color: var(--dark-text-primary);
}

body.dark-theme .modal-body {
    color: var(--dark-text-primary);
}

body.dark-theme .modal-body .form-label {
    color: var(--dark-text-primary) !important;
}

body.dark-theme .modal-body .form-control-plaintext {
    color: var(--dark-text-primary) !important;
}

/* Estilos específicos para o modal de observações */
body.dark-theme #modal-observacoes-analista .modal-content {
    background-color: var(--dark-bg-secondary);
    color: var(--dark-text-primary);
}

body.dark-theme #modal-observacoes-analista .border {
    border-color: var(--dark-border-primary) !important;
    background-color: var(--dark-bg-tertiary) !important;
    color: var(--dark-text-primary) !important;
}

/* Melhorias para dropdowns */
body.dark-theme .dropdown-menu {
    background-color: var(--dark-bg-secondary);
    border-color: var(--dark-border-primary);
}

body.dark-theme .dropdown-item {
    color: var(--dark-text-primary);
}

body.dark-theme .dropdown-item:hover,
body.dark-theme .dropdown-item:focus {
    background-color: var(--dark-hover-bg);
    color: var(--dark-text-primary);
}

body.dark-theme .dropdown-divider {
    border-color: var(--dark-border-primary);
}

/* Melhorias para navegação e tabs */
body.dark-theme .nav-tabs {
    border-color: var(--dark-border-primary);
}

body.dark-theme .nav-tabs .nav-link {
    color: var(--dark-text-secondary);
}

body.dark-theme .nav-tabs .nav-link:hover {
    color: var(--primary-light);
    background-color: var(--dark-highlight-secondary);
    border-color: var(--dark-border-primary);
}

body.dark-theme .nav-tabs .nav-link.active {
    color: var(--primary-light);
    background-color: var(--dark-highlight-primary);
    border-color: var(--dark-border-primary);
    border-bottom-color: var(--dark-bg-secondary);
}

/* Melhorias para paginação */
body.dark-theme .pagination {
    background-color: transparent;
}

body.dark-theme .page-item.disabled .page-link {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
    color: var(--dark-text-muted);
}

body.dark-theme .page-link {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
    color: var(--dark-text-primary);
}

body.dark-theme .page-item.active .page-link {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: white;
}

body.dark-theme .page-link:hover {
    background-color: var(--dark-hover-bg);
    border-color: var(--dark-border-primary);
    color: var(--dark-text-primary);
}

/* Melhorias para alertas */
body.dark-theme .alert-primary {
    background-color: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
    color: var(--primary-light);
}

body.dark-theme .alert-success {
    background-color: rgba(16, 185, 129, 0.2);
    border-color: rgba(16, 185, 129, 0.3);
    color: #34d399;
}

body.dark-theme .alert-warning {
    background-color: rgba(245, 158, 11, 0.2);
    border-color: rgba(245, 158, 11, 0.3);
    color: #fbbf24;
}

body.dark-theme .alert-danger {
    background-color: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.3);
    color: #f87171;
}

body.dark-theme .alert-info {
    background-color: rgba(6, 182, 212, 0.2);
    border-color: rgba(6, 182, 212, 0.3);
    color: #22d3ee;
}

/* Melhorias para badges */
body.dark-theme .badge {
    color: var(--dark-text-primary);
}

body.dark-theme .badge-primary {
    background-color: var(--primary-dark);
}

body.dark-theme .badge-success {
    background-color: var(--success-color);
}

body.dark-theme .badge-warning {
    background-color: var(--warning-color);
    color: var(--dark-bg-primary);
}

body.dark-theme .badge-danger {
    background-color: var(--danger-color);
}

body.dark-theme .badge-info {
    background-color: var(--info-color);
}

/* Melhorias para botões */
body.dark-theme .btn-outline-primary {
    color: var(--primary-light);
    border-color: var(--primary-light);
}

body.dark-theme .btn-outline-primary:hover {
    background-color: var(--primary-dark);
    color: white;
}

body.dark-theme .btn-outline-secondary {
    color: var(--dark-text-secondary);
    border-color: var(--dark-border-primary);
}

body.dark-theme .btn-outline-secondary:hover {
    background-color: var(--dark-hover-bg);
    color: var(--dark-text-primary);
}

/* Melhorias para listas */
body.dark-theme .list-group-item {
    background-color: var(--dark-bg-secondary);
    border-color: var(--dark-border-primary);
    color: var(--dark-text-primary);
}

body.dark-theme .list-group-item-action:hover {
    background-color: var(--dark-hover-bg);
    color: var(--dark-text-primary);
}

body.dark-theme .list-group-item.active {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

/* Melhorias para tabelas de comparação em cenários */
body.dark-theme .comparison-row td {
    background-color: var(--dark-bg-tertiary);
}

body.dark-theme .comparison-content {
    border-color: var(--dark-border-primary);
}

body.dark-theme .table-danger {
    background-color: rgba(220, 53, 69, 0.2) !important;
}

body.dark-theme .inconsistente-row {
    border-color: var(--dark-border-primary);
}

body.dark-theme .comparison-row .table-primary {
    background-color: rgba(13, 110, 253, 0.2) !important;
}

body.dark-theme .producao-row {
    background-color: rgba(13, 110, 253, 0.2);
}

body.dark-theme .inconsistent-value {
    color: #f87171 !important;
}

body.dark-theme .loading-overlay {
    background-color: rgba(0, 0, 0, 0.7);
}

body.dark-theme .table tbody tr.selected {
    background-color: rgba(13, 110, 253, 0.2) !important;
}

body.dark-theme .cenario-producao-row {
    background-color: rgba(13, 110, 253, 0.2) !important;
}

body.dark-theme .cenario-producao-badge {
    background-color: var(--primary-dark);
}

/* Melhorias específicas para DataTables */
body.dark-theme div.dataTables_wrapper div.dataTables_length label,
body.dark-theme div.dataTables_wrapper div.dataTables_filter label {
    color: var(--dark-text-primary);
}

body.dark-theme div.dataTables_wrapper div.dataTables_info {
    color: var(--dark-text-secondary);
}

body.dark-theme table.dataTable thead .sorting:after,
body.dark-theme table.dataTable thead .sorting_asc:after,
body.dark-theme table.dataTable thead .sorting_desc:after,
body.dark-theme table.dataTable thead .sorting_asc_disabled:after,
body.dark-theme table.dataTable thead .sorting_desc_disabled:after {
    opacity: 0.7;
}

body.dark-theme table.dataTable.stripe tbody tr.odd,
body.dark-theme table.dataTable.display tbody tr.odd {
    background-color: var(--dark-bg-tertiary);
}

body.dark-theme table.dataTable.stripe tbody tr.even,
body.dark-theme table.dataTable.display tbody tr.even {
    background-color: var(--dark-bg-secondary);
}

body.dark-theme table.dataTable.hover tbody tr:hover,
body.dark-theme table.dataTable.display tbody tr:hover {
    background-color: var(--dark-hover-bg);
}

body.dark-theme table.dataTable.row-border tbody th,
body.dark-theme table.dataTable.row-border tbody td,
body.dark-theme table.dataTable.display tbody th,
body.dark-theme table.dataTable.display tbody td {
    border-top: 1px solid var(--dark-border-primary);
}

body.dark-theme table.dataTable.cell-border tbody th,
body.dark-theme table.dataTable.cell-border tbody td {
    border-top: 1px solid var(--dark-border-primary);
    border-right: 1px solid var(--dark-border-primary);
}

body.dark-theme table.dataTable.cell-border tbody tr th:first-child,
body.dark-theme table.dataTable.cell-border tbody tr td:first-child {
    border-left: 1px solid var(--dark-border-primary);
}

body.dark-theme table.dataTable.order-column tbody tr > .sorting_1,
body.dark-theme table.dataTable.order-column tbody tr > .sorting_2,
body.dark-theme table.dataTable.order-column tbody tr > .sorting_3,
body.dark-theme table.dataTable.display tbody tr > .sorting_1,
body.dark-theme table.dataTable.display tbody tr > .sorting_2,
body.dark-theme table.dataTable.display tbody tr > .sorting_3 {
    background-color: var(--dark-highlight-secondary);
}

body.dark-theme table.dataTable.display tbody tr.odd > .sorting_1,
body.dark-theme table.dataTable.order-column.stripe tbody tr.odd > .sorting_1 {
    background-color: rgba(59, 130, 246, 0.15);
}

body.dark-theme table.dataTable.display tbody tr.even > .sorting_1,
body.dark-theme table.dataTable.order-column.stripe tbody tr.even > .sorting_1 {
    background-color: rgba(59, 130, 246, 0.1);
}

/* Melhorias para o sidebar */
body.dark-theme .dashboard-sidebar {
    background-color: var(--dark-bg-secondary);
    border-color: var(--dark-border-primary);
}

body.dark-theme .sidebar-header {
    border-color: var(--dark-border-primary);
}

body.dark-theme .sidebar-brand {
    color: var(--dark-text-primary);
}

body.dark-theme .sidebar-nav .nav-item .nav-link {
    color: var(--dark-text-secondary);
}

body.dark-theme .sidebar-nav .nav-item .nav-link:hover {
    color: var(--primary-light);
    background-color: var(--dark-hover-bg);
}

body.dark-theme .sidebar-nav .nav-item.active .nav-link {
    color: var(--primary-light);
    background-color: var(--dark-highlight-primary);
}

body.dark-theme .sidebar-nav .nav-item .dropdown-menu {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
}

body.dark-theme .sidebar-nav .nav-item .dropdown-item {
    color: var(--dark-text-secondary);
}

body.dark-theme .sidebar-nav .nav-item .dropdown-item:hover {
    color: var(--primary-light);
    background-color: var(--dark-hover-bg);
}

body.dark-theme .sidebar-nav .nav-item .dropdown-item.active {
    color: var(--primary-light);
    background-color: var(--dark-highlight-primary);
}

/* Melhorias para o header */
body.dark-theme .dashboard-header {
    background-color: var(--dark-bg-secondary);
    border-color: var(--dark-border-primary);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.dark-theme .header-left .toggle-sidebar-btn {
    color: var(--dark-text-secondary);
}

body.dark-theme .header-left .toggle-sidebar-btn:hover {
    color: var(--dark-text-primary);
}

body.dark-theme .header-filters .form-select {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
    color: var(--dark-text-primary);
}

body.dark-theme .user-profile .dropdown-toggle {
    color: var(--dark-text-secondary);
}

body.dark-theme .user-profile .dropdown-toggle:hover {
    color: var(--dark-text-primary);
}

/* Melhorias específicas para a página de login */
body.dark-theme .login-page {
    background: linear-gradient(135deg, var(--dark-bg-primary) 0%, var(--dark-bg-secondary) 100%);
}

body.dark-theme .login-card {
    background-color: var(--dark-bg-secondary);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

body.dark-theme .login-title {
    color: var(--dark-text-primary);
}

body.dark-theme .login-subtitle {
    color: var(--dark-text-secondary);
}

body.dark-theme .form-label {
    color: var(--dark-text-primary);
}

body.dark-theme .input-group-text {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
    color: var(--dark-text-secondary);
}

body.dark-theme .toggle-password {
    color: var(--dark-text-secondary);
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
}

body.dark-theme .toggle-password:hover {
    color: var(--dark-text-primary);
    background-color: var(--dark-hover-bg);
}

body.dark-theme .form-check-label {
    color: var(--dark-text-secondary);
}

body.dark-theme .forgot-password {
    color: var(--primary-light);
}

body.dark-theme .forgot-password:hover {
    color: var(--primary-light);
    opacity: 0.8;
}

body.dark-theme .login-btn {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    box-shadow: 0 4px 10px rgba(59, 130, 246, 0.2);
}

body.dark-theme .login-footer {
    border-top-color: var(--dark-border-primary);
}

body.dark-theme .feature-item {
    color: var(--dark-text-secondary);
}

body.dark-theme .feature-item i {
    color: var(--primary-light);
}

body.dark-theme #login-msg.alert-danger {
    background-color: rgba(239, 68, 68, 0.2);
    color: #f87171;
    border-left-color: #ef4444;
}

body.dark-theme #login-msg.alert-success {
    background-color: rgba(16, 185, 129, 0.2);
    color: #34d399;
    border-left-color: #10b981;
}

body.dark-theme #login-msg.alert-info {
    background-color: rgba(6, 182, 212, 0.2);
    color: #22d3ee;
    border-left-color: #06b6d4;
}

/* Melhorias específicas para cenários detalhes */
body.dark-theme .comparison-row td {
    background-color: var(--dark-bg-tertiary);
}

body.dark-theme .comparison-content {
    border-top: 1px solid var(--dark-border-primary);
    background-color: var(--dark-bg-tertiary);
}

body.dark-theme .table-danger {
    background-color: rgba(220, 53, 69, 0.2) !important;
}

body.dark-theme .text-danger.fw-bold {
    color: #f87171 !important;
}

body.dark-theme .inconsistente-row {
    border-bottom: 1px solid var(--dark-border-primary);
}

body.dark-theme .comparison-row table {
    background-color: var(--dark-bg-tertiary);
}

body.dark-theme .comparison-row .table-primary {
    background-color: rgba(13, 110, 253, 0.2) !important;
}

body.dark-theme .producao-row {
    background-color: rgba(13, 110, 253, 0.2);
    color: var(--dark-text-primary);
}

body.dark-theme .producao-row > div {
    color: var(--dark-text-primary);
}

body.dark-theme .inconsistent-value {
    color: #f87171 !important;
}

/* Melhorias para os cards de contagem de cenários */
body.dark-theme #cenarios-count-cards .card {
    background-color: var(--dark-bg-secondary);
    border-color: var(--dark-border-primary);
}

body.dark-theme #cenarios-count-cards .card-subtitle {
    color: var(--dark-text-secondary);
}

body.dark-theme #cenarios-count-cards .card-title {
    color: var(--dark-text-primary);
}

/* Melhorias para os botões de ação em tabelas */
body.dark-theme .btn-sm.btn-primary,
body.dark-theme .btn-sm.btn-success,
body.dark-theme .btn-sm.btn-danger,
body.dark-theme .btn-sm.btn-warning,
body.dark-theme .btn-sm.btn-info {
    color: white;
}

body.dark-theme .btn-sm.btn-outline-primary,
body.dark-theme .btn-sm.btn-outline-success,
body.dark-theme .btn-sm.btn-outline-danger,
body.dark-theme .btn-sm.btn-outline-warning,
body.dark-theme .btn-sm.btn-outline-info {
    border-color: var(--dark-border-primary);
}

body.dark-theme .btn-sm.btn-outline-primary {
    color: var(--primary-light);
}

body.dark-theme .btn-sm.btn-outline-success {
    color: #34d399;
}

body.dark-theme .btn-sm.btn-outline-danger {
    color: #f87171;
}

body.dark-theme .btn-sm.btn-outline-warning {
    color: #fbbf24;
}

body.dark-theme .btn-sm.btn-outline-info {
    color: #22d3ee;
}

/* Melhorias adicionais para DataTables */
body.dark-theme .dataTables_wrapper .dataTables_length,
body.dark-theme .dataTables_wrapper .dataTables_filter,
body.dark-theme .dataTables_wrapper .dataTables_info,
body.dark-theme .dataTables_wrapper .dataTables_processing,
body.dark-theme .dataTables_wrapper .dataTables_paginate {
    color: var(--dark-text-primary) !important;
}

body.dark-theme .dataTables_wrapper .dataTables_paginate .paginate_button {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary) !important;
    color: var(--dark-text-primary) !important;
}

body.dark-theme .dataTables_wrapper .dataTables_paginate .paginate_button.current,
body.dark-theme .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
    color: white !important;
}

body.dark-theme .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: var(--dark-hover-bg) !important;
    border-color: var(--dark-border-primary) !important;
    color: var(--dark-text-primary) !important;
}

body.dark-theme .dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
body.dark-theme .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover,
body.dark-theme .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active {
    color: var(--dark-text-muted) !important;
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary) !important;
}

body.dark-theme .dataTables_wrapper .dataTables_filter input {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
    color: var(--dark-text-primary);
}

body.dark-theme .dataTables_wrapper .dataTables_length select {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
    color: var(--dark-text-primary);
}

/* Forçar cores para tabelas Bootstrap no modo escuro */
body.dark-theme .table-striped > tbody > tr:nth-of-type(odd) > * {
    background-color: var(--dark-bg-tertiary);
    --bs-table-accent-bg: var(--dark-bg-tertiary);
}

body.dark-theme .table-striped > tbody > tr:nth-of-type(even) > * {
    background-color: var(--dark-bg-secondary);
    --bs-table-accent-bg: var(--dark-bg-secondary);
}

/* Forçar cores para tabelas Bootstrap 5 */
body.dark-theme .table {
    --bs-table-bg: var(--dark-bg-secondary);
    --bs-table-striped-bg: var(--dark-bg-tertiary);
    --bs-table-striped-color: var(--dark-text-primary) !important;
    --bs-table-active-bg: var(--dark-hover-bg) !important;
    --bs-table-active-color: var(--dark-text-primary) !important;
    --bs-table-hover-bg: var(--dark-hover-bg) !important;
    --bs-table-hover-color: var(--dark-text-primary) !important;
    color: var(--dark-text-primary) !important;
    border-color: var(--dark-border-primary) !important;
}

/* Estilos específicos para tabelas DataTables com Bootstrap 5 */
body.dark-theme table.dataTable.table-striped > tbody > tr.odd > * {
    box-shadow: inset 0 0 0 9999px var(--dark-bg-tertiary);
}

body.dark-theme table.dataTable.table-striped > tbody > tr.even > * {
    box-shadow: inset 0 0 0 9999px var(--dark-bg-secondary);
}

body.dark-theme table.dataTable.table-hover > tbody > tr:hover > * {
    box-shadow: inset 0 0 0 9999px var(--dark-hover-bg) !important;
}

/* Forçar cores para todas as células da tabela */
body.dark-theme table.dataTable td,
body.dark-theme table.dataTable th,
body.dark-theme .table td,
body.dark-theme .table th {
    background-color: inherit !important;
}

/* Forçar cores para linhas específicas */
body.dark-theme tr,
body.dark-theme tr.odd,
body.dark-theme tr.even {
    background-color: var(--dark-table-row-even);
}

body.dark-theme tr.odd,
body.dark-theme tr:nth-of-type(odd) {
    background-color: var(--dark-table-row-odd);
}

body.dark-theme tr:hover,
body.dark-theme tbody tr:hover {
    background-color: var(--dark-table-row-hover) !important;
}

/* Forçar cores para cabeçalhos de tabela */
body.dark-theme thead,
body.dark-theme thead tr,
body.dark-theme thead th {
    background-color: var(--dark-table-header-bg) !important;
}

/* Estilos diretos para tabelas */
body.dark-theme table {
    background-color: var(--dark-table-row-even);
    color: var(--dark-text-primary) !important;
}

body.dark-theme table tbody tr {
    background-color: var(--dark-table-row-even);
}

body.dark-theme table tbody tr:nth-of-type(odd) {
    background-color: var(--dark-table-row-odd);
}

body.dark-theme table tbody tr:hover {
    background-color: var(--dark-table-row-hover) !important;
}

/* Melhorias para os elementos de paginação personalizados */
body.dark-theme #per-page-select-novo,
body.dark-theme #per-page-select-producao,
body.dark-theme #per-page-select-inconsistente {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
    color: var(--dark-text-primary);
}

/* Melhorias para os filtros de coluna */
body.dark-theme #cenario-filter-novo,
body.dark-theme #cenario-filter-producao,
body.dark-theme #cenario-filter-inconsistente {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
    color: var(--dark-text-primary);
}

/* Melhorias para o seletor de colunas */
body.dark-theme .column-selector {
    background-color: var(--dark-bg-secondary);
    border-color: var(--dark-border-primary);
}

body.dark-theme .column-selector .form-check-label {
    color: var(--dark-text-primary);
}

body.dark-theme .column-selector .dropdown-divider {
    border-color: var(--dark-border-primary);
}

body.dark-theme .audit-dashboard-filters {
    background-color: var(--dark-bg-secondary);
    border-color: var(--dark-border-primary);
}

body.dark-theme .audit-dashboard-filters .form-label {
    color: var(--dark-text-primary);
}

body.dark-theme .audit-dashboard-filters .form-control,
body.dark-theme .audit-dashboard-filters .form-select {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border-primary);
}