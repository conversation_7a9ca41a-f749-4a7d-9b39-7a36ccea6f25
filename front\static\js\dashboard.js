/**
 * Dashboard.js - Auditoria Fiscal
 * Funções para o dashboard principal e navegação
 */

// Variáveis globais
let currentUser = null;
let selectedCompany = null;
let selectedYear = new Date().getFullYear();
let selectedMonth = new Date().getMonth() + 1; // 1-12
let darkThemeEnabled = false;
let isInitialized = false; // Flag para controlar inicialização única
let authChecked = false;

function getCookie(name) {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop().split(';').shift();
}

// Inicialização
document.addEventListener('DOMContentLoaded', function () {
  checkAuth();
});

/**
 * Verifica se o usuário está autenticado
 */
function checkAuth() {
  if (authChecked) return;
  authChecked = true;
  const token = localStorage.getItem('token');

  if (!token) {
    window.location.href = '/fiscal/web';
    return;
  }

  // Verificar se o token é válido fazendo uma requisição para /me
  fetch('/fiscal/me', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => {
      if (response.status === 401) {
        localStorage.removeItem('token');
        window.location.href = '/fiscal/web';
        return;
      }

      // Token válido, continuar com a inicialização
      return response.json();
    })
    .then((user) => {
      if (user) {
        // Carregar informações do usuário
        loadUserInfo(user);

        // Continuar com a inicialização do dashboard
        initDashboard();
      }
    })
    .catch((error) => {
      // Mesmo com erro, tentar continuar com a sessão
      initDashboard();
    });
}

/**
 * Carrega informações do usuário
 * @param {Object} user - Dados do usuário (opcional)
 */
function loadUserInfo(user = null) {
  if (!user) {
    // Tentar carregar do localStorage
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
      try {
        currentUser = JSON.parse(storedUser);
        // Carregar empresas imediatamente após carregar usuário do localStorage
        if (currentUser.tipo_usuario === 'admin' || currentUser.is_admin) {
          loadAllCompanies();
        } else if (currentUser.tipo_usuario === 'escritorio') {
          loadEscritorioCompanies(currentUser.escritorio_id);
        } else if (
          currentUser.empresas_permitidas &&
          currentUser.empresas_permitidas.length > 0
        ) {
          loadUserCompanies(currentUser.empresas_permitidas);
        }
      } catch (e) {
        currentUser = null;
      }
    }

    // Se ainda não tiver dados, fazer requisição
    if (!currentUser) {
      fetch('/fiscal/me', {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      })
        .then((response) => response.json())
        .then((userData) => {
          loadUserInfo(userData);
        })
        .catch((error) => { });
      return;
    }
  } else {
    // Armazenar dados do usuário globalmente e no localStorage
    currentUser = user;
    localStorage.setItem('currentUser', JSON.stringify(user));
  }

  // Exibir nome do usuário
  document.getElementById('user-name').textContent =
    currentUser.nome || 'Usuário';

  // Carregar empresas para o seletor
  if (currentUser.tipo_usuario === 'admin' || currentUser.is_admin) {
    // Administradores veem todas as empresas
    loadAllCompanies();
  } else if (currentUser.tipo_usuario === 'escritorio') {
    // Usuários do tipo escritório veem empresas do seu escritório
    loadEscritorioCompanies(currentUser.escritorio_id);
  } else if (
    currentUser.empresas_permitidas &&
    currentUser.empresas_permitidas.length > 0
  ) {
    // Usuários comuns veem apenas empresas permitidas
    loadUserCompanies(currentUser.empresas_permitidas);
  }
}

/**
 * Inicializa o dashboard
 */
function initDashboard() {
  // Verificar se já foi inicializado para evitar duplicação de event listeners
  if (isInitialized) {
    // Atualizar apenas os dados
    showCurrentPage();
    loadPageData();
    return;
  }

  isInitialized = true;

  // Configurar componentes na ordem correta
  setupCompanySelector();
  loadThemePreference();
  setupThemeToggle();
  setupSidebarHoverEvents();
  setupSidebarNavigation();
  setupProfileDropdownLinks();
  setupSidebarToggle();
  setupLogoutButton();

  // Se já temos um usuário, carregar empresas
  if (currentUser) {
    if (currentUser.tipo_usuario === 'admin' || currentUser.is_admin) {
      loadAllCompanies();
    } else if (currentUser.tipo_usuario === 'escritorio') {
      loadEscritorioCompanies(currentUser.escritorio_id);
    } else if (
      currentUser.empresas_permitidas &&
      currentUser.empresas_permitidas.length > 0
    ) {
      loadUserCompanies(currentUser.empresas_permitidas);
    }
  }

  // Configurar seletor de empresa
  setupCompanySelector();

  // Configurar seletor de ano
  setupYearSelector();

  // Configurar seletor de mês
  setupMonthSelector();

  // Configurar botão de alternância de tema
  setupThemeToggle();

  // Configurar botão de logout
  setupLogoutButton();

  // Mostrar a página correta com base na URL atual
  showCurrentPage();

  // Carregar dados iniciais
  loadPageData();
}

/**
 * Carrega a preferência de tema do usuário
 */
function loadThemePreference() {
  const savedTheme = localStorage.getItem('darkTheme');

  if (savedTheme === 'true') {
    darkThemeEnabled = true;
    document.body.classList.add('dark-theme');

    // Atualizar ícone do botão se ele já existir
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    if (themeToggleBtn) {
      const icon = themeToggleBtn.querySelector('i');
      icon.classList.remove('fa-moon');
      icon.classList.add('fa-sun');
    }
  }
}

/**
 * Configura o botão de alternância de tema
 */
function setupThemeToggle() {
  const themeToggleBtn = document.getElementById('theme-toggle-btn');

  if (themeToggleBtn) {
    // Remover event listeners anteriores (se possível)
    const newThemeToggleBtn = themeToggleBtn.cloneNode(true);
    themeToggleBtn.parentNode.replaceChild(newThemeToggleBtn, themeToggleBtn);

    // Atualizar ícone inicial com base no tema atual
    const icon = newThemeToggleBtn.querySelector('i');
    if (darkThemeEnabled) {
      icon.classList.remove('fa-moon');
      icon.classList.add('fa-sun');
    }

    newThemeToggleBtn.addEventListener('click', function () {
      // Alternar o tema
      darkThemeEnabled = !darkThemeEnabled;

      // Salvar preferência
      localStorage.setItem('darkTheme', darkThemeEnabled);

      // Aplicar ou remover classe do body com uma pequena animação
      if (darkThemeEnabled) {
        document.body.classList.add('dark-theme');
        // Animar o ícone
        icon.style.transform = 'rotate(360deg)';
        setTimeout(() => {
          icon.classList.remove('fa-moon');
          icon.classList.add('fa-sun');
          icon.style.transform = '';
        }, 150);
      } else {
        document.body.classList.remove('dark-theme');
        // Animar o ícone
        icon.style.transform = 'rotate(-360deg)';
        setTimeout(() => {
          icon.classList.remove('fa-sun');
          icon.classList.add('fa-moon');
          icon.style.transform = '';
        }, 150);
      }
    });
  }
}

/**
 * Configura eventos de hover para os dropdowns na sidebar recolhida
 */
function setupSidebarHoverEvents() {
  // Esta função foi movida para dentro de setupSidebarToggle
  // para evitar duplicação de event listeners
}

/**
 * Mostra a página correta com base na URL atual
 */
function showCurrentPage() {
  const currentPath = window.location.pathname;
  let currentPage = 'dashboard'; // Padrão

  // Verificar se estamos em uma página de detalhes de cenários
  const parts = currentPath.split('/').filter(p => p);
  const isCenarioDetalhe =
    parts.length >= 4 &&
    parts[1] === 'cenarios' &&
    (parts[2] === 'entrada' || parts[2] === 'saida') &&
    ['icms', 'icms_st', 'difal', 'ipi', 'pis', 'cofins'].includes(parts[3]);

  // Verificar se estamos em uma página de auditoria específica
  const isAuditoriaDetalhe =
    parts.length >= 4 &&
    parts[1] === 'auditoria' &&
    (parts[2] === 'entrada' || parts[2] === 'saida') &&
    ['icms', 'icms-st', 'difal', 'ipi', 'pis', 'cofins'].includes(parts[3]);

  if (isCenarioDetalhe) {
    return;
  }

  if (isAuditoriaDetalhe) {
    const pages = document.querySelectorAll('.page-section');
    pages.forEach((page) => {
      page.classList.remove('active');
    });

    const auditoriaPage = document.getElementById('page-auditoria-tributo');
    if (auditoriaPage) {
      auditoriaPage.classList.add('active');

      // Atualizar o título da página
      const tituloElement = document.getElementById('auditoria-tributo-titulo');
      if (tituloElement) {
        const tipoOperacao = parts[2] === 'entrada' ? 'Entrada' : 'Saída';
        const tipoTributo = parts[3].toUpperCase().replace('-', '-');
        tituloElement.textContent = `Auditoria de ${tipoTributo} - ${tipoOperacao}`;
      }

      // Configurar o botão de executar auditoria
      const btnExecutarAuditoria = document.getElementById(
        'btn-executar-auditoria',
      );
      if (btnExecutarAuditoria) {
        btnExecutarAuditoria.onclick = function () {
          executarAuditoriaTributo(parts[3], parts[2]);
        };
      }
    }
    return;
  }

  // Determinar a página atual com base na URL
  if (currentPath === '/fiscal/gestao-xmls') {
    currentPage = 'gestao-xmls';
  } else if (currentPath === '/fiscal/auditoria/entrada/auditoria') {
    currentPage = 'auditoria-entrada-auditoria';
  } else if (currentPath.includes('/fiscal/auditoria/entrada')) {
    currentPage = 'auditoria-entrada';
  } else if (currentPath.includes('/fiscal/auditoria/saida')) {
    currentPage = 'auditoria-saida';
  } else if (currentPath.includes('/fiscal/cenarios/entrada')) {
    currentPage = 'cenarios-entrada';
  } else if (currentPath.includes('/fiscal/cenarios/saida')) {
    currentPage = 'cenarios-saida';
  } else if (currentPath.includes('/fiscal/clientes')) {
    currentPage = 'clientes';
  } else if (currentPath.includes('/fiscal/produto')) {
    currentPage = 'produto';
  } else if (currentPath.includes('/fiscal/importacao')) {
    currentPage = 'importacao';
  } else if (currentPath.includes('/fiscal/apuracao')) {
    currentPage = 'apuracao';
  } else if (currentPath.includes('/fiscal/empresas')) {
    currentPage = 'empresas';
  } else if (currentPath.includes('/fiscal/usuarios')) {
    currentPage = 'usuarios';
  } else if (currentPath.includes('/fiscal/escritorios')) {
    currentPage = 'escritorios';
  }

  // Esconder todas as páginas
  const pages = document.querySelectorAll('.page-section');
  pages.forEach((page) => {
    page.classList.remove('active');
  });

  // Mostrar a página atual
  const currentPageElement = document.getElementById(`page-${currentPage}`);
  console.log('Tentando mostrar página:', currentPage, 'Elemento encontrado:', !!currentPageElement);

  if (currentPageElement) {
    currentPageElement.classList.add('active');
    console.log('Página ativada:', currentPage);
  } else {
    console.log('Página não encontrada, mostrando dashboard');
    const dashboardPage = document.getElementById('page-dashboard');
    if (dashboardPage) {
      dashboardPage.classList.add('active');
    }
  }
}

/**
 * Carrega os dados específicos da página atual
 */
function loadPageData() {
  const currentPath = window.location.pathname;

  // Verificar se estamos em uma página de detalhes de cenários
  const parts = currentPath.split('/').filter(p => p);
  const isCenarioDetalhe =
    parts.length >= 4 &&
    parts[1] === 'cenarios' &&
    (parts[2] === 'entrada' || parts[2] === 'saida') &&
    ['icms', 'icms_st', 'difal', 'ipi', 'pis', 'cofins'].includes(parts[3]);

  // Verificar se estamos em uma página de auditoria específica
  const isAuditoriaDetalhe =
    parts.length >= 4 &&
    parts[1] === 'auditoria' &&
    (parts[2] === 'entrada' || parts[2] === 'saida') &&
    ['icms', 'icms-st', 'difal', 'ipi', 'pis', 'cofins'].includes(parts[3]);

  if (isCenarioDetalhe) {
    if (typeof window.cenariosDetalhes !== 'undefined') {
      setupCenariosDetalhesPage();
    } else {
      const script = document.createElement('script');
      script.src = '/fiscal/static/js/cenarios_detalhes.js';
      script.onload = function () {
        if (typeof setupCenariosDetalhesPage === 'function') {
          setupCenariosDetalhesPage();
        }
      };
      script.onerror = function () { };
      document.head.appendChild(script);
    }
    return;
  }

  if (isAuditoriaDetalhe) {
    carregarDashboardAuditoria(parts[3]);
    return;
  }

  if (currentPath === '/fiscal/gestao-xmls') {
    // Página de gestão de XMLs - será carregada pelo auditoria_entrada.js
    return;
  } else if (currentPath === '/fiscal/auditoria/entrada/auditoria') {
    // Página de auditoria comparativa - será carregada pelo auditoria_entrada.js
    return;
  } else if (currentPath.includes('/fiscal/auditoria/entrada')) {
    // Página principal de auditoria de entrada - não precisa carregar dados específicos
    return;
  } else if (currentPath.includes('/fiscal/auditoria/saida')) {
    loadAuditoriaSaidaData();
  } else if (currentPath.includes('/fiscal/cenarios/entrada')) {
    loadCenariosEntradaData();
  } else if (currentPath.includes('/fiscal/cenarios/saida')) {
    loadCenariosSaidaData();
  } else if (currentPath.includes('/fiscal/clientes')) {
    loadClientesData();
  } else if (currentPath.includes('/fiscal/produto')) {
    loadProdutoData();
  } else if (currentPath.includes('/fiscal/importacao')) {
    loadImportacaoData();
  } else if (currentPath.includes('/fiscal/apuracao')) {
    loadApuracaoData();
  } else if (currentPath.includes('/fiscal/empresas')) {
    loadEmpresasData();
  } else if (currentPath.includes('/fiscal/usuarios')) {
    loadUsuariosData();
  } else if (currentPath.includes('/fiscal/escritorios')) {
    loadEscritoriosData();
  } else {
    // Dashboard é a página padrão
    loadDashboardData();
  }
}

/**
 * Configura a navegação da sidebar
 */
function setupSidebarNavigation() {
  const navItems = document.querySelectorAll('.nav-item');
  const dropdownItems = document.querySelectorAll('.nav-dropdown-item');
  const dropdownToggles = document.querySelectorAll('.dropdown-toggle');

  // Marcar o item ativo com base na URL atual
  const currentPath = window.location.pathname;
  let currentPage = 'home'; // Padrão
  let parentPage = '';

  // Determinar a página atual com base na URL
  if (currentPath === '/fiscal/gestao-xmls') {
    currentPage = 'gestao-xmls';
  } else if (currentPath === '/fiscal/auditoria/entrada/auditoria') {
    currentPage = 'auditoria-entrada-auditoria';
    parentPage = 'auditoria';
  } else if (currentPath.includes('/fiscal/auditoria/entrada')) {
    currentPage = 'auditoria-entrada';
    parentPage = 'auditoria';
  } else if (currentPath.includes('/fiscal/auditoria/saida')) {
    currentPage = 'auditoria-saida';
    parentPage = 'auditoria';
  } else if (currentPath.includes('/fiscal/cenarios/entrada')) {
    currentPage = 'cenarios-entrada';
    parentPage = 'cenarios';
  } else if (currentPath.includes('/fiscal/cenarios/saida')) {
    currentPage = 'cenarios-saida';
    parentPage = 'cenarios';
  } else if (currentPath.includes('/fiscal/clientes')) {
    currentPage = 'clientes';
  } else if (currentPath.includes('/fiscal/produto')) {
    currentPage = 'produto';
  } else if (currentPath.includes('/fiscal/importacao')) {
    currentPage = 'importacao';
  } else if (currentPath.includes('/fiscal/apuracao')) {
    currentPage = 'apuracao';
  } else if (currentPath.includes('/fiscal/empresas')) {
    currentPage = 'empresas';
  } else if (currentPath.includes('/fiscal/usuarios')) {
    currentPage = 'usuarios';
  } else if (currentPath.includes('/fiscal/escritorios')) {
    currentPage = 'escritorios';
  }

  // Remover classe active de todos os itens
  navItems.forEach((item) => {
    item.classList.remove('active');

    // Adicionar classe active ao item correspondente à página atual ou ao seu pai
    if (item.dataset.page === currentPage || item.dataset.page === parentPage) {
      item.classList.add('active');

      // Se for um dropdown, abri-lo apenas se a sidebar não estiver colapsada
      if (item.classList.contains('nav-dropdown')) {
        const container = document.querySelector('.dashboard-container');
        // Só abrir o dropdown se a sidebar NÃO estiver colapsada
        if (container && !container.classList.contains('sidebar-collapsed')) {
          item.classList.add('open');
        } else {
          // Se a sidebar estiver colapsada, garantir que o dropdown esteja fechado
          item.classList.remove('open');
        }
      }
    }
  });

  // Marcar o item do dropdown ativo
  dropdownItems.forEach((item) => {
    item.classList.remove('active');

    if (item.dataset.page === currentPage) {
      item.classList.add('active');
    }
  });

  // Configurar os toggles dos dropdowns
  document.querySelectorAll('.nav-dropdown > a').forEach((toggle) => {
    // Remover event listeners anteriores (se possível)
    const newToggle = toggle.cloneNode(true);
    toggle.parentNode.replaceChild(newToggle, toggle);

    newToggle.addEventListener('click', function (e) {
      e.preventDefault();
      const parent = this.closest('.nav-dropdown');

      // Fechar outros dropdowns
      document.querySelectorAll('.nav-dropdown').forEach((dropdown) => {
        if (dropdown !== parent) {
          dropdown.classList.remove('open');
        }
      });

      // Alternar o estado do dropdown atual
      parent.classList.toggle('open');
    });
  });

  // Configurar os links do dropdown do perfil
  setupProfileDropdownLinks();
}

/**
 * Configura os links do dropdown do perfil do usuário
 */
function setupProfileDropdownLinks() {
  const empresasLink = document.getElementById('menu-empresas');
  const usuariosLink = document.getElementById('menu-usuarios');
  const escritoriosLink = document.getElementById('menu-escritorios');

  // Função auxiliar para clonar e substituir um elemento para remover event listeners
  function resetElement(element) {
    if (!element) return null;
    const newElement = element.cloneNode(true);
    element.parentNode.replaceChild(newElement, element);
    return newElement;
  }

  // Resetar e configurar link de empresas
  if (empresasLink) {
    const newEmpresasLink = resetElement(empresasLink);
    newEmpresasLink.addEventListener('click', function (e) {
      e.preventDefault();
      window.location.href = '/fiscal/empresas';
    });
  }

  // Resetar e configurar link de usuários
  if (usuariosLink) {
    const newUsuariosLink = resetElement(usuariosLink);
    newUsuariosLink.addEventListener('click', function (e) {
      e.preventDefault();
      window.location.href = '/fiscal/usuarios';
    });
  }

  // Resetar e configurar link de escritórios
  if (escritoriosLink) {
    const newEscritoriosLink = resetElement(escritoriosLink);

    // Mostrar o link de escritórios apenas para usuários admin
    if (
      currentUser &&
      (currentUser.is_admin || currentUser.tipo_usuario === 'admin')
    ) {
      newEscritoriosLink.style.display = 'block';

      newEscritoriosLink.addEventListener('click', function (e) {
        e.preventDefault();
        window.location.href = '/fiscal/escritorios';
      });
    } else {
      newEscritoriosLink.style.display = 'none';
    }
  }
}

/**
 * Configura o toggle da sidebar
 */
function setupSidebarToggle() {
  const toggleBtn = document.getElementById('toggle-sidebar');
  // Remover event listeners anteriores (se possível)
  const newToggleBtn = toggleBtn.cloneNode(true);
  toggleBtn.parentNode.replaceChild(newToggleBtn, toggleBtn);

  const container = document.querySelector('.dashboard-container');
  const dropdowns = document.querySelectorAll('.nav-dropdown');

  // Armazenar referências às funções de handler para poder removê-las depois
  const mouseEnterHandlers = new Map();
  const mouseLeaveHandlers = new Map();

  // Função para adicionar event listeners de hover
  function addHoverListeners() {
    dropdowns.forEach((dropdown, index) => {
      const dropdownMenu = dropdown.querySelector('.nav-dropdown-menu');
      const dropdownToggle = dropdown.querySelector('.nav-link');

      // Criar funções de handler e armazená-las para remoção posterior
      const mouseEnterHandler = function () {
        if (container.classList.contains('sidebar-collapsed')) {
          // Posicionar o menu dropdown ao lado do item
          const rect = dropdownToggle.getBoundingClientRect();
          dropdownMenu.style.top = rect.top + 'px';
          dropdown.classList.add('open');
        }
      };

      const mouseLeaveHandler = function (event) {
        if (container.classList.contains('sidebar-collapsed')) {
          // Verificar se o mouse está saindo para o dropdown
          const relatedTarget = event.relatedTarget;
          if (!dropdown.contains(relatedTarget)) {
            // Adicionar um pequeno delay para permitir que o usuário mova o mouse para o dropdown
            setTimeout(() => {
              if (!dropdown.matches(':hover')) {
                dropdown.classList.remove('open');
              }
            }, 100);
          }
        }
      };

      // Armazenar referências
      mouseEnterHandlers.set(dropdown, mouseEnterHandler);
      mouseLeaveHandlers.set(dropdown, mouseLeaveHandler);

      // Adicionar event listeners
      dropdown.addEventListener('mouseenter', mouseEnterHandler);
      dropdown.addEventListener('mouseleave', mouseLeaveHandler);
    });
  }

  // Função para remover event listeners de hover
  function removeHoverListeners() {
    dropdowns.forEach((dropdown) => {
      const mouseEnterHandler = mouseEnterHandlers.get(dropdown);
      const mouseLeaveHandler = mouseLeaveHandlers.get(dropdown);

      if (mouseEnterHandler) {
        dropdown.removeEventListener('mouseenter', mouseEnterHandler);
      }

      if (mouseLeaveHandler) {
        dropdown.removeEventListener('mouseleave', mouseLeaveHandler);
      }
    });

    // Limpar os maps
    mouseEnterHandlers.clear();
    mouseLeaveHandlers.clear();
  }

  // Configurar estado inicial com base no tamanho da tela e no localStorage
  const storedSidebarState = localStorage.getItem('sidebarCollapsed');
  const isMobile = window.innerWidth <= 992;

  if (!isMobile) {
    if (storedSidebarState === 'true') {
      container.classList.add('sidebar-collapsed');
      dropdowns.forEach((dropdown) => {
        dropdown.classList.remove('open');
      });
      addHoverListeners();
    } else if (storedSidebarState === 'false') {
      container.classList.remove('sidebar-collapsed');
      removeHoverListeners();
    } else if (container.classList.contains('sidebar-collapsed')) {
      dropdowns.forEach((dropdown) => {
        dropdown.classList.remove('open');
      });
      addHoverListeners();
    }
  }

  newToggleBtn.addEventListener('click', function () {
    // Fechar todos os dropdowns antes de colapsar/expandir a sidebar
    dropdowns.forEach((dropdown) => {
      dropdown.classList.remove('open');
    });

    const isMobile = window.innerWidth <= 992;

    if (isMobile) {
      container.classList.toggle('sidebar-visible');
    } else {
      // Toggle da classe sidebar-collapsed
      container.classList.toggle('sidebar-collapsed');

      // Salvar estado no localStorage
      const isCollapsed = container.classList.contains('sidebar-collapsed');
      localStorage.setItem('sidebarCollapsed', isCollapsed);

      // Adicionar ou remover event listeners com base no estado da sidebar
      if (isCollapsed) {
        addHoverListeners();
      } else {
        removeHoverListeners();
      }
    }
  });
}

/**
 * Configura o seletor de empresa
 */
function setupCompanySelector() {
  const dropdown = document.querySelector('.company-selector');
  const companySelect = document.getElementById('company-select');

  if (!companySelect) {
    return;
  }

  const empresaSalva = localStorage.getItem('selectedCompany');
  if (empresaSalva) {
    selectedCompany = empresaSalva;
  }

  // Remover event listeners anteriores (se possível)
  const newCompanySelect = companySelect.cloneNode(true);
  companySelect.parentNode.replaceChild(newCompanySelect, companySelect);

  if (
    dropdown &&
    dropdown.classList.contains('custom-dropdown') &&
    window.initCustomDropdown
  ) {
    dropdown
      .querySelectorAll('.custom-dropdown-toggle, .custom-dropdown-menu')
      .forEach((el) => el.remove());
    window.initCustomDropdown(dropdown);
  }

  // Configurar evento de mudança
  newCompanySelect.addEventListener('change', function () {
    const novaEmpresa = this.value;
    selectedCompany = novaEmpresa;
    localStorage.setItem('selectedCompany', novaEmpresa);
    window.selectedCompany = novaEmpresa;

    // Disparar um evento personalizado para notificar outros componentes
    const event = new CustomEvent('company-changed', {
      detail: { companyId: novaEmpresa },
    });
    window.dispatchEvent(event);

    // Recarregar dados da página atual
    loadPageData();
  });
}

/**
 * Configura o seletor de ano
 */
function setupYearSelector() {
  const dropdown = document.querySelector('.year-selector');
  const yearSelect = document.getElementById('year-select');
  if (!yearSelect) return;

  // Remover event listeners anteriores (se possível)
  const newYearSelect = yearSelect.cloneNode(false); // false para não clonar as opções
  yearSelect.parentNode.replaceChild(newYearSelect, yearSelect);

  if (
    dropdown &&
    dropdown.classList.contains('custom-dropdown') &&
    window.initCustomDropdown
  ) {
    dropdown
      .querySelectorAll('.custom-dropdown-toggle, .custom-dropdown-menu')
      .forEach((el) => el.remove());
    window.initCustomDropdown(dropdown);
  }

  // Preencher opções de ano (atual e 5 anos anteriores)
  const currentYear = new Date().getFullYear();
  for (let year = currentYear; year >= currentYear - 5; year--) {
    const option = document.createElement('option');
    option.value = year;
    option.textContent = year;
    newYearSelect.appendChild(option);
  }

  // Recuperar ano selecionado do localStorage
  const storedYear = localStorage.getItem('selectedYear');
  if (storedYear) {
    newYearSelect.value = storedYear;
    selectedYear = parseInt(storedYear);
  } else {
    newYearSelect.value = currentYear;
    selectedYear = currentYear;
  }

  // Configurar evento de mudança
  newYearSelect.addEventListener('change', function () {
    selectedYear = parseInt(this.value);
    localStorage.setItem('selectedYear', selectedYear);
    // Recarregar a página para refletir a mudança de ano
    window.location.reload();


    // Recarregar dados com o novo ano selecionado
    const activeSection = document.querySelector('.content-section.active');
    if (activeSection) {
      const sectionId = activeSection.id.replace('sec-', '');
      showSection(sectionId);
    }
  });
}

/**
 * Configura o seletor de mês
 */
function setupMonthSelector() {
  const dropdown = document.querySelector('.month-selector');
  const monthSelect = document.getElementById('month-select');
  if (!monthSelect) return;

  // Remover event listeners anteriores (se possível)
  const newMonthSelect = monthSelect.cloneNode(true);
  monthSelect.parentNode.replaceChild(newMonthSelect, monthSelect);

  // Recuperar mês selecionado do localStorage
  const storedMonth = localStorage.getItem('selectedMonth');
  if (storedMonth) {
    newMonthSelect.value = storedMonth;
    selectedMonth = parseInt(storedMonth);
  } else {
    const currentMonth = new Date().getMonth() + 1;
    newMonthSelect.value = currentMonth;
    selectedMonth = currentMonth;
    localStorage.setItem('selectedMonth', currentMonth);
  }

  // Configurar evento de mudança
  newMonthSelect.addEventListener('change', function () {
    selectedMonth = parseInt(this.value);
    localStorage.setItem('selectedMonth', selectedMonth);

    // Recarregar dados da página atual
    loadPageData();
  });

  // Reinicializar o dropdown customizado APÓS definir o valor
  if (
    dropdown &&
    dropdown.classList.contains('custom-dropdown') &&
    window.initCustomDropdown
  ) {
    dropdown
      .querySelectorAll('.custom-dropdown-toggle, .custom-dropdown-menu')
      .forEach((el) => el.remove());
    window.initCustomDropdown(dropdown);
  }
}

/**
 * Carrega todas as empresas para o seletor
 */
function loadAllCompanies() {
  fetch('/fiscal/api/empresas', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (!data.empresas) {
        return;
      }
      populateCompanySelector(data.empresas);
    })
    .catch((error) => { });
}

/**
 * Carrega empresas do escritório para o seletor
 * @param {number} escritorioId - ID do escritório
 */
function loadEscritorioCompanies(escritorioId) {
  fetch('/fiscal/api/empresas', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      // Filtrar empresas do escritório (já deve vir filtrado do backend)
      populateCompanySelector(data.empresas);
    })
    .catch((error) => { });
}

/**
 * Carrega empresas permitidas para o usuário
 * @param {Array} empresasPermitidas - Array de IDs de empresas permitidas
 */
function loadUserCompanies(empresasPermitidas) {
  fetch('/fiscal/api/empresas', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      // Filtrar empresas permitidas (já deve vir filtrado do backend)
      populateCompanySelector(data.empresas);
    })
    .catch((error) => { });
}

/**
 * Preenche o seletor de empresas com as opções
 * @param {Array} empresas - Lista de empresas
 */
function populateCompanySelector(empresas) {
  // Verificar se temos empresas válidas
  if (!Array.isArray(empresas)) {
    return;
  }

  // Obter o seletor atual (que pode ter sido substituído)
  const companySelect = document.getElementById('company-select');

  if (!companySelect) {
    return;
  }

  // Verificar se já existe uma empresa selecionada
  const empresaSalva = localStorage.getItem('selectedCompany');

  // Limpar opções existentes, mantendo a primeira
  while (companySelect.options.length > 1) {
    companySelect.remove(1);
  }

  // Adicionar novas opções
  empresas.forEach((empresa) => {
    const option = document.createElement('option');
    option.value = empresa.id;
    option.textContent =
      empresa.razao_social || empresa.nome_fantasia || empresa.nome;
    companySelect.appendChild(option);
  });

  // Selecionar empresa
  if (
    empresaSalva &&
    empresas.some((e) => e.id.toString() === empresaSalva.toString())
  ) {
    selectedCompany = empresaSalva;
    companySelect.value = empresaSalva;
  } else if (empresas.length > 0) {
    // Selecionar primeira empresa se nenhuma estiver selecionada
    selectedCompany = empresas[0].id;
    companySelect.value = selectedCompany;
    localStorage.setItem('selectedCompany', selectedCompany);
  }

  // Disparar evento de mudança para notificar outros componentes
  companySelect.dispatchEvent(new Event('change'));

  // Atualizar variável global
  window.selectedCompany = selectedCompany;
}

/**
 * Carrega dados para o dashboard principal
 */
function loadDashboardData() {
  // Verificar se estamos na página do dashboard
  if (!window.location.pathname.endsWith('/fiscal/dashboard')) {
    return;
  }

  // Inserir botões de tipo de dashboard logo abaixo do título
  inserirBotoesTipoDashboard();

  // Exemplo: atualizar contadores
  const contadores = {
    'total-documentos': '0',
    'total-conformes': '0',
    'total-pendentes': '0',
    'total-nao-conformes': '0',
  };

  // Atualizar apenas os contadores que existem
  Object.entries(contadores).forEach(([id, valor]) => {
    const elemento = document.getElementById(id);
    if (elemento) {
      elemento.textContent = valor;
    } else {
    }
  });

  // Aqui você faria uma requisição para obter os dados reais
  // e atualizar os elementos da interface
}

/**
 * Insere os botões de tipo de dashboard (Saída/Entrada) logo abaixo do título
 */
function inserirBotoesTipoDashboard() {
  const header = document.querySelector('#page-dashboard .section-header');
  if (!header) return;

  const wrapperId = 'dashboard-tipo-buttons-wrapper';
  const existing = document.getElementById(wrapperId);

  // Verificar qual aba está ativa atualmente
  const saidaTab = document.getElementById('saida-dashboard-tab');
  const entradaTab = document.getElementById('entrada-dashboard-tab');
  const saidaAtiva = saidaTab && saidaTab.classList.contains('active');
  const entradaAtiva = entradaTab && entradaTab.classList.contains('active');

  // Ocultar as abas originais do HTML
  const originalTabs = document.getElementById('dashboard-main-tabs');
  if (originalTabs) {
    originalTabs.style.display = 'none';
  }

  const buttonsHTML = `
    <div class="dashboard-tipo-buttons-wrapper" id="${wrapperId}" style="margin-top: 15px; margin-bottom: 20px;">
      <div class="btn-group dashboard-tipo-btn-group" role="group" aria-label="Tipo de Dashboard">
        <button type="button" 
                class="btn ${saidaAtiva || !entradaAtiva ? 'btn-primary' : 'btn-outline-primary'} dashboard-tipo-btn" 
                id="dashboard-saida-btn"
                style="border-radius: 25px 0 0 25px; padding: 10px 20px;"
                onclick="alternarDashboardTipo('saida')">
          <i class="fas fa-arrow-up"></i> Saída
        </button>
        <button type="button" 
                class="btn ${entradaAtiva ? 'btn-primary' : 'btn-outline-primary'} dashboard-tipo-btn" 
                id="dashboard-entrada-btn"
                style="border-radius: 0 25px 25px 0; padding: 10px 20px;"
                onclick="alternarDashboardTipo('entrada')">
          <i class="fas fa-arrow-down"></i> Entrada
        </button>
      </div>
    </div>`;

  if (existing) {
    existing.outerHTML = buttonsHTML;
  } else {
    const title = header.querySelector('h2');
    if (title) {
      title.insertAdjacentHTML('afterend', buttonsHTML);
    } else {
      header.insertAdjacentHTML('beforeend', buttonsHTML);
    }
  }
}

/**
 * Alterna entre os tipos de dashboard (Saída/Entrada)
 */
function alternarDashboardTipo(tipo) {
  // Atualizar botões visuais
  const saidaBtn = document.getElementById('dashboard-saida-btn');
  const entradaBtn = document.getElementById('dashboard-entrada-btn');

  if (tipo === 'saida') {
    saidaBtn.className = 'btn btn-primary';
    entradaBtn.className = 'btn btn-outline-primary';

    // Ativar aba de saída
    const saidaTab = document.getElementById('saida-dashboard-tab');
    const entradaTab = document.getElementById('entrada-dashboard-tab');
    if (saidaTab && entradaTab) {
      saidaTab.click();
    }
  } else {
    saidaBtn.className = 'btn btn-outline-primary';
    entradaBtn.className = 'btn btn-primary';

    // Ativar aba de entrada
    const entradaTab = document.getElementById('entrada-dashboard-tab');
    const saidaTab = document.getElementById('saida-dashboard-tab');
    if (entradaTab && saidaTab) {
      entradaTab.click();
    }
  }

  // A cor será controlada pelo CSS baseado no tema
}

/**

/**
 * Carrega dados para a página de escritórios
 */
function loadEscritoriosData() {
  // Verificar se já estamos na página de escritórios para evitar loop
  if (window.location.pathname !== '/fiscal/escritorios') {
    // Redirecionar para a página de escritórios
    window.location.href = '/fiscal/escritorios';
  }
}

/**
 * Configura o botão de logout
 */
function setupLogoutButton() {
  const logoutBtn = document.getElementById('logout-btn');

  logoutBtn.addEventListener('click', function (e) {
    e.preventDefault();
    performLogout();
  });
}

/**
 * Realiza o logout do usuário
 */
function performLogout() {
  // Remover token e dados salvos
  localStorage.removeItem('token');
  localStorage.removeItem('selectedCompany');
  localStorage.removeItem('currentUser');

  // Remover cookie de autenticação do portal
  document.cookie = 'token=; Max-Age=0; path=/';

  // Redirecionar para a página de login
  window.location.href = '/fiscal/web';
}