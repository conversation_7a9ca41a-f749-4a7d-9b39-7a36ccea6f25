<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auditoria Fiscal | Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <title>Auditoria Fiscal | Dashboard</title>
    <link rel="icon" href="{{ url_for('static', filename='img/Audittei-25.ico') }}" type="image/x-icon">
    <!-- Bootstrap CSS -->
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <!-- Chart.js CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@4.4.3/dist/chart.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/animations.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dark-mode-enhancements.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/auditoria_dashboard.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-empresa.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/auditoria_comparativa.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/import-progress.css') }}">
    <!-- Chatbot IA -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/chatbot_ia.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom-dropdown.css') }}">
    <!-- Enhanced Design System -->
</head>

<body>
    <div class="dashboard-container">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="btn toggle-sidebar-btn" id="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo-container">
                    <img src="{{ url_for('static', filename='img/Audittei--26.png') }}" alt="Logo" class="header-logo">
                    <!-- <span class="logo-text">Auditoria Fiscal</span> -->
                </div>
            </div>
            <div class="header-right">
                <div class="header-filters">
                    <!-- Seletor de Empresa -->
                    <div class="company-selector custom-dropdown">
                        <select id="company-select" class="form-select">
                            <option value="">Selecione uma empresa</option>
                            <!-- Opções serão carregadas via JavaScript -->
                        </select>
                    </div>

                    <!-- Seletor de Ano -->
                    <div class="year-selector custom-dropdown">
                        <select id="year-select" class="form-select">
                            <option value="">Ano</option>
                            <!-- Opções serão carregadas via JavaScript -->
                        </select>
                    </div>

                    <!-- Seletor de Mês -->
                    <div class="month-selector custom-dropdown">
                        <select id="month-select" class="form-select">
                            <option value="">Mês</option>
                            <option value="1">Janeiro</option>
                            <option value="2">Fevereiro</option>
                            <option value="3">Março</option>
                            <option value="4">Abril</option>
                            <option value="5">Maio</option>
                            <option value="6">Junho</option>
                            <option value="7">Julho</option>
                            <option value="8">Agosto</option>
                            <option value="9">Setembro</option>
                            <option value="10">Outubro</option>
                            <option value="11">Novembro</option>
                            <option value="12">Dezembro</option>
                        </select>
                    </div>
                </div>

                <!-- Botão de Tema -->
                <div class="theme-toggle">
                    <button id="theme-toggle-btn" class="btn" title="Alternar modo escuro">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>

                <!-- Perfil do Usuário -->
                <div class="user-profile dropdown">
                    <button class="btn dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <span id="user-name">Usuário</span>
                        <i class="fas fa-user-circle"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="{{ url_for('main.perfil_page') }}" id="menu-perfil"><i class="fas fa-user me-2"></i> Meu Perfil</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('main.empresas_page') }}" id="menu-empresas"><i class="fas fa-building me-2"></i> Empresas</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('main.usuarios_page') }}" id="menu-usuarios"><i class="fas fa-users me-2"></i> Usuários</a></li>
                        <!-- <li><a class="dropdown-item" href="{{ url_for('main.escritorios_page') }}" id="menu-escritorios"><i class="fas fa-briefcase me-2"></i> Escritórios</a></li> -->
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li><a class="dropdown-item" href="#" id="logout-btn"><i class="fas fa-sign-out-alt me-2"></i> Sair</a></li>
                    </ul>
                </div>
            </div>
        </header>

        <!-- Sidebar -->
        <aside class="dashboard-sidebar">
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item active" data-page="home">
                        <a href="{{ url_for('main.dashboard') }}" class="nav-link" data-tooltip="Home">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                    </li>

                    <!-- Auditoria com dropdown -->
                    <li class="nav-item nav-dropdown" data-page="auditoria">
                        <a href="#" class="nav-link" data-tooltip="Auditoria">
                            <i class="fas fa-clipboard-check"></i>
                            <span>Auditoria</span>
                            <i class="fas fa-chevron-down dropdown-icon"></i>
                        </a>
                        <ul class="nav-dropdown-menu">
                            <li class="nav-dropdown-item" data-page="auditoria-entrada">
                                <a href="{{ url_for('main.auditoria_entrada') }}" class="nav-dropdown-link">
                                    <i class="fas fa-arrow-right"></i>
                                    <span>Entrada</span>
                                </a>
                            </li>
                            <li class="nav-dropdown-item" data-page="auditoria-saida">
                                <a href="{{ url_for('main.auditoria_saida') }}" class="nav-dropdown-link">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>Saída</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Cenários com dropdown -->
                    <li class="nav-item nav-dropdown" data-page="cenarios">
                        <a href="#" class="nav-link" data-tooltip="Cenários">
                            <i class="fas fa-sitemap"></i>
                            <span>Cenários</span>
                            <i class="fas fa-chevron-down dropdown-icon"></i>
                        </a>
                        <ul class="nav-dropdown-menu">
                            <li class="nav-dropdown-item" data-page="cenarios-entrada">
                                <a href="{{ url_for('main.cenarios_entrada') }}" class="nav-dropdown-link">
                                    <i class="fas fa-arrow-right"></i>
                                    <span>Entrada</span>
                                </a>
                            </li>
                            <li class="nav-dropdown-item" data-page="cenarios-saida">
                                <a href="{{ url_for('main.cenarios_saida') }}" class="nav-dropdown-link">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>Saída</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Clientes -->
                    <li class="nav-item" data-page="clientes">
                        <a href="{{ url_for('main.clientes') }}" class="nav-link" data-tooltip="Participantes">
                            <i class="fas fa-users"></i>
                            <span>Participantes</span>
                        </a>
                    </li>

                    <!-- Produto -->
                    <li class="nav-item" data-page="produto">
                        <a href="{{ url_for('main.produto') }}" class="nav-link" data-tooltip="Produto">
                            <i class="fas fa-box"></i>
                            <span>Produto</span>
                        </a>
                    </li>

                    <!-- Importação -->
                    <li class="nav-item" data-page="importacao">
                        <a href="{{ url_for('main.importacao') }}" class="nav-link" data-tooltip="Importação">
                            <i class="fas fa-file-import"></i>
                            <span>Importação</span>
                        </a>
                    </li>

                    <!-- Gestão de XMLs -->
                    <li class="nav-item" data-page="gestao-xmls">
                        <a href="{{ url_for('main.gestao_xmls') }}" class="nav-link" data-tooltip="Gestão de XMLs">
                            <i class="fas fa-file-alt"></i>
                            <span>Gestão de XMLs</span>
                        </a>
                    </li>

                    <!-- Apuração -->
                    <li class="nav-item" data-page="apuracao">
                        <a href="{{ url_for('main.apuracao_page') }}" class="nav-link" data-tooltip="Apuração">
                            <i class="fas fa-calculator"></i>
                            <span>Apuração</span>
                        </a>
                    </li>

                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="dashboard-content">
            <!-- Conteúdo dinâmico baseado na página atual -->
            <div id="page-content">
                <!-- O conteúdo será carregado dinamicamente com base na URL -->

                <!-- Dashboard Page (default) -->
                <div id="page-dashboard" class="page-section animate-fade-in-up">
                    <div class="section-header animate-fade-in-down">
                        <h2><i class="fas fa-tachometer-alt"></i> Dashboard</h2>
                    </div>
                    <ul class="nav nav-tabs mb-4 dashboard-overview-only" id="dashboard-main-tabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="saida-dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard-saida" type="button" role="tab">
                                <i class="fas fa-arrow-up"></i> Saída
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="entrada-dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard-entrada" type="button" role="tab">
                                <i class="fas fa-arrow-down"></i> Entrada
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content dashboard-overview-only">
                        <div class="tab-pane fade show active" id="dashboard-saida" role="tabpanel">
                            <!-- Novos Cards do Dashboard - Ocultos na página de empresa -->
                            <div class="dashboard-cards dashboard-overview-only">
                                <div class="row">
                            <div class="col-md-4">
                                <div class="card dashboard-card hover-lift animate-scale-in animate-delay-100">
                                    <div class="card-body">
                                        <div class="card-icon bg-primary">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div class="card-info">
                                            <h5 class="card-title">Total de Empresas</h5>
                                            <h3 class="card-value" id="total-empresas">0</h3>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <span>Empresas atribuídas ao usuário</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card dashboard-card hover-lift animate-scale-in animate-delay-200">
                                    <div class="card-body">
                                        <div class="card-icon bg-success">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                        <div class="card-info">
                                            <h5 class="card-title">Empresas Auditadas</h5>
                                            <h3 class="card-value" id="empresas-auditadas">0</h3>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <span>Com todos os impostos auditados</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card dashboard-card hover-lift animate-scale-in animate-delay-300">
                                    <div class="card-body">
                                        <div class="card-icon bg-warning">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="card-info">
                                            <h5 class="card-title">Empresas Pendentes</h5>
                                            <h3 class="card-value" id="empresas-pendentes">0</h3>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <span>Aguardando auditoria</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Lista de Empresas - Oculta na página de empresa -->
                    <div class="empresas-section mt-5 dashboard-overview-only">
                        <div class="section-header">
                            <h4><i class="fas fa-list"></i> Suas Empresas</h4>
                            <p class="text-muted">Clique em "Ver Detalhes" para acessar o dashboard específico da empresa</p>
                        </div>
                        <div id="empresas-list-container">
                            <!-- Lista será carregada dinamicamente -->
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="dashboard-entrada" role="tabpanel">
                    <div class="dashboard-cards dashboard-overview-only">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card dashboard-card hover-lift animate-scale-in animate-delay-100">
                                    <div class="card-body">
                                        <div class="card-icon bg-primary">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div class="card-info">
                                            <h5 class="card-title">Total de Empresas</h5>
                                            <h3 class="card-value" id="total-empresas-entrada">0</h3>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <span>Empresas atribuídas ao usuário</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card dashboard-card hover-lift animate-scale-in animate-delay-200">
                                    <div class="card-body">
                                        <div class="card-icon bg-success">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                        <div class="card-info">
                                            <h5 class="card-title">Empresas Auditadas</h5>
                                            <h3 class="card-value" id="empresas-auditadas-entrada">0</h3>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <span>Com todos os impostos auditados</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card dashboard-card hover-lift animate-scale-in animate-delay-300">
                                    <div class="card-body">
                                        <div class="card-icon bg-warning">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="card-info">
                                            <h5 class="card-title">Empresas Pendentes</h5>
                                            <h3 class="card-value" id="empresas-pendentes-entrada">0</h3>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <span>Aguardando auditoria</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="empresas-section mt-5 dashboard-overview-only">
                        <div class="section-header">
                            <h4><i class="fas fa-list"></i> Suas Empresas</h4>
                            <p class="text-muted">Clique em "Ver Detalhes" para acessar o dashboard específico da empresa</p>
                        </div>
                        <div id="empresas-list-container-entrada">
                            <!-- Lista será carregada dinamicamente -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

                <!-- Auditoria de Entrada Page -->
                <div id="page-auditoria-entrada" class="page-section animate-fade-in-up">
                    <div class="section-header animate-fade-in-down">
                        <h2><i class="fas fa-clipboard-check"></i> Auditoria de Entrada</h2>
                        <p class="text-muted">Execute auditorias comparativas entre XML, SPED e Cenários</p>
                    </div>

                    <!-- Card Principal de Auditoria Comparativa -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <a href="{{ url_for('main.auditoria_entrada_auditoria') }}" class="card-link-wrapper animate-scale-in animate-delay-100">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-primary">
                                            <i class="fas fa-balance-scale"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">Auditoria Comparativa</h3>
                                            <p class="card-description">Compare dados entre XML, SPED e Cenários configurados para identificar divergências</p>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- Sub-Cards por Tributo -->
                    <div class="section-header">
                        <h4><i class="fas fa-tachometer-alt"></i> Auditoria por Tributo</h4>
                        <p class="text-muted">Acesso direto à auditoria de tributos específicos</p>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-4">
                            <a href="{{ url_for('main.auditoria_entrada_auditoria', tributo='escrituracao') }}" class="card-link-wrapper animate-scale-in animate-delay-200">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-info">
                                            <i class="fas fa-file-invoice"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">Escrituração</h3>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.auditoria_entrada_auditoria', tributo='icms') }}" class="card-link-wrapper animate-scale-in animate-delay-300">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-primary">
                                            <i class="fas fa-percentage"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">ICMS</h3>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.auditoria_entrada_auditoria', tributo='icms_st') }}" class="card-link-wrapper animate-scale-in animate-delay-400">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-success">
                                            <i class="fas fa-percentage"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">ICMS-ST</h3>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <a href="{{ url_for('main.auditoria_entrada_auditoria', tributo='ipi') }}" class="card-link-wrapper animate-scale-in animate-delay-500">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-warning">
                                            <i class="fas fa-industry"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">IPI</h3>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.auditoria_entrada_auditoria', tributo='pis') }}" class="card-link-wrapper animate-scale-in animate-delay-600">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-danger">
                                            <i class="fas fa-coins"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">PIS</h3>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.auditoria_entrada_auditoria', tributo='cofins') }}" class="card-link-wrapper animate-scale-in animate-delay-700">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-secondary">
                                            <i class="fas fa-coins"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">COFINS</h3>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Página de Auditoria Comparativa -->
                <div id="page-auditoria-entrada-auditoria" class="page-section animate-fade-in-up">
                    <div class="section-header animate-fade-in-down">
                        <h2><i class="fas fa-balance-scale"></i> Auditoria Comparativa</h2>
                        <p class="text-muted">Compare dados entre XML, SPED e Cenários configurados</p>
                    </div>

                    <!-- Navegação por Tributos -->
                    <ul class="nav nav-pills mb-4" id="tributos-nav" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" data-tributo="escrituracao" type="button">
                                <i class="fas fa-file-invoice"></i> Escrituração
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" data-tributo="icms" type="button">
                                <i class="fas fa-percentage"></i> ICMS
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" data-tributo="icms_st" type="button">
                                <i class="fas fa-percentage"></i> ICMS-ST
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" data-tributo="ipi" type="button">
                                <i class="fas fa-industry"></i> IPI
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" data-tributo="pis_cofins" type="button">
                                <i class="fas fa-coins"></i> PIS/COFINS
                            </button>
                        </li>
                    </ul>

                    <!-- Busca por Nota Fiscal -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-search me-2"></i>
                                Buscar Nota Fiscal para Auditoria
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-key"></i>
                                        </span>
                                        <input type="text"
                                               class="form-control"
                                               id="input-chave-nf"
                                               placeholder="Digite a chave da nota fiscal (44 dígitos)"
                                               maxlength="44">
                                        <button class="btn btn-primary" id="btn-buscar-nota">
                                            <i class="fas fa-search me-2"></i>
                                            Buscar
                                        </button>
                                    </div>
                                    <small class="text-muted">
                                        A chave da nota fiscal deve ter exatamente 44 caracteres
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Ações da Auditoria -->
                    <div class="row mb-4">
                        <div class="col-md-8 d-flex align-items-start flex-wrap gap-2">
                            <div class="input-group input-group-sm" style="width:150px;">
                                <span class="input-group-text">Dif. Máx</span>
                                <input type="number" step="0.01" min="0" class="form-control" id="tolerancia-escrituracao" value="0.01">
                            </div>
                            <button class="btn btn-info" id="btn-gerar-auditoria-tributo">
                                <i class="fas fa-calculator"></i> Gerar Auditoria
                            </button>
                            <button class="btn btn-info" id="btn-gerar-escrituracao-comparativa">
                                <i class="fas fa-sync"></i> Gerar Escrituração
                            </button>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group">
                                <button class="btn btn-outline-info" id="btn-exportar">
                                    <i class="fas fa-download"></i> Exportar
                                </button>
                                <button class="btn btn-outline-primary" id="btn-regras-tributo">
                                    <i class="fas fa-book"></i> Regras
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Conteúdo da Auditoria -->
                    <div id="auditoria-content">
                        <!-- O conteúdo será carregado dinamicamente via JavaScript -->
                    </div>
                </div>



                <!-- Página de Gestão de XMLs -->
                <div id="page-gestao-xmls" class="page-section animate-fade-in-up">
                    <div class="section-header animate-fade-in-down">
                        <h2><i class="fas fa-file-alt"></i> Gestão de XMLs</h2>
                        <p class="text-muted">Gerencie XMLs importados, identifique notas faltantes e valide dados</p>
                    </div>

                    <!-- Loading Toast -->
                    <div id="loading-toast" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
                        <div class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                            <div class="toast-header">
                                <div class="spinner-border spinner-border-sm me-2" role="status">
                                    <span class="visually-hidden">Carregando...</span>
                                </div>
                                <strong class="me-auto">Carregando dados</strong>
                                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                            </div>
                            <div class="toast-body">
                                Aguarde enquanto carregamos os dados dos XMLs...
                            </div>
                        </div>
                    </div>

                    <!-- Navegação por Abas -->
                    <ul class="nav nav-tabs mb-4" id="xmls-tabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="entrada-tab" data-bs-toggle="tab" data-tab="entrada" type="button" role="tab">
                                <i class="fas fa-arrow-down"></i> XMLs de Entrada
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="saida-tab" data-bs-toggle="tab" data-tab="saida" type="button" role="tab">
                                <i class="fas fa-arrow-up"></i> XMLs de Saída
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="faltantes-entrada-tab" data-bs-toggle="tab" data-tab="faltantes-entrada" type="button" role="tab">
                                <i class="fas fa-exclamation-triangle text-warning"></i> Notas Faltantes Entrada
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="faltantes-saida-tab" data-bs-toggle="tab" data-tab="faltantes-saida" type="button" role="tab">
                                <i class="fas fa-exclamation-triangle text-danger"></i> Notas Faltantes Saída
                            </button>
                        </li>
                    </ul>

                    <!-- Conteúdo das Abas -->
                    <div id="tab-content" class="tab-content">
                        <!-- O conteúdo será carregado dinamicamente via JavaScript -->
                    </div>
                </div>

                <!-- Auditoria de Tributo Específico (ICMS, ICMS-ST, IPI, etc.) -->
                <div id="page-auditoria-tributo" class="page-section auditoria-page">
                    <div class="section-header">
                        <h2><i class="fas fa-clipboard-check"></i> <span id="auditoria-tributo-titulo">Auditoria de Tributo</span></h2>
                    </div>

                    <div class="auditoria-actions mb-4">
                        <button id="btn-executar-auditoria" class="btn btn-primary">
                            <i class="fas fa-calculator"></i> Executar Auditoria
                        </button>
                    </div>

                    <div id="auditoria-dashboard-container">
                        <!-- O conteúdo do dashboard será carregado dinamicamente via JavaScript -->
                    </div>
                </div>

                <!-- Auditoria de Saída Page -->
                <div id="page-auditoria-saida" class="page-section">
                    <div class="section-header">
                        <h2><i class="fas fa-clipboard-check"></i> Auditoria de Saída</h2>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-4">
                            <a href="{{ url_for('main.auditoria_saida_tributo', tributo='icms') }}" class="card-link-wrapper" data-tipo="icms">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-primary">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">ICMS</h3>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.auditoria_saida_tributo', tributo='icms-st') }}" class="card-link-wrapper" data-tipo="icms-st">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-success">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">ICMS-ST</h3>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.auditoria_saida_tributo', tributo='difal') }}" class="card-link-wrapper" data-tipo="difal">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-info">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">DIFAL</h3>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <a href="{{ url_for('main.auditoria_saida_tributo', tributo='ipi') }}" class="card-link-wrapper" data-tipo="ipi">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-warning">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">IPI</h3>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.auditoria_saida_tributo', tributo='pis') }}" class="card-link-wrapper" data-tipo="pis">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-danger">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">PIS</h3>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.auditoria_saida_tributo', tributo='cofins') }}" class="card-link-wrapper" data-tipo="cofins">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-secondary">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">COFINS</h3>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Cenários de Entrada Page -->
                <div id="page-cenarios-entrada" class="page-section">
                    <div class="section-header">
                        <h2><i class="fas fa-sitemap"></i> Cenários de Entrada</h2>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-4">
                            <a href="{{ url_for('main.cenarios_entrada_tributo', tributo='icms') }}" class="card-link-wrapper" data-tipo="icms">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-primary">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">ICMS</h3>
                                            <div class="card-counter">
                                                <div class="counter-row">
                                                    <div class="counter-item novo">
                                                        <span class="counter-label">Novos</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                    <div class="counter-item producao">
                                                        <span class="counter-label">Produção</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.cenarios_entrada_tributo', tributo='icms_st') }}" class="card-link-wrapper" data-tipo="icms_st">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-success">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">ICMS-ST</h3>
                                            <div class="card-counter">
                                                <div class="counter-row">
                                                    <div class="counter-item novo">
                                                        <span class="counter-label">Novos</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                    <div class="counter-item producao">
                                                        <span class="counter-label">Produção</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.cenarios_entrada_tributo', tributo='difal') }}" class="card-link-wrapper" data-tipo="difal">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-info">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">DIFAL</h3>
                                            <div class="card-counter">
                                                <div class="counter-row">
                                                    <div class="counter-item novo">
                                                        <span class="counter-label">Novos</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                    <div class="counter-item producao">
                                                        <span class="counter-label">Produção</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <a href="{{ url_for('main.cenarios_entrada_tributo', tributo='ipi') }}" class="card-link-wrapper" data-tipo="ipi">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-warning">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">IPI</h3>
                                            <div class="card-counter">
                                                <div class="counter-row">
                                                    <div class="counter-item novo">
                                                        <span class="counter-label">Novos</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                    <div class="counter-item producao">
                                                        <span class="counter-label">Produção</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.cenarios_entrada_tributo', tributo='pis') }}" class="card-link-wrapper" data-tipo="pis">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-danger">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">PIS</h3>
                                            <div class="card-counter">
                                                <div class="counter-row">
                                                    <div class="counter-item novo">
                                                        <span class="counter-label">Novos</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                    <div class="counter-item producao">
                                                        <span class="counter-label">Produção</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.cenarios_entrada_tributo', tributo='cofins') }}" class="card-link-wrapper" data-tipo="cofins">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-secondary">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">COFINS</h3>
                                            <div class="card-counter">
                                                <div class="counter-row">
                                                    <div class="counter-item novo">
                                                        <span class="counter-label">Novos</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                    <div class="counter-item producao">
                                                        <span class="counter-label">Produção</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Cenários de Saída Page -->
                <div id="page-cenarios-saida" class="page-section">
                    <div class="section-header">
                        <h2><i class="fas fa-sitemap"></i> Cenários de Saída</h2>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-4">
                            <a href="{{ url_for('main.cenarios_saida_tributo', tributo='icms') }}" class="card-link-wrapper" data-tipo="icms">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-primary">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">ICMS</h3>
                                            <div class="card-counter">
                                                <div class="counter-row">
                                                    <div class="counter-item novo">
                                                        <span class="counter-label">Novos</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                    <div class="counter-item producao">
                                                        <span class="counter-label">Produção</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.cenarios_saida_tributo', tributo='icms_st') }}" class="card-link-wrapper" data-tipo="icms_st">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-success">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">ICMS-ST</h3>
                                            <div class="card-counter">
                                                <div class="counter-row">
                                                    <div class="counter-item novo">
                                                        <span class="counter-label">Novos</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                    <div class="counter-item producao">
                                                        <span class="counter-label">Produção</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.cenarios_saida_tributo', tributo='difal') }}" class="card-link-wrapper" data-tipo="difal">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-info">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">DIFAL</h3>
                                            <div class="card-counter">
                                                <div class="counter-row">
                                                    <div class="counter-item novo">
                                                        <span class="counter-label">Novos</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                    <div class="counter-item producao">
                                                        <span class="counter-label">Produção</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <a href="{{ url_for('main.cenarios_saida_tributo', tributo='ipi') }}" class="card-link-wrapper" data-tipo="ipi">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-warning">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">IPI</h3>
                                            <div class="card-counter">
                                                <div class="counter-row">
                                                    <div class="counter-item novo">
                                                        <span class="counter-label">Novos</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                    <div class="counter-item producao">
                                                        <span class="counter-label">Produção</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.cenarios_saida_tributo', tributo='pis') }}" class="card-link-wrapper" data-tipo="pis">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-danger">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">PIS</h3>
                                            <div class="card-counter">
                                                <div class="counter-row">
                                                    <div class="counter-item novo">
                                                        <span class="counter-label">Novos</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                    <div class="counter-item producao">
                                                        <span class="counter-label">Produção</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('main.cenarios_saida_tributo', tributo='cofins') }}" class="card-link-wrapper" data-tipo="cofins">
                                <div class="card fiscal-card">
                                    <div class="card-body">
                                        <div class="card-icon bg-secondary">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                        <div class="card-info">
                                            <h3 class="card-title">COFINS</h3>
                                            <div class="card-counter">
                                                <div class="counter-row">
                                                    <div class="counter-item novo">
                                                        <span class="counter-label">Novos</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                    <div class="counter-item producao">
                                                        <span class="counter-label">Produção</span>
                                                        <span class="counter-value">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Empresas Page -->
                <div id="page-empresas" class="page-section">
                    <div class="section-header">
                        <h2><i class="fas fa-building"></i> Empresas</h2>
                        <button class="btn btn-primary" id="new-empresa-btn">
                            <i class="fas fa-plus"></i> Nova Empresa
                        </button>
                    </div>
                    <div id="empresas-content">
                        <!-- Conteúdo será carregado via JavaScript -->
                    </div>
                </div>

                <!-- Usuários Page -->
                <div id="page-usuarios" class="page-section">
                    <div class="section-header">
                        <h2><i class="fas fa-users"></i> Usuários</h2>
                        <!-- <button class="btn btn-primary" id="new-usuario-btn">
                            <i class="fas fa-plus"></i> Novo Usuário
                        </button> -->
                    </div>
                    <div id="usuarios-content">
                        <!-- Conteúdo será carregado via JavaScript -->
                    </div>
                </div>

                <!-- Escritórios Page -->
                <div id="page-escritorios" class="page-section">
                    <div class="section-header">
                        <h2><i class="fas fa-briefcase"></i> Escritórios</h2>
                    </div>
                    <div class="content-area">
                        <!-- Conteúdo será carregado via JavaScript -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal para formulários -->
    <div class="modal fade" id="formModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">Título do Modal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- Conteúdo do modal será carregado dinamicamente -->
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Socket.IO Client -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <!-- Chart.js JS (carregado antes dos scripts customizados) -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.3/dist/chart.umd.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <!-- Chart.js JS -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/custom-dropdown.js') }}"></script>
    <script src="{{ url_for('static', filename='js/common-ui.js') }}"></script>
    <script src="{{ url_for('static', filename='js/tributo_functions.js') }}"></script>
    <script src="{{ url_for('static', filename='js/tributo_historico.js') }}"></script>
    <script src="{{ url_for('static', filename='js/auditoria_progress.js') }}"></script>
    <script src="{{ url_for('static', filename='js/auditoria_fiscal.js') }}"></script>
    <script src="{{ url_for('static', filename='js/auditoria_dashboard.js') }}"></script>
    <script src="{{ url_for('static', filename='js/tributo_modal_functions.js') }}"></script>
    <script src="{{ url_for('static', filename='js/tributo_modal_enhanced.js') }}"></script>
    <script src="{{ url_for('static', filename='js/cenario_modal_enhanced.js') }}"></script>
    <script src="{{ url_for('static', filename='js/cenarios_filtros_dropdown.js') }}"></script>
    <script src="{{ url_for('static', filename='js/cenarios_detalhes.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dashboard_novo.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dashboard_empresa.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dashboard_empresa_graficos.js') }}"></script>
    <script src="{{ url_for('static', filename='js/cenarios.js') }}"></script>
    <script src="{{ url_for('static', filename='js/usuarios.js') }}"></script>
    <script src="{{ url_for('static', filename='js/empresas.js') }}"></script>
    <script src="{{ url_for('static', filename='js/escritorios.js') }}"></script>
    <script src="{{ url_for('static', filename='js/importacao.js') }}"></script>
    <script src="{{ url_for('static', filename='js/clientes.js') }}"></script>
    <script src="{{ url_for('static', filename='js/produtos.js') }}"></script>
    <script src="{{ url_for('static', filename='js/perfil_dashboard.js') }}"></script>
    <script src="{{ url_for('static', filename='js/auditoria_entrada.js') }}"></script>
    <script src="{{ url_for('static', filename='js/notas_faltantes.js') }}"></script>
    <script src="{{ url_for('static', filename='js/apuracao.js') }}"></script>
    <script src="{{ url_for('static', filename='js/apuracao_graficos.js') }}"></script>
    <script src="{{ url_for('static', filename='js/fiscal_analysis_rules.js') }}"></script>
    <script src="{{ url_for('static', filename='js/fiscal_analysis_modals.js') }}"></script>
    <script src="{{ url_for('static', filename='js/tributo_rules_modal.js') }}"></script>
    <script src="{{ url_for('static', filename='js/auditoria_comparativa_init.js') }}"></script>
    <script src="{{ url_for('static', filename='js/auditoria_comparativa.js') }}"></script>
    <script src="{{ url_for('static', filename='js/auditoria_comparativa_analysis.js') }}"></script>
    <script src="{{ url_for('static', filename='js/auditoria_comparativa_matching_manual.js') }}"></script>
    <script src="{{ url_for('static', filename='js/auditoria_comparativa_websocket.js') }}"></script>
    <script src="{{ url_for('static', filename='js/auditoria_comparativa_filtros.js') }}"></script>
    <!-- Chatbot IA -->
    <script src="{{ url_for('static', filename='js/chatbot_ia.js') }}"></script>
    </body>

</html>