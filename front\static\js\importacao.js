/**
 * importacao.js - Auditoria Fiscal
 * Funções para gerenciar a importação de arquivos XML
 */

let importacaoInterval = null;
// Mantém o controle das importações que já entraram na sala WebSocket
const joinedImports = new Set();
// Controle de tempo para importações
const importTimers = new Map();

document.addEventListener('DOMContentLoaded', function () {
  // Verificar se estamos na página de importação
  if (window.location.pathname === '/fiscal/importacao') {
    setupImportacaoPage();

    // Configurar WebSocket global
    window.socket = setupWebSocket();

    // Verificar importações ativas após um pequeno delay
    setTimeout(() => {
      verificarImportacoesAtivas();
      if (!importacaoInterval) {
        importacaoInterval = setInterval(verificarImportacoesAtivas, 5000);
      }
    }, 1000);
  }
});

window.addEventListener('beforeunload', () => {
  if (importacaoInterval) {
    clearInterval(importacaoInterval);
    importacaoInterval = null;
  }
});

/**
 * Configura a página de importação
 */
function setupImportacaoPage() {
  // Carregar o conteúdo da página
  loadImportacaoContent();
}

/**
 * Carrega o conteúdo da página de importação
 */
function loadImportacaoContent() {
  const pageContent = document.getElementById('page-content');
  if (!pageContent) return;

  // Verificar se a seção já existe
  let importacaoSection = document.getElementById('page-importacao');
  if (importacaoSection) {
    importacaoSection.classList.add('active');
    resetImportResults();
    loadImportacaoData();
    return;
  }

  // Criar a seção de importação
  importacaoSection = document.createElement('div');
  importacaoSection.id = 'page-importacao';
  importacaoSection.className = 'page-section active';

  // Conteúdo HTML da página
  importacaoSection.innerHTML = `
    <div class="section-header">
      <h2><i class="fas fa-file-import"></i> Importação de Arquivos</h2>
    </div>

    <!-- Abas para XML e SPED -->
    <ul class="nav nav-tabs mb-4" id="import-tabs" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="xml-tab" data-bs-toggle="tab" data-bs-target="#xml-panel" type="button" role="tab">
          <i class="fas fa-file-code"></i> Importação XML
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="sped-tab" data-bs-toggle="tab" data-bs-target="#sped-panel" type="button" role="tab">
          <i class="fas fa-file-alt"></i> Importação SPED
        </button>
      </li>
    </ul>

    <div class="tab-content" id="import-tab-content">
      <!-- Painel XML -->
      <div class="tab-pane fade show active" id="xml-panel" role="tabpanel">
        <div class="card mb-4">
          <div class="card-body">
            <h5 class="card-title">Importar Arquivo XML</h5>
            <p class="card-text">Selecione um arquivo XML para importar os dados fiscais.</p>

            <form id="import-xml-form" enctype="multipart/form-data">
              <div class="mb-3">
                <label for="xml-file" class="form-label">Arquivos XML ou ZIP</label>
                <input type="file" class="form-control" id="xml-file" accept=".xml,.zip" multiple required>
                <div class="form-text">
                  <strong>Novidade:</strong> Agora você pode enviar arquivos ZIP contendo múltiplos XMLs!<br>
                  • <strong>XML Individual:</strong> Selecione um arquivo .xml<br>
                  • <strong>ZIP:</strong> Selecione um arquivo .zip com múltiplos XMLs<br>
                  • <strong>Múltiplos XMLs:</strong> Selecione vários arquivos .xml<br>
                  O sistema identificará automaticamente as empresas pelos CNPJs dos emitentes.
                </div>
              </div>

              <div class="btn-group" role="group">
                <button type="submit" class="btn btn-primary" data-mode="single">
                  <i class="fas fa-upload"></i> Importar Individual
                </button>
                <button type="submit" class="btn btn-success" data-mode="batch">
                  <i class="fas fa-cloud-upload-alt"></i> Importar em Lote
                </button>
                <button type="submit" class="btn btn-warning" data-mode="optimized">
                  <i class="fas fa-rocket"></i> Importar Otimizado
                </button>
              </div>
            </form>

            <div id="import-xml-result" class="mt-3 d-none"></div>
          </div>
        </div>
      </div>

      <!-- Painel SPED -->
      <div class="tab-pane fade" id="sped-panel" role="tabpanel">
        <div class="card mb-4">
          <div class="card-body">
            <h5 class="card-title">Importar Arquivo SPED</h5>
            <p class="card-text">Selecione um arquivo SPED (.txt) para importar as notas de entrada.</p>

            <form id="import-sped-form" enctype="multipart/form-data">
              <div class="mb-3">
                <label for="sped-file" class="form-label">Arquivo SPED</label>
                <input type="file" class="form-control" id="sped-file" accept=".txt" required>
                <div class="form-text">
                  O sistema identificará automaticamente a empresa pelo CNPJ no arquivo SPED.
                  Certifique-se de que a empresa já está cadastrada no sistema.
                  Apenas notas de entrada (IND_OPER = 0) serão importadas.
                </div>
              </div>

              <button type="submit" class="btn btn-primary">
                <i class="fas fa-upload"></i> Importar SPED
              </button>
            </form>

            <div id="import-sped-result" class="mt-3 d-none"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="card">
      <div class="card-body">
        <h5 class="card-title">Histórico de Importações</h5>

        <!-- Abas para histórico -->
        <ul class="nav nav-pills mb-3" id="history-tabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link active" id="xml-history-tab" data-bs-toggle="pill" data-bs-target="#xml-history" type="button" role="tab">
              XML
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="sped-history-tab" data-bs-toggle="pill" data-bs-target="#sped-history" type="button" role="tab">
              SPED
            </button>
          </li>
        </ul>

        <div class="tab-content" id="history-tab-content">
          <div class="tab-pane fade show active" id="xml-history" role="tabpanel">
            <div id="importacoes-xml-content">
              <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Carregando...</span>
                </div>
              </div>
            </div>
          </div>
          <div class="tab-pane fade" id="sped-history" role="tabpanel">
            <div id="importacoes-sped-content">
              <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Carregando...</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  // Adicionar a seção ao conteúdo da página
  pageContent.appendChild(importacaoSection);

  // Configurar os formulários de importação
  setupImportForms();

  // Garantir que contêineres de resultado estejam limpos
  resetImportResults();

  // Carregar dados de importações
  loadImportacaoData();
}

/**
 * Configura os formulários de importação
 */
function setupImportForms() {
  // Configurar formulário XML
  const xmlForm = document.getElementById('import-xml-form');
  if (xmlForm) {
    xmlForm.addEventListener('submit', function (event) {
      event.preventDefault();
      const button = event.submitter;
      const mode = button.getAttribute('data-mode') || 'single';
      importarXML(mode);
    });
  }

  // Configurar formulário SPED
  const spedForm = document.getElementById('import-sped-form');
  if (spedForm) {
    spedForm.addEventListener('submit', function (event) {
      event.preventDefault();
      importarSPED();
    });
  }

  // Configurar abas de histórico
  const historyTabs = document.querySelectorAll(
    '#history-tabs button[data-bs-toggle="pill"]',
  );
  historyTabs.forEach((tab) => {
    tab.addEventListener('shown.bs.tab', function (event) {
      const target = event.target.getAttribute('data-bs-target');
      if (target === '#sped-history') {
        loadImportacaoSpedData();
      } else if (target === '#xml-history') {
        loadImportacaoXmlData();
      }
    });
  });
}

// Função loadEmpresasForSelect removida pois não é mais necessária

/**
 * Importa um arquivo XML
 */
function importarXML(mode = 'single') {
  const form = document.getElementById('import-xml-form');
  const resultDiv = document.getElementById('import-xml-result');
  const fileInput = document.getElementById('xml-file');

  if (!form || !resultDiv || !fileInput) return;

  // Verificar se arquivos foram selecionados
  if (!fileInput.files || fileInput.files.length === 0) {
    showImportResult('error', 'Selecione pelo menos um arquivo XML ou ZIP');
    return;
  }

  // Verificar modo de importação
  if (mode === 'single' && fileInput.files.length > 1) {
    showImportResult(
      'error',
      'Selecione apenas um arquivo para importação individual',
    );
    return;
  }

  // Verificar se há arquivo ZIP (usar modo otimizado automaticamente)
  const hasZipFile = Array.from(fileInput.files).some(file =>
    file.name.toLowerCase().endsWith('.zip')
  );

  if (hasZipFile && mode !== 'optimized') {
    // Se há arquivo ZIP, usar modo otimizado automaticamente
    mode = 'optimized';
    console.log('Arquivo ZIP detectado, usando modo otimizado automaticamente');
  }

  // Criar FormData para envio do(s) arquivo(s)
  const formData = new FormData();

  if (mode === 'single' || mode === 'optimized') {
    // Para modo single ou otimizado, enviar apenas o primeiro arquivo
    formData.append('arquivo', fileInput.files[0]);
  } else {
    // Para importação em lote, adicionar todos os arquivos
    Array.from(fileInput.files).forEach((file) => {
      formData.append('arquivos', file);
    });
  }

  // Mostrar indicador de carregamento
  let message;
  if (mode === 'optimized') {
    const fileName = fileInput.files[0].name;
    const isZip = fileName.toLowerCase().endsWith('.zip');
    message = isZip
      ? `Importando arquivo ZIP otimizado: ${fileName}...`
      : `Importando XML otimizado: ${fileName}...`;
  } else if (mode === 'single') {
    message = 'Importando arquivo...';
  } else {
    message = `Preparando importação de ${fileInput.files.length} arquivos...`;
  }

  const elementId = mode === 'single' || mode === 'optimized' ? 'import-xml-result' : 'import-result';
  showImportResult('loading', message, elementId);

  // Para importação em lote ou ZIP otimizado, configurar WebSocket
  let socket = null;
  let importId = null;
  const isZipOptimized = mode === 'optimized' && fileInput.files[0].name.toLowerCase().endsWith('.zip');
  if (mode === 'batch' || isZipOptimized) {
    importId = generateUUID();
    formData.append('import_id', importId);
    socket = setupWebSocket();
    if (socket) {
      socket.emit('join_import', {
        token: localStorage.getItem('token'),
        import_id: importId,
      });
      setupImportProgress(socket, importId, 0);
    }
  }

  // Enviar o(s) arquivo(s) para a API
  let endpoint;
  if (mode === 'optimized') {
    endpoint = '/fiscal/api/importacoes/optimized';
  } else if (mode === 'single') {
    endpoint = '/fiscal/api/importacoes';
  } else {
    endpoint = '/fiscal/api/importacoes/batch';
  }

  fetch(endpoint, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    body: formData,
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success || data.importacao || data.import_id || data.results) {
        if (mode === 'batch' && data.import_id) {
          // Importação em lote - configurar WebSocket para acompanhar progresso
          if (socket) {
            setupImportProgress(socket, data.import_id, data.total_files);
          } else {
            // Fallback se WebSocket não estiver disponível
            showImportResult(
              'info',
              'Importação iniciada. Aguarde a conclusão...',
            );
            // Polling como fallback (opcional)
            // pollImportStatus(data.import_id);
          }
        } else if (data.results) {
          // Resposta de importação em lote já concluída (fallback)
          const total = data.results.total;
          const success = data.results.success.length;
          const errors = data.results.errors.length;
          const invalid = data.invalid_files ? data.invalid_files.length : 0;

          let message = `Importação em lote concluída:<br>`;
          message += `- ${success} arquivo(s) importado(s) com sucesso<br>`;
          if (errors > 0) message += `- ${errors} arquivo(s) com erro<br>`;
          if (invalid > 0) message += `- ${invalid} arquivo(s) inválido(s)<br>`;

          showImportResult(errors === 0 ? 'success' : 'warning', message);
          loadImportacaoData();
          form.reset();
        } else if (mode === 'optimized') {
          // Importação otimizada - mostrar métricas de performance
          let message = data.message || 'Importação otimizada concluída com sucesso';

          // Adicionar métricas se disponíveis
          if (data.performance) {
            const perf = data.performance;
            const minutos = perf.total_time_seconds
              ? (perf.total_time_seconds / 60).toFixed(2)
              : 0;
            message += `<br><br><strong>📊 Métricas de Performance:</strong>`;
            message += `<br>• Tempo total: ${minutos || 0} minutos`;
            message += `<br>• XMLs processados: ${perf.total_xmls || 0}`;
            message += `<br>• Velocidade: ${(perf.xmls_per_second || 0).toFixed(2)} XMLs/s`;
            message += `<br>• Taxa de sucesso: ${(perf.success_rate || 0).toFixed(1)}%`;

            if (perf.cache_efficiency) {
              const cache = perf.cache_efficiency;
              message += `<br><br><strong>🚀 Eficiência do Cache:</strong>`;
              if (cache.clientes) {
                message += `<br>• Clientes: ${(cache.clientes.hit_rate || 0).toFixed(1)}% hit rate`;
              }
              if (cache.produtos) {
                message += `<br>• Produtos: ${(cache.produtos.hit_rate || 0).toFixed(1)}% hit rate`;
              }
            }
          }

          showImportResult('success', message, 'import-xml-result');
          loadImportacaoXmlData();
          form.reset();
        } else {
          // Importação individual
          showImportResult(
            'success',
            data.message || 'XML importado com sucesso',
            'import-xml-result',
          );
          loadImportacaoXmlData();
          form.reset();
        }
      } else {
        showImportResult(
          'error',
          data.message || 'Erro ao importar XML',
          'import-xml-result',
        );
      }
    })
    .catch((error) => {
      console.error('Erro ao importar XML:', error);
      showImportResult('error', 'Erro ao importar XML', 'import-xml-result');
    });
}

/**
 * Exibe o resultado da importação
 */
function showImportResult(type, message, elementId = 'import-result') {
  const resultDiv = document.getElementById(elementId);
  if (!resultDiv) return;

  // Limpar classes anteriores
  resultDiv.className = 'mt-3';

  // Configurar aparência com base no tipo
  if (type === 'loading') {
    resultDiv.classList.add('alert', 'alert-info');
    resultDiv.innerHTML = `
      <div class="d-flex align-items-center">
        <div class="spinner-border spinner-border-sm me-2" role="status">
          <span class="visually-hidden">Carregando...</span>
        </div>
        <span>${message}</span>
      </div>
    `;
  } else if (type === 'success') {
    resultDiv.classList.add('alert', 'alert-success');
    resultDiv.innerHTML = `<i class="fas fa-check-circle me-2"></i> ${message}`;
  } else if (type === 'warning') {
    resultDiv.classList.add('alert', 'alert-warning');
    resultDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i> ${message}`;
  } else if (type === 'error') {
    resultDiv.classList.add('alert', 'alert-danger');
    resultDiv.innerHTML = `<i class="fas fa-exclamation-circle me-2"></i> ${message}`;
  }
}

/**
 * Limpa os contêineres de resultado de importação
 */
function resetImportResults() {
  const xmlResult = document.getElementById('import-xml-result');
  if (xmlResult) {
    xmlResult.className = 'mt-3 d-none';
    xmlResult.innerHTML = '';
  }
  const spedResult = document.getElementById('import-sped-result');
  if (spedResult) {
    spedResult.className = 'mt-3 d-none';
    spedResult.innerHTML = '';
  }
}

/**
 * Remove elementos de progresso antigos (compatibilidade)
 */
function cleanupOldProgressElements() {
  // Remove barras de progresso antigas do Bootstrap
  const oldProgressBars = document.querySelectorAll('.progress-bar-striped');
  oldProgressBars.forEach(bar => {
    const container = bar.closest('.alert');
    if (container) {
      container.remove();
    }
  });
}

/**
 * Inicia o cronômetro para uma importação
 */
function startImportTimer(importId) {
  if (importTimers.has(importId)) {
    clearInterval(importTimers.get(importId).interval);
  }

  const startTime = Date.now();
  const interval = setInterval(() => {
    updateElapsedTime(importId, startTime);
  }, 1000);

  importTimers.set(importId, {
    startTime,
    interval
  });
}

/**
 * Para o cronômetro para uma importação
 */
function stopImportTimer(importId) {
  if (importTimers.has(importId)) {
    clearInterval(importTimers.get(importId).interval);
    importTimers.delete(importId);
  }
}

/**
 * Atualiza o tempo decorrido na interface
 */
function updateElapsedTime(importId, startTime) {
  const elapsedSeconds = Math.floor((Date.now() - startTime) / 1000);
  const minutes = Math.floor(elapsedSeconds / 60);
  const seconds = elapsedSeconds % 60;
  
  const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  
  // Atualizar tempo para XML
  const timeElement = document.getElementById('import-elapsed-time');
  if (timeElement) {
    timeElement.textContent = timeString;
  }
  
  // Atualizar tempo para SPED
  const spedTimeElement = document.getElementById('sped-elapsed-time');
  if (spedTimeElement) {
    spedTimeElement.textContent = timeString;
  }
}

/**
 * Formata tempo em segundos para MM:SS
 */
function formatElapsedTime(seconds) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Carrega os dados de importações (função principal)
 */
function loadImportacaoData() {
  // Carregar dados XML por padrão
  loadImportacaoXmlData();
}

/**
 * Gera um UUID simples (usa crypto.randomUUID quando disponível)
 */
function generateUUID() {
  if (window.crypto && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * Configura WebSocket para acompanhar progresso da importação com reconexão automática
 */
function setupWebSocket() {
  try {
    // Verificar se Socket.IO está disponível
    if (typeof io === 'undefined') {
      console.warn('Socket.IO não está disponível');
      return null;
    }

    const socket = io({
      autoConnect: true,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: 5,
      timeout: 20000,
      path: '/fiscal/socket.io'
    });

    socket.on('connect', () => {
      console.log('WebSocket conectado');

      // Verificar se há importações ativas ao conectar
      verificarImportacoesAtivas();
    });

    socket.on('disconnect', () => {
      console.log('WebSocket desconectado');
      joinedImports.clear();
    });

    socket.on('connect_error', (error) => {
      console.error('Erro de conexão WebSocket:', error);
    });

    socket.on('reconnect', (attemptNumber) => {
      console.log('WebSocket reconectado após', attemptNumber, 'tentativas');

      // Verificar importações ativas após reconexão
      verificarImportacoesAtivas();
    });

    return socket;
  } catch (error) {
    console.error('Erro ao configurar WebSocket:', error);
    return null;
  }
}

/**
 * Verifica se há importações ativas e reconecta aos WebSockets necessários
 */
function verificarImportacoesAtivas() {
  fetch('/fiscal/api/importacoes/ativas', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      if (data.importacoes.length > 0) {
        console.log('Importações ativas encontradas:', data.importacoes.length);

        // Reconectar aos WebSockets das importações ativas
        data.importacoes.forEach(importacao => {
          if (importacao.tipo === 'xml_batch' || importacao.tipo === 'xml_zip') {
            reconectarImportacaoXML(importacao);
          } else if (importacao.tipo === 'sped') {
            reconectarImportacaoSPED(importacao);
          }
        });
      } else if (importacaoInterval) {
        // Nenhuma importação ativa, parar verificação periódica
        clearInterval(importacaoInterval);
        importacaoInterval = null;
      }
    }
  })
  .catch(error => {
    console.error('Erro ao verificar importações ativas:', error);
  });
}

/**
 * Reconecta a uma importação XML em andamento
 */
function reconectarImportacaoXML(importacao) {
  console.log('Reconectando à importação XML:', importacao.id);

  if (joinedImports.has(importacao.id)) {
    // Já estamos acompanhando esta importação via WebSocket
    return;
  }

  // Mostrar barra de progresso com o estado atual salvo no banco
  showProgressBar(
    importacao.arquivos_processados || 0,
    importacao.total_arquivos || 0,
  );

  updateProgressBar(
    importacao.arquivos_processados || 0,
    importacao.total_arquivos || 0,
    importacao.mensagem_atual || 'Processando...',
    importacao.arquivos_sucesso || 0,
    importacao.arquivos_erro || 0,
  );

  // Conectar ao WebSocket e configurar listeners
  if (window.socket) {
    setupImportProgress(
      window.socket,
      importacao.id,
      importacao.total_arquivos || 0,
    );
  }
}

/**
 * Reconecta a uma importação SPED em andamento
 */
function reconectarImportacaoSPED(importacao) {
  console.log('Reconectando à importação SPED:', importacao.id);

  if (joinedImports.has(importacao.id)) {
    // Já estamos acompanhando esta importação
    return;
  }

  // Mostrar barra de progresso inicial com o estado salvo
  showSpedProgressBar(
    importacao.mensagem_atual || 'Reconectando...',
    importacao.porcentagem || 0,
  );

  updateSpedProgressBar({
    porcentagem: importacao.porcentagem || 0,
    mensagem: importacao.mensagem_atual || 'Processando...'
  });

  if (window.socket) {
    window.socket.emit('join_import', {
      token: localStorage.getItem('token'),
      import_id: importacao.id,
    });
    setupSpedWebSocketListeners(importacao.id);
  }
}

/**
 * Configura o acompanhamento de progresso da importação
 */
function setupImportProgress(socket, importId, totalFiles) {
  if (!socket || !importId) {
    console.warn('Socket ou importId não fornecidos');
    return;
  }

  if (joinedImports.has(importId)) {
    return;
  }
  joinedImports.add(importId);

  // Aguardar um pouco para garantir que a conexão WebSocket esteja estabelecida
  setTimeout(() => {
    // Entrar na sala da importação
    socket.emit('join_import', {
      token: localStorage.getItem('token'),
      import_id: importId,
    });

    console.log(`Entrando na sala de importação: ${importId}`);
  }, 100);

  // Configurar barra de progresso
  if (totalFiles && totalFiles > 0) {
    showProgressBar(0, totalFiles);
    // Iniciar cronômetro imediatamente
    startImportTimer(importId);
  }

  socket.on('import_start', (data) => {
    const total = data.total_files || data.total || totalFiles || 0;
    showProgressBar(0, total);
    // Iniciar cronômetro
    startImportTimer(importId);
  });

  // Escutar atualizações de progresso
  socket.on('import_progress', (data) => {
    updateProgressBar(
      data.processed,
      data.total,
      data.current_file,
      data.success_count,
      data.error_count,
    );
  });

  // Escutar conclusão da importação
  socket.on('import_complete', (data) => {
    joinedImports.delete(importId);
    stopImportTimer(importId);
    handleImportComplete(data);
    socket.off('import_start');
    socket.off('import_progress');
    socket.off('import_complete');
    socket.off('import_error');
    socket.disconnect();
  });

  // Escutar erros da importação
  socket.on('import_error', (data) => {
    joinedImports.delete(importId);
    stopImportTimer(importId);
    handleImportError(data);
    socket.off('import_start');
    socket.off('import_progress');
    socket.off('import_complete');
    socket.off('import_error');
    socket.disconnect();
  });
}

/**
 * Mostra a barra de progresso
 */
function showProgressBar(processed, total) {
  const resultDiv = document.getElementById('import-xml-result');
  if (!resultDiv) return;

  const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;

  resultDiv.className = 'mt-3';
  resultDiv.innerHTML = `
    <div class="import-progress-container">
      <div class="import-progress-header">
        <div class="import-progress-title">
          <i class="fas fa-cloud-upload-alt me-2"></i>
          <span>Importando arquivos XML</span>
        </div>
        <div class="import-progress-counter">
          <span class="counter-current">${processed}</span>
          <span class="counter-separator">/</span>
          <span class="counter-total">${total}</span>
        </div>
      </div>
      
      <div class="import-progress-bar-container">
        <div class="import-progress-bar">
          <div class="import-progress-fill" 
               style="width: ${percentage}%"
               data-percentage="${percentage}">
            <div class="import-progress-shine"></div>
          </div>
        </div>
        <div class="import-progress-percentage">${percentage}%</div>
      </div>
      
      <div class="import-progress-info-row">
        <div class="current-file-info">
          <i class="fas fa-file-code me-1"></i>
          <span class="current-file-label">Processando:</span>
          <span id="current-file" class="current-file-name">Iniciando...</span>
        </div>
        
        <div class="import-time-info">
          <i class="fas fa-clock"></i>
          <span id="import-elapsed-time">00:00</span>
        </div>
        
        <div class="import-status-indicators">
          <div class="status-indicator success-indicator">
            <div class="status-icon">
              <i class="fas fa-check"></i>
            </div>
            <div class="status-info">
              <span class="status-count" id="success-count">0</span>
              <span class="status-label">Sucessos</span>
            </div>
          </div>
          
          <div class="status-indicator error-indicator">
            <div class="status-icon">
              <i class="fas fa-times"></i>
            </div>
            <div class="status-info">
              <span class="status-count" id="error-count">0</span>
              <span class="status-label">Erros</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

/**
 * Atualiza a barra de progresso
 */
function updateProgressBar(
  processed,
  total,
  currentFile,
  successCount,
  errorCount,
) {
  const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;

  // Atualizar barra de progresso
  const progressFill = document.querySelector('.import-progress-fill');
  if (progressFill) {
    progressFill.style.width = `${percentage}%`;
    progressFill.setAttribute('data-percentage', percentage);
  }

  // Atualizar porcentagem
  const progressPercentage = document.querySelector('.import-progress-percentage');
  if (progressPercentage) {
    progressPercentage.textContent = `${percentage}%`;
  }

  // Atualizar contador
  const counterCurrent = document.querySelector('.counter-current');
  const counterTotal = document.querySelector('.counter-total');
  if (counterCurrent) counterCurrent.textContent = processed;
  if (counterTotal) counterTotal.textContent = total;

  // Atualizar arquivo atual
  const currentFileSpan = document.getElementById('current-file');
  if (currentFileSpan && currentFile) {
    currentFileSpan.textContent = currentFile;
  }

  // Atualizar contadores de sucesso e erro
  const successSpan = document.getElementById('success-count');
  const errorSpan = document.getElementById('error-count');
  if (successSpan) successSpan.textContent = successCount || 0;
  if (errorSpan) errorSpan.textContent = errorCount || 0;

  // Adicionar animação de pulso nos indicadores quando há mudanças
  if (successCount > 0) {
    const successIndicator = document.querySelector('.success-indicator');
    if (successIndicator) {
      successIndicator.classList.add('pulse-animation');
      setTimeout(() => successIndicator.classList.remove('pulse-animation'), 600);
    }
  }

  if (errorCount > 0) {
    const errorIndicator = document.querySelector('.error-indicator');
    if (errorIndicator) {
      errorIndicator.classList.add('pulse-animation');
      setTimeout(() => errorIndicator.classList.remove('pulse-animation'), 600);
    }
  }
}

/**
 * Trata a conclusão da importação
 */
function handleImportComplete(data) {
  const results = data.results || {};
  const performance = results.performance;
  const errorList = results.errors || [];

  if (performance) {
    // Importação otimizada (ex.: ZIP) com métricas de performance
    let message = results.message || 'Importação ZIP otimizada concluída';

    const minutos = performance.total_time_seconds
      ? (performance.total_time_seconds / 60).toFixed(2)
      : 0;
    message += `<br><br><strong>📊 Métricas de Performance:</strong>`;
    message += `<br>• Tempo total: ${minutos || 0} minutos`;
    message += `<br>• XMLs processados: ${performance.total_xmls || 0}`;
    message += `<br>• Velocidade: ${(performance.xmls_per_second || 0).toFixed(2)} XMLs/s`;
    message += `<br>• Taxa de sucesso: ${(performance.success_rate || 0).toFixed(1)}%`;

    if (errorList.length > 0) {
      const nomes = errorList
        .map((e) => e.filename || e.file || '')
        .filter((n) => n)
        .join(', ');
      if (nomes) {
        message += `<br><br><strong>Arquivos com erro:</strong><br>${nomes}`;
      }
    }

    showImportResult(
      errorList.length === 0 ? 'success' : 'warning',
      message,
      'import-xml-result',
    );
  } else {
    const success =
      results.success?.length ?? results.successful_imports ?? 0;
    const errors =
      results.errors?.length ?? results.failed_imports ?? 0;
    const invalid = data.invalid_files ? data.invalid_files.length : 0;

    let message = `Importação concluída:<br>`;
    message += `- ${success} arquivo(s) importado(s) com sucesso<br>`;
    if (errors > 0) message += `- ${errors} arquivo(s) com erro<br>`;
    if (invalid > 0) message += `- ${invalid} arquivo(s) inválido(s)<br>`;

    showImportResult(
      errors === 0 ? 'success' : 'warning',
      message,
      'import-xml-result',
    );
  }
  // Recarregar dados e limpar formulário
  loadImportacaoData();
  const form = document.getElementById('import-xml-form');
  if (form) form.reset();
}

/**
 * Trata erros da importação
 */
function handleImportError(data) {
  showImportResult(
    'error',
    data.message || 'Erro durante a importação',
    'import-xml-result',
  );
}

/**
 * Mostra a barra de progresso da importação SPED
 */
function showSpedProgressBar(message, percentage) {
  const resultDiv = document.getElementById('import-sped-result');
  if (!resultDiv) return;

  const pct = percentage || 0;

  resultDiv.className = 'mt-3';
  resultDiv.innerHTML = `
    <div class="import-progress-container sped-progress">
      <div class="import-progress-header">
        <div class="import-progress-title">
          <i class="fas fa-file-alt me-2"></i>
          <span>Importando arquivo SPED</span>
        </div>
        <div class="import-progress-counter">
          <span class="counter-current">${pct}</span>
          <span class="counter-separator">%</span>
        </div>
      </div>
      
      <div class="import-progress-bar-container">
        <div class="import-progress-bar sped-bar">
          <div class="import-progress-fill sped-fill" 
               style="width: ${pct}%"
               data-percentage="${pct}">
            <div class="import-progress-shine"></div>
          </div>
        </div>
        <div class="import-progress-percentage">${pct}%</div>
      </div>
      
      <div class="import-progress-info-row">
        <div class="current-file-info">
          <i class="fas fa-cogs me-1"></i>
          <span class="current-file-label">Status:</span>
          <span id="sped-progress-message" class="current-file-name">${message}</span>
        </div>
        
        <div class="import-time-info">
          <i class="fas fa-clock"></i>
          <span id="sped-elapsed-time">00:00</span>
        </div>
      </div>
    </div>
  `;
}

/**
 * Atualiza a barra de progresso da importação SPED
 */
function updateSpedProgressBar(data) {
  const percentage = data.porcentagem || 0;
  const message = data.mensagem || '';

  // Atualizar barra de progresso SPED
  const progressFill = document.querySelector('.sped-fill');
  if (progressFill) {
    progressFill.style.width = `${percentage}%`;
    progressFill.setAttribute('data-percentage', percentage);
  }

  // Atualizar porcentagem
  const progressPercentage = document.querySelector('.sped-progress .import-progress-percentage');
  if (progressPercentage) {
    progressPercentage.textContent = `${percentage}%`;
  }

  // Atualizar contador
  const counterCurrent = document.querySelector('.sped-progress .counter-current');
  if (counterCurrent) {
    counterCurrent.textContent = percentage;
  }

  // Atualizar mensagem de status
  const messageSpan = document.getElementById('sped-progress-message');
  if (messageSpan) {
    messageSpan.textContent = message;
  }
}

/**
 * Importa um arquivo SPED
 */
function importarSPED() {
  const form = document.getElementById('import-sped-form');
  const resultDiv = document.getElementById('import-sped-result');
  const fileInput = document.getElementById('sped-file');

  if (!form || !resultDiv || !fileInput) return;

  // Verificar se arquivo foi selecionado
  if (!fileInput.files || fileInput.files.length === 0) {
    showImportResult(
      'error',
      'Selecione um arquivo SPED',
      'import-sped-result',
    );
    return;
  }

  // Gerar import_id para identificar a importação
  const importId = generateUUID();

  // Criar FormData para envio do arquivo
  const formData = new FormData();
  formData.append('arquivo', fileInput.files[0]);
  formData.append('import_id', importId);

  // Criar conexão WebSocket caso ainda não exista
  if (!window.socket) {
    window.socket = setupWebSocket();
  }

  if (window.socket) {
    window.socket.emit('join_import', {
      token: localStorage.getItem('token'),
      import_id: importId,
    });
    setupSpedWebSocketListeners(importId);
  }

  showSpedProgressBar('Iniciando importação SPED...', 0);
  
  // Iniciar cronômetro para SPED
  startImportTimer(importId);

  // Enviar o arquivo para a API
  fetch('/fiscal/api/importacoes/sped', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    body: formData,
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success || data.importacao) {
        if (!data.import_id) {
          const totais = data.totais || {};
          let message = 'SPED importado com sucesso!<br>';
          message += `- ${totais.notas || 0} nota(s) de entrada<br>`;
          message += `- ${totais.itens || 0} item(ns)<br>`;
          message += `- ${totais.clientes || 0} fornecedor(es)<br>`;
          message += `- ${totais.produtos || 0} produto(s)`;

          showImportResult('success', message, 'import-sped-result');
          loadImportacaoSpedData();
          form.reset();
        }
      } else {
        showImportResult(
          'error',
          data.message || 'Erro ao importar SPED',
          'import-sped-result',
        );
      }
    })
    .catch((error) => {
      console.error('Erro ao importar SPED:', error);
      showImportResult(
        'error',
        'Erro ao processar solicitação',
        'import-sped-result',
      );
    });
}

/**
 * Configura os listeners WebSocket para importação SPED
 */
function setupSpedWebSocketListeners(importId) {
  if (!window.socket) return;

  if (!joinedImports.has(importId)) {
    joinedImports.add(importId);
  }

  // Mostrar barra de progresso inicial
  showSpedProgressBar('Iniciando importação SPED...', 0);

  // Listener para início da importação SPED
  window.socket.on('sped_import_start', (data) => {
    console.log('SPED import started:', data);
    showSpedProgressBar(data.message || 'Iniciando importação SPED...', 0);
    // Iniciar cronômetro
    startImportTimer(importId);
  });

  // Listener para progresso da importação SPED
  window.socket.on('sped_import_progress', (data) => {
    console.log('SPED import progress:', data);
    updateSpedProgressBar(data);
  });

  // Listener para conclusão da importação SPED
  window.socket.on('sped_import_complete', (data) => {
    console.log('SPED import completed:', data);

    // Alguns backends enviam os totais diretamente (total_notas, etc.)
    // enquanto outros usam a estrutura { totais: { notas, itens, ... } }.
    const totals = {
      notas: data.total_notas ?? (data.totais ? data.totais.notas : undefined),
      itens: data.total_itens ?? (data.totais ? data.totais.itens : undefined),
      clientes:
        data.total_clientes ?? (data.totais ? data.totais.clientes : undefined),
      produtos:
        data.total_produtos ?? (data.totais ? data.totais.produtos : undefined),
    };

    let message = 'SPED importado com sucesso!<br>';
    message += `- ${totals.notas || 0} nota(s) de entrada<br>`;
    message += `- ${totals.itens || 0} item(ns)<br>`;
    message += `- ${totals.clientes || 0} fornecedor(es)<br>`;
    message += `- ${totals.produtos || 0} produto(s)`;
    showImportResult('success', message, 'import-sped-result');
    loadImportacaoSpedData();

    // Parar cronômetro
    stopImportTimer(importId);

    // Limpar formulário
    const form = document.getElementById('import-sped-form');
    if (form) form.reset();

    // Limpar listeners
    cleanupSpedWebSocketListeners();
    joinedImports.delete(importId);
  });

  // Listener para erro na importação SPED
  window.socket.on('sped_import_error', (data) => {
    console.log('SPED import error:', data);

    showImportResult(
      'error',
      data.message || 'Erro ao importar SPED',
      'import-sped-result',
    );

    // Parar cronômetro
    stopImportTimer(importId);

    // Limpar listeners
    cleanupSpedWebSocketListeners();
    joinedImports.delete(importId);
  });
}

/**
 * Remove os listeners WebSocket para importação SPED
 */
function cleanupSpedWebSocketListeners() {
  if (!window.socket) return;

  window.socket.off('sped_import_start');
  window.socket.off('sped_import_progress');
  window.socket.off('sped_import_complete');
  window.socket.off('sped_import_error');
}

/**
 * Carrega os dados de importações XML
 */
function loadImportacaoXmlData() {
  const importacoesContent = document.getElementById('importacoes-xml-content');
  if (!importacoesContent) return;

  // Mostrar indicador de carregamento
  importacoesContent.innerHTML = `
    <div class="text-center py-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Carregando...</span>
      </div>
    </div>
  `;

  // Obter importações da API
  fetch('/fiscal/api/importacoes', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.importacoes && data.importacoes.length > 0) {
        // Renderizar tabela de importações XML
        renderImportacoesXmlTable(importacoesContent, data.importacoes);
      } else {
        // Mostrar mensagem de nenhuma importação
        importacoesContent.innerHTML = `
          <div class="alert alert-info">
            Nenhuma importação XML encontrada.
          </div>
        `;
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar importações XML:', error);
      importacoesContent.innerHTML = `
        <div class="alert alert-danger">
          Erro ao carregar importações XML: ${error.message}
        </div>
      `;
    });
}

/**
 * Carrega os dados de importações SPED
 */
function loadImportacaoSpedData() {
  const importacoesContent = document.getElementById(
    'importacoes-sped-content',
  );
  if (!importacoesContent) return;

  // Mostrar indicador de carregamento
  importacoesContent.innerHTML = `
    <div class="text-center py-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Carregando...</span>
      </div>
    </div>
  `;

  // Obter importações da API
  fetch('/fiscal/api/importacoes/sped/historico', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.importacoes && data.importacoes.length > 0) {
        // Renderizar tabela de importações SPED
        renderImportacoesSpedTable(importacoesContent, data.importacoes);
      } else {
        // Mostrar mensagem de nenhuma importação
        importacoesContent.innerHTML = `
          <div class="alert alert-info">
            Nenhuma importação SPED encontrada.
          </div>
        `;
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar importações SPED:', error);
      importacoesContent.innerHTML = `
        <div class="alert alert-danger">
          Erro ao carregar importações SPED: ${error.message}
        </div>
      `;
    });
}

/**
 * Renderiza a tabela de importações XML
 */
function renderImportacoesXmlTable(container, importacoes) {
  // Criar HTML da tabela
  let html = `
    <div class="table-responsive">
      <table id="importacoes-xml-table" class="table table-striped table-hover">
        <thead>
          <tr>
            <th>ID</th>
            <th>Arquivo</th>
            <th>Nota Fiscal</th>
            <th>Data Emissão</th>
            <th>Data Importação</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Adicionar linhas da tabela
  importacoes.forEach((importacao) => {
    const dataEmissao = importacao.data_emissao
      ? new Date(importacao.data_emissao).toLocaleDateString('pt-BR')
      : '-';
    const dataImportacao = importacao.data_importacao
      ? new Date(importacao.data_importacao).toLocaleDateString('pt-BR')
      : '-';

    const statusClass =
      importacao.status === 'concluido' ? 'text-success' : 'text-danger';
    const statusIcon =
      importacao.status === 'concluido'
        ? '<i class="fas fa-check-circle"></i>'
        : '<i class="fas fa-exclamation-circle"></i>';

    html += `
      <tr>
        <td>${importacao.id}</td>
        <td>${importacao.arquivo_nome}</td>
        <td>${importacao.numero_nf || '-'}</td>
        <td>${dataEmissao}</td>
        <td>${dataImportacao}</td>
        <td class="${statusClass}">${statusIcon} ${
      importacao.status === 'concluido' ? 'Concluído' : 'Erro'
    }</td>
      </tr>
    `;
  });

  html += `
        </tbody>
      </table>
    </div>
  `;

  // Atualizar o conteúdo
  container.innerHTML = html;

  // Inicializar DataTable
  try {
    new DataTable('#importacoes-xml-table', {
      language: {
        url: '/fiscal/static/js/vendor/datatables/pt-BR.json',
      },
      responsive: true,
      order: [[0, 'desc']], // Ordenar por ID (mais recentes primeiro)
    });
  } catch (error) {
    console.error('Erro ao inicializar DataTable XML:', error);
  }
}

/**
 * Renderiza a tabela de importações SPED
 */
function renderImportacoesSpedTable(container, importacoes) {
  // Criar HTML da tabela
  let html = `
    <div class="table-responsive">
      <table id="importacoes-sped-table" class="table table-striped table-hover">
        <thead>
          <tr>
            <th>ID</th>
            <th>Arquivo</th>
            <th>Empresa</th>
            <th>Período</th>
            <th>Notas</th>
            <th>Itens</th>
            <th>Fornecedores</th>
            <th>Produtos</th>
            <th>Data Importação</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Adicionar linhas da tabela
  importacoes.forEach((importacao) => {
    const dataImportacao = importacao.data_importacao
      ? new Date(importacao.data_importacao).toLocaleDateString('pt-BR')
      : '-';

    const dataInicio = importacao.data_inicio
      ? new Date(importacao.data_inicio).toLocaleDateString('pt-BR')
      : '';
    const dataFim = importacao.data_fim
      ? new Date(importacao.data_fim).toLocaleDateString('pt-BR')
      : '';
    const periodo = dataInicio && dataFim ? `${dataInicio} - ${dataFim}` : '-';

    const statusClass =
      importacao.status === 'concluido' ? 'text-success' : 'text-danger';
    const statusIcon =
      importacao.status === 'concluido'
        ? '<i class="fas fa-check-circle"></i>'
        : '<i class="fas fa-exclamation-circle"></i>';

    html += `
      <tr>
        <td>${importacao.id}</td>
        <td>${importacao.arquivo_nome}</td>
        <td>${importacao.razao_social_empresa || '-'}</td>
        <td>${periodo}</td>
        <td>${importacao.total_notas || 0}</td>
        <td>${importacao.total_itens || 0}</td>
        <td>${importacao.total_clientes || 0}</td>
        <td>${importacao.total_produtos || 0}</td>
        <td>${dataImportacao}</td>
        <td class="${statusClass}">${statusIcon} ${
      importacao.status === 'concluido' ? 'Concluído' : 'Erro'
    }</td>
      </tr>
    `;
  });

  html += `
        </tbody>
      </table>
    </div>
  `;

  // Atualizar o conteúdo
  container.innerHTML = html;

  // Inicializar DataTable
  try {
    new DataTable('#importacoes-sped-table', {
      language: {
        url: '/fiscal/static/js/vendor/datatables/pt-BR.json',
      },
      responsive: true,
      order: [[0, 'desc']], // Ordenar por ID (mais recentes primeiro)
    });
  } catch (error) {
    console.error('Erro ao inicializar DataTable SPED:', error);
  }
}
