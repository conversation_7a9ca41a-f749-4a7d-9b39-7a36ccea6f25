#!/usr/bin/env python3
"""
Script de teste para verificar a funcionalidade de edição SPED
"""

import requests
import json

# Configurações
BASE_URL = "http://localhost:5000"
TOKEN = "seu_token_aqui"  # Substitua pelo token real

def test_sped_edit():
    """Testa a edição de dados SPED"""
    
    # Dados de teste
    auditoria_id = 1  # Substitua por um ID real
    dados_teste = {
        "empresa_id": 1,  # Substitua por um ID real
        "cfop": "1102",
        "ncm": "12345678",
        "origem_icms": "0",
        "tipo_item": "01",
        "icms": {
            "cst": "000",
            "bc": 100.00,
            "aliquota": 18.00,
            "valor": 18.00,
            "reducao": 0.00
        },
        "motivo_alteracao": "Teste de edição SPED"
    }
    
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    print(f"Testando edição SPED para auditoria ID: {auditoria_id}")
    print(f"Dados enviados: {json.dumps(dados_teste, indent=2)}")
    
    try:
        response = requests.put(
            f"{BASE_URL}/api/auditoria-comparativa/editar-sped/{auditoria_id}",
            headers=headers,
            json=dados_teste
        )
        
        print(f"\nStatus Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Teste passou! Dados SPED atualizados com sucesso.")
            else:
                print(f"❌ Teste falhou: {result.get('message')}")
        else:
            print(f"❌ Erro HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erro na requisição: {str(e)}")

def test_database_connection():
    """Testa conexão com banco de dados"""
    try:
        from models import db
        from models.auditoria_comparativa_impostos import AuditoriaComparativaImpostos
        
        # Buscar primeiro registro
        auditoria = db.session.query(AuditoriaComparativaImpostos).first()
        
        if auditoria:
            print(f"✅ Conexão com banco OK. Primeiro registro ID: {auditoria.id}")
            print(f"   CFOP atual: {auditoria.sped_cfop}")
            print(f"   NCM atual: {auditoria.sped_ncm}")
            return auditoria.id
        else:
            print("❌ Nenhum registro encontrado na tabela auditoria_comparativa_impostos")
            return None
            
    except Exception as e:
        print(f"❌ Erro na conexão com banco: {str(e)}")
        return None

if __name__ == "__main__":
    print("=== TESTE DE EDIÇÃO SPED ===\n")
    
    # Primeiro, testar conexão com banco
    print("1. Testando conexão com banco de dados...")
    auditoria_id = test_database_connection()
    
    if auditoria_id:
        print(f"\n2. Testando edição SPED...")
        # Usar o ID real encontrado
        test_sped_edit()
    else:
        print("\n❌ Não foi possível testar sem dados no banco.")
    
    print("\n=== FIM DO TESTE ===")
