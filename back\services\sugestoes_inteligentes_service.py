"""
Serviço de Sugestões Inteligentes para Auditoria Comparativa
Utiliza histórico de aprendizado para sugerir correções e auto-aprovações
"""

from typing import Dict, List, Optional, Tuple, Any
from sqlalchemy import and_, or_, desc
from models import db
from models.auditoria_comparativa_impostos import AuditoriaComparativaImpostos, HistoricoMatchingAprendizado
from models.nota_fiscal_item import NotaFiscalItem
from models.item_nota_entrada import ItemNotaEntrada
from models.produto import Produto
from models.produto_entrada import ProdutoEntrada
from models.cliente import Cliente


class SugestoesInteligentesService:
    def __init__(self, empresa_id: int, escritorio_id: int):
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id

    def obter_sugestoes_para_item(self, auditoria_id: int, tributo: str) -> Dict[str, Any]:
        """
        Obtém sugestões inteligentes para um item específico baseado no histórico
        
        Args:
            auditoria_id: ID do registro de auditoria
            tributo: Tipo de tributo (icms, icms_st, ipi, pis, cofins)
            
        Returns:
            Dict com sugestões e dados de comparação
        """
        try:
            # Buscar registro de auditoria
            auditoria = db.session.get(AuditoriaComparativaImpostos, auditoria_id)
            if not auditoria:
                return {'success': False, 'message': 'Auditoria não encontrada'}

            # Buscar dados dos itens XML e SPED
            xml_item = db.session.get(NotaFiscalItem, auditoria.xml_item_id) if auditoria.xml_item_id else None
            sped_item = db.session.get(ItemNotaEntrada, auditoria.sped_item_id) if auditoria.sped_item_id else None

            if not xml_item or not sped_item:
                return {'success': False, 'message': 'Itens XML ou SPED não encontrados'}

            # Obter códigos dos produtos
            xml_codigo = xml_item.codigo_produto if hasattr(xml_item, 'codigo_produto') else None
            sped_codigo = sped_item.cod_item if hasattr(sped_item, 'cod_item') else None

            # Buscar sugestões baseadas no histórico
            sugestoes_historico = self._buscar_sugestoes_historico(
                xml_codigo, sped_codigo, auditoria.cliente_id, tributo
            )

            # Verificar se pode ser auto-aprovado
            pode_auto_aprovar, motivo_auto_aprovacao = self._verificar_auto_aprovacao(
                auditoria, tributo, sugestoes_historico
            )

            # Obter sugestões de correção
            sugestoes_correcao = self._obter_sugestoes_correcao(
                auditoria, tributo, sugestoes_historico
            )

            # Calcular score de confiança das sugestões
            score_confianca = self._calcular_score_confianca(sugestoes_historico)

            return {
                'success': True,
                'auditoria_id': auditoria_id,
                'tributo': tributo,
                'pode_auto_aprovar': pode_auto_aprovar,
                'motivo_auto_aprovacao': motivo_auto_aprovacao,
                'sugestoes_correcao': sugestoes_correcao,
                'sugestoes_historico': sugestoes_historico,
                'score_confianca': score_confianca,
                'xml_codigo': xml_codigo,
                'sped_codigo': sped_codigo
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'Erro ao obter sugestões: {str(e)}'
            }

    def _buscar_sugestoes_historico(self, xml_codigo: str, sped_codigo: str, 
                                   cliente_id: int, tributo: str) -> List[Dict]:
        """
        Busca sugestões baseadas no histórico de aprendizado
        """
        sugestoes = []

        if not xml_codigo or not sped_codigo:
            return sugestoes

        # Buscar matches exatos aprovados
        matches_exatos = db.session.query(HistoricoMatchingAprendizado).filter(
            HistoricoMatchingAprendizado.empresa_id == self.empresa_id,
            HistoricoMatchingAprendizado.xml_codigo_produto == xml_codigo,
            HistoricoMatchingAprendizado.sped_codigo_produto == sped_codigo,
            HistoricoMatchingAprendizado.acao_usuario == 'aprovado'
        ).order_by(desc(HistoricoMatchingAprendizado.data_acao)).limit(5).all()

        for match in matches_exatos:
            sugestoes.append({
                'tipo': 'match_exato',
                'confianca': 1.0,
                'historico': match.to_dict(),
                'dados_aprovados': match.caracteristicas_match or {}
            })

        # Buscar matches similares por cliente
        if cliente_id:
            matches_cliente = db.session.query(HistoricoMatchingAprendizado).filter(
                HistoricoMatchingAprendizado.empresa_id == self.empresa_id,
                HistoricoMatchingAprendizado.cliente_id == cliente_id,
                HistoricoMatchingAprendizado.acao_usuario == 'aprovado'
            ).order_by(desc(HistoricoMatchingAprendizado.data_acao)).limit(10).all()

            for match in matches_cliente:
                if match.xml_codigo_produto != xml_codigo or match.sped_codigo_produto != sped_codigo:
                    similaridade = self._calcular_similaridade_codigo(
                        xml_codigo, match.xml_codigo_produto
                    )
                    if similaridade > 0.7:
                        sugestoes.append({
                            'tipo': 'similar_cliente',
                            'confianca': similaridade * 0.8,
                            'historico': match.to_dict(),
                            'dados_aprovados': match.caracteristicas_match or {}
                        })

        # Buscar matches similares por código
        matches_similares = db.session.query(HistoricoMatchingAprendizado).filter(
            HistoricoMatchingAprendizado.empresa_id == self.empresa_id,
            HistoricoMatchingAprendizado.acao_usuario == 'aprovado'
        ).order_by(desc(HistoricoMatchingAprendizado.data_acao)).limit(20).all()

        for match in matches_similares:
            if match.xml_codigo_produto and match.xml_codigo_produto != xml_codigo:
                similaridade_xml = self._calcular_similaridade_codigo(
                    xml_codigo, match.xml_codigo_produto
                )
                similaridade_sped = self._calcular_similaridade_codigo(
                    sped_codigo, match.sped_codigo_produto
                ) if match.sped_codigo_produto else 0

                similaridade_media = (similaridade_xml + similaridade_sped) / 2
                
                if similaridade_media > 0.6:
                    sugestoes.append({
                        'tipo': 'similar_codigo',
                        'confianca': similaridade_media * 0.6,
                        'historico': match.to_dict(),
                        'dados_aprovados': match.caracteristicas_match or {}
                    })

        # Ordenar por confiança
        sugestoes.sort(key=lambda x: x['confianca'], reverse=True)
        
        return sugestoes[:10]  # Limitar a 10 sugestões

    def _verificar_auto_aprovacao(self, auditoria: AuditoriaComparativaImpostos, 
                                 tributo: str, sugestoes: List[Dict]) -> Tuple[bool, str]:
        """
        Verifica se o item pode ser auto-aprovado baseado no histórico
        """
        # Verificar se há matches exatos com alta confiança
        matches_exatos = [s for s in sugestoes if s['tipo'] == 'match_exato' and s['confianca'] >= 0.95]
        
        if not matches_exatos:
            return False, "Nenhum match exato encontrado no histórico"

        # Verificar se os dados tributários são compatíveis
        dados_atuais = self._extrair_dados_tributarios(auditoria, tributo)
        
        for match in matches_exatos:
            dados_historicos = match.get('dados_aprovados', {})
            
            if self._comparar_dados_tributarios(dados_atuais, dados_historicos, tributo):
                return True, f"Match exato encontrado com {len(matches_exatos)} ocorrência(s) no histórico"

        return False, "Dados tributários não são compatíveis com o histórico"

    def _obter_sugestoes_correcao(self, auditoria: AuditoriaComparativaImpostos, 
                                 tributo: str, sugestoes: List[Dict]) -> Dict[str, Any]:
        """
        Obtém sugestões de correção baseadas no histórico
        """
        correcoes = {}

        if not sugestoes:
            return correcoes

        # Analisar sugestões de alta confiança
        sugestoes_alta_confianca = [s for s in sugestoes if s['confianca'] >= 0.7]

        for sugestao in sugestoes_alta_confianca:
            dados_aprovados = sugestao.get('dados_aprovados', {})
            
            if tributo in dados_aprovados:
                dados_tributo = dados_aprovados[tributo]
                
                for campo, valor in dados_tributo.items():
                    if campo not in correcoes:
                        correcoes[campo] = {
                            'valor_sugerido': valor,
                            'confianca': sugestao['confianca'],
                            'fonte': sugestao['tipo'],
                            'historico_id': sugestao['historico']['id']
                        }
                    elif sugestao['confianca'] > correcoes[campo]['confianca']:
                        correcoes[campo] = {
                            'valor_sugerido': valor,
                            'confianca': sugestao['confianca'],
                            'fonte': sugestao['tipo'],
                            'historico_id': sugestao['historico']['id']
                        }

        return correcoes

    def _calcular_score_confianca(self, sugestoes: List[Dict]) -> float:
        """
        Calcula score geral de confiança das sugestões
        """
        if not sugestoes:
            return 0.0

        # Peso maior para matches exatos
        peso_total = 0
        score_ponderado = 0

        for sugestao in sugestoes:
            if sugestao['tipo'] == 'match_exato':
                peso = 1.0
            elif sugestao['tipo'] == 'similar_cliente':
                peso = 0.7
            else:
                peso = 0.5

            score_ponderado += sugestao['confianca'] * peso
            peso_total += peso

        return score_ponderado / peso_total if peso_total > 0 else 0.0

    def _calcular_similaridade_codigo(self, codigo1: str, codigo2: str) -> float:
        """
        Calcula similaridade entre dois códigos de produto
        """
        if not codigo1 or not codigo2:
            return 0.0

        if codigo1 == codigo2:
            return 1.0

        # Similaridade baseada em caracteres comuns no início
        min_len = min(len(codigo1), len(codigo2))
        chars_comuns = 0

        for i in range(min_len):
            if codigo1[i] == codigo2[i]:
                chars_comuns += 1
            else:
                break

        return chars_comuns / max(len(codigo1), len(codigo2))

    def _extrair_dados_tributarios(self, auditoria: AuditoriaComparativaImpostos, 
                                  tributo: str) -> Dict[str, Any]:
        """
        Extrai dados tributários atuais do registro de auditoria
        """
        dados = {}

        if tributo == 'icms':
            dados = {
                'cst': auditoria.sped_cst_icms,
                'bc': float(auditoria.sped_icms_bc) if auditoria.sped_icms_bc else None,
                'aliquota': float(auditoria.sped_icms_aliquota) if auditoria.sped_icms_aliquota else None,
                'valor': float(auditoria.sped_icms_valor) if auditoria.sped_icms_valor else None,
                'reducao': float(auditoria.sped_icms_reducao) if auditoria.sped_icms_reducao else None
            }
        elif tributo == 'icms_st':
            dados = {
                'bc': float(auditoria.sped_icms_st_bc) if auditoria.sped_icms_st_bc else None,
                'aliquota': float(auditoria.sped_icms_st_aliquota) if auditoria.sped_icms_st_aliquota else None,
                'valor': float(auditoria.sped_icms_st_valor) if auditoria.sped_icms_st_valor else None,
                'mva': float(auditoria.sped_icms_st_mva) if auditoria.sped_icms_st_mva else None,
                'reducao': float(auditoria.sped_icms_st_reducao) if auditoria.sped_icms_st_reducao else None
            }
        # Adicionar outros tributos conforme necessário

        return dados

    def _comparar_dados_tributarios(self, dados_atuais: Dict, dados_historicos: Dict, 
                                   tributo: str, tolerancia: float = 0.02) -> bool:
        """
        Compara dados tributários atuais com históricos
        """
        if tributo not in dados_historicos:
            return False

        dados_hist_tributo = dados_historicos[tributo]

        # Campos obrigatórios para comparação
        campos_obrigatorios = ['cst', 'aliquota'] if tributo == 'icms' else ['aliquota']

        for campo in campos_obrigatorios:
            valor_atual = dados_atuais.get(campo)
            valor_historico = dados_hist_tributo.get(campo)

            if valor_atual is None or valor_historico is None:
                continue

            # Para valores numéricos, usar tolerância
            if isinstance(valor_atual, (int, float)) and isinstance(valor_historico, (int, float)):
                if abs(valor_atual - valor_historico) / max(abs(valor_atual), abs(valor_historico), 1) > tolerancia:
                    return False
            else:
                if str(valor_atual) != str(valor_historico):
                    return False

        return True

    def processar_auto_aprovacao(self, auditoria_id: int, tributo: str, usuario_id: int) -> Dict[str, Any]:
        """
        Processa auto-aprovação de um item baseado nas sugestões
        """
        try:
            sugestoes = self.obter_sugestoes_para_item(auditoria_id, tributo)
            
            if not sugestoes['success']:
                return sugestoes

            if not sugestoes['pode_auto_aprovar']:
                return {
                    'success': False,
                    'message': 'Item não pode ser auto-aprovado',
                    'motivo': sugestoes['motivo_auto_aprovacao']
                }

            # Buscar registro de auditoria
            auditoria = db.session.get(AuditoriaComparativaImpostos, auditoria_id)
            if not auditoria:
                return {'success': False, 'message': 'Auditoria não encontrada'}

            # Aprovar automaticamente
            auditoria.set_status_tributo(tributo, 'aprovado', usuario_id, 
                                       f'Auto-aprovado: {sugestoes["motivo_auto_aprovacao"]}')

            # Registrar no histórico de aprendizado
            self._registrar_auto_aprovacao(auditoria, tributo, usuario_id, sugestoes)

            db.session.commit()

            return {
                'success': True,
                'message': 'Item auto-aprovado com sucesso',
                'auditoria': auditoria.to_dict()
            }

        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'message': f'Erro na auto-aprovação: {str(e)}'
            }

    def _registrar_auto_aprovacao(self, auditoria: AuditoriaComparativaImpostos, 
                                 tributo: str, usuario_id: int, sugestoes: Dict):
        """
        Registra auto-aprovação no histórico de aprendizado
        """
        if not auditoria.xml_item_id or not auditoria.sped_item_id:
            return

        # Buscar códigos dos produtos
        xml_item = db.session.get(NotaFiscalItem, auditoria.xml_item_id)
        sped_item = db.session.get(ItemNotaEntrada, auditoria.sped_item_id)

        if not xml_item or not sped_item:
            return

        xml_codigo = xml_item.codigo_produto if hasattr(xml_item, 'codigo_produto') else None
        sped_codigo = sped_item.cod_item if hasattr(sped_item, 'cod_item') else None

        # Criar registro de histórico
        historico = HistoricoMatchingAprendizado(
            empresa_id=self.empresa_id,
            escritorio_id=self.escritorio_id,
            xml_item_id=auditoria.xml_item_id,
            sped_item_id=auditoria.sped_item_id,
            cliente_id=auditoria.cliente_id,
            xml_codigo_produto=xml_codigo,
            sped_codigo_produto=sped_codigo,
            match_type_original=auditoria.match_type,
            confidence_score_original=auditoria.confidence_score,
            acao_usuario='aprovado',
            usuario_id=usuario_id,
            caracteristicas_match={
                tributo: self._extrair_dados_tributarios(auditoria, tributo),
                'auto_aprovado': True,
                'score_confianca_sugestoes': sugestoes['score_confianca']
            },
            feedback_usuario=f'Auto-aprovado baseado em {len(sugestoes["sugestoes_historico"])} sugestão(ões) do histórico'
        )

        db.session.add(historico)
