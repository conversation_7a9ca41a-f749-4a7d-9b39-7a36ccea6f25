# Import models to make them available when importing from models package
from .escritorio import Escritorio, db
from .empresa import Empresa
from .usuario import Usuario
from .cliente import <PERSON>liente
from .produto import Produto
from .tributo import Tributo
from .importacao_xml import ImportacaoXML
from .tributo_historico import TributoHistorico
from .cenario import CenarioICMS, CenarioICMSST, CenarioIPI, CenarioPIS, CenarioCOFINS, CenarioDIFAL
from .nota_fiscal_item import NotaFiscalItem
from .auditoria_resultado import AuditoriaResultado
from .auditoria_sumario import AuditoriaSumario
from .chatbot_conversas import ChatbotConversas
from .chatbot_templates import ChatbotTemplates
# Modelos SPED
from .produto_entrada import ProdutoEntrada
from .nota_entrada import NotaEntrada
from .item_nota_entrada import ItemNotaEntrada
from .importacao_sped import ImportacaoSped
# Modelos Auditoria de Entrada
from .auditoria_entrada import AuditoriaEntrada
from .notas_faltantes import NotasFaltantes, HistoricoAlteracaoXML, HistoricoAuditoriaEntrada
from .importacao_async import ImportacaoAsync
# Modelos Auditoria Comparativa de Impostos
from .auditoria_comparativa_impostos import AuditoriaComparativaImpostos, HistoricoMatchingAprendizado
from .matching_manual_temporario import MatchingManualTemporario
