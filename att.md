Preciso da sua ajuda na parte de Auditoria Comparativa do Sistema, ela está show de bola e estamos conseguindo assimilar muito bem um produto do SPED com o mesmo produto do XML, isso está ótimo!.
Porém, vários dados do SPED vem incorretos, como o CST, CFOP, NCM, Base de Cálculo, Alíquota, Valor Tributo, Redução, Crédito de ICMS, etc (erro humano na hora da emissão). E o analista fiscal poderá atualizar, editar esses campos do SPED do produto dentro do nosso sistema, pois posteriormente, teremos que exportar esse arquivo SPED, dessa empresa e desse período, atualizado para o analista.
Para fazermos isso, temos que pensar no que realmente utilizaremos, para não faltar nada. Ao editar os dados do SPED, o analista vai aprovar o match, e aprovando esse match, nós temos que salvar o histórico de matching com os dados atualizados do produto do SPED, certo? Para que em futuras auditorias, nós consigamos aprovar automaticamente o match de produto, caso os dados dele batam 100% com os dados que temos salvo no sistema.
Ou seja, o sistema encontrou o produto X no SPED e seu respectivo produto no XML, porém o CFOP do produto no SPED está incorreto, o analista fiscal irá corrigir esse erro, e ao aprovar o match, nós temos que salvar o histórico de matching com os dados atualizados do produto do SPED, para que em uma futura importação, o sistema já consiga mostrar, se o CFOP do produto no SPED está correto ou não e se estiver correto (tudo, não só CFOP), o sistema já consiga mostrar que o produto está correto. Evitando trabalho manual.
Além disso, haverá a parte de exportação do SPED.

1. Trabalhar na edição dos dados do SPED
1.1 Atualizar os dados na tabela auditoria_comparativa_impostos (tudo), item_nota_entrada (CST, CFOP, codigo natureza, Base de Calculo, aliquota, valor tributo, redução), produto_entrada(NCM)
1.2 Dados que podem ser alterados, são:
    - CFOP
    - CST
    - NCM
    - Origem
    - Alíquota
    - Tipo Item
    - CSOSN
    - Base de Cálculo
    - Valor tributo
    - Redução
    - Crédito de ICMS
1.3 Temos que atualizar todas as tabelas que salvam esses dados, não? Pois posteriormente teremos que atualizar os dados do SPED e exportar o mesmo atualizado
1.4 Ao editar o SPED e fazer a aprovação do MATCH, temos que salvar o histórico de matching com os dados novos de SPED
2. Criar um outro modal de edição, onde poderemos editar os dados do SPED em massa

Consegue verificar tudo que precisamos e fazer as mudanças, atualizaçõe necessárias?
Com base nessas informações, você deve analisar profundamente o nosso sistema, ver como esta sendo feita a parte de escrituração, como estamos assimilando as notas, como estamos registrando e como estamos pegando os dados, pois nas auditorias por tributos, o usuário deve conseguir editar as informações do SPED, como CFOP, CST, Redução %, Base R$, Alíquota %, Valor R$, MVA, etc (depende do tributo), para que posteriormente, consigamos exportar o SPED atualizado para o usuário, então devemos sempre trabalhar pensando nisso para fazermos da melhor maneira possível.
Analise com atenção todos os aspectos envolvidos: funcionalidades, dados trafegando pela API, rotas utilizadas, consultas ao banco de dados, e integrações entre frontend, backend e banco de dados. Todas as alterações devem ser feitas de forma coordenada entre essas camadas.
Após entender a lógica e estrutura dessa parte do sistema, elabore um plano de execução dividido por etapas. Só então inicie as mudanças.
Se for necessários mudanças ou atualizações no banco, crie um código SQL, que posteriormente eu executarei manualmente no PgAdmin.


## Na página de Auditoria Comparativa
Temos que, acima da tabela, listar 3 pequenos cards, que mostrarão inconsistencia de alguns dados, um de CFOP, um de CFOP com CST e um de CFOP, CST e ALIQUOTA ou Valor do Imposto
Ao clicar no card, devemos ou mostrar todas os dados inconsistentes em um modal, ou na tabela, listar apenas os registros que tem esses erros.
Também temos que ter um botão de 'Correção', onde automaticamente o código irá sugerir as alterações, e se o usuário aprovar, aplicaremos essas atualizações nos registros.

Temos uma coluna de 'UF' a mais na tabela, no fim dela, temos que manter apenas a primeira, que está depois da coluna 'NOME/RAZÃO SOCIAL'.
