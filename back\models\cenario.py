from .escritorio import db
from sqlalchemy.sql import func
from sqlalchemy.ext.declarative import declared_attr

class CenarioBase(db.Model):
    """Classe base abstrata para todos os modelos de cenário"""
    __abstract__ = True

    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.In<PERSON>ger, db.<PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON>ey('escritorio.id'), nullable=False)
    cliente_id = db.Column(db.<PERSON>ger, db.<PERSON>ey('cliente.id'), nullable=False)
    produto_id = db.Column(db.Integer, db.Foreign<PERSON>ey('produto.id'), nullable=False)
    direcao = db.Column(db.String(10))  # 'entrada' ou 'saida'
    tipo_operacao = db.Column(db.String(1))  # '0' = entrada, '1' = saída (valor da tag tpNF do XML)
    cfop = db.Column(db.String(10))  # Código Fiscal de Operações e Prestações
    ncm = db.Column(db.String(20))  # Nomenclatura Comum do Mercosul

    status = db.Column(db.String(20), default='novo')  # 'novo', 'producao', 'inconsistente'
    data_inicio_vigencia = db.Column(db.Date)
    data_fim_vigencia = db.Column(db.Date)
    ativo = db.Column(db.Boolean, default=False)
    data_criacao = db.Column(db.DateTime, server_default=func.now())
    data_atualizacao = db.Column(db.DateTime, server_default=func.now(), onupdate=func.now())

    # Relacionamentos como @declared_attr
    @declared_attr
    def empresa(cls):
        return db.relationship('Empresa', backref=f'{cls.__name__.lower()}s')

    @declared_attr
    def escritorio(cls):
        return db.relationship('Escritorio', backref=f'{cls.__name__.lower()}s')

    @declared_attr
    def cliente(cls):
        return db.relationship('Cliente', backref=f'{cls.__name__.lower()}s')

    @declared_attr
    def produto(cls):
        return db.relationship('Produto', backref=f'{cls.__name__.lower()}s')

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'cliente_id': self.cliente_id,
            'produto_id': self.produto_id,
            'direcao': self.direcao,
            'tipo_operacao': self.tipo_operacao,
            'cfop': self.cfop,
            'ncm': self.ncm,
            'status': self.status,
            'data_inicio_vigencia': self.data_inicio_vigencia.isoformat() if self.data_inicio_vigencia else None,
            'data_fim_vigencia': self.data_fim_vigencia.isoformat() if self.data_fim_vigencia else None,
            'ativo': self.ativo,
            'data_criacao': self.data_criacao.isoformat() if self.data_criacao else None,
            'data_atualizacao': self.data_atualizacao.isoformat() if self.data_atualizacao else None
        }


class CenarioICMS(CenarioBase):
    __tablename__ = 'cenario_icms'

    # Campos específicos do ICMS
    origem = db.Column(db.String(2))
    cst = db.Column(db.String(3))
    mod_bc = db.Column(db.String(2))
    p_red_bc = db.Column(db.Numeric(10, 4))
    aliquota = db.Column(db.Numeric(10, 4))
    p_dif = db.Column(db.Numeric(10, 4))
    incluir_frete = db.Column(db.Boolean, default=True)  # Se deve incluir frete na base de cálculo
    incluir_desconto = db.Column(db.Boolean, default=True)  # Se deve subtrair desconto da base de cálculo

    def __repr__(self):
        return f"<CenarioICMS {self.id} - Produto {self.produto_id} - Cliente {self.cliente_id}>"

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        base_dict = super().to_dict()
        icms_dict = {
            'origem': self.origem,
            'cst': self.cst,
            'mod_bc': self.mod_bc,
            'p_red_bc': float(self.p_red_bc) if self.p_red_bc else None,
            'aliquota': float(self.aliquota) if self.aliquota else None,
            'p_dif': float(self.p_dif) if self.p_dif else None,
            'incluir_frete': self.incluir_frete
        }
        return {**base_dict, **icms_dict}


class CenarioICMSST(CenarioBase):
    __tablename__ = 'cenario_icms_st'

    # Campos de ICMS que também são usados no ICMS-ST
    origem = db.Column(db.String(2))
    cst = db.Column(db.String(3))
    mod_bc = db.Column(db.String(2))
    p_red_bc = db.Column(db.Numeric(10, 4))
    aliquota = db.Column(db.Numeric(10, 4))

    # Campos específicos do ICMS-ST
    icms_st_mod_bc = db.Column(db.String(2))
    icms_st_aliquota = db.Column(db.Numeric(10, 4))
    icms_st_p_mva = db.Column(db.Numeric(10, 4))
    incluir_frete = db.Column(db.Boolean, default=True)  # Se deve incluir frete na base de cálculo
    incluir_desconto = db.Column(db.Boolean, default=True)  # Se deve subtrair desconto da base de cálculo

    def __repr__(self):
        return f"<CenarioICMSST {self.id} - Produto {self.produto_id} - Cliente {self.cliente_id}>"

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        base_dict = super().to_dict()
        icms_st_dict = {
            'origem': self.origem,
            'cst': self.cst,
            'mod_bc': self.mod_bc,
            'p_red_bc': float(self.p_red_bc) if self.p_red_bc else None,
            'aliquota': float(self.aliquota) if self.aliquota else None,
            'icms_st_mod_bc': self.icms_st_mod_bc,
            'icms_st_aliquota': float(self.icms_st_aliquota) if self.icms_st_aliquota else None,
            'icms_st_p_mva': float(self.icms_st_p_mva) if self.icms_st_p_mva else None,
            'incluir_frete': self.incluir_frete
        }
        return {**base_dict, **icms_st_dict}


class CenarioIPI(CenarioBase):
    __tablename__ = 'cenario_ipi'

    # Campos específicos do IPI
    cst = db.Column(db.String(3))
    aliquota = db.Column(db.Numeric(10, 4))
    ex = db.Column(db.String(3))  # Campo para EXTIPI
    incluir_frete = db.Column(db.Boolean, default=True)  # Se deve incluir frete na base de cálculo
    incluir_desconto = db.Column(db.Boolean, default=True)  # Se deve subtrair desconto da base de cálculo

    def __repr__(self):
        return f"<CenarioIPI {self.id} - Produto {self.produto_id} - Cliente {self.cliente_id}>"

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        base_dict = super().to_dict()
        ipi_dict = {
            'cst': self.cst,
            'aliquota': float(self.aliquota) if self.aliquota else None,
            'ex': self.ex,
            'incluir_frete': self.incluir_frete
        }
        return {**base_dict, **ipi_dict}


class CenarioPIS(CenarioBase):
    __tablename__ = 'cenario_pis'

    # Campos específicos do PIS
    cst = db.Column(db.String(3))
    aliquota = db.Column(db.Numeric(10, 4))
    p_red_bc = db.Column(db.Numeric(10, 4))  # Percentual de redução da base de cálculo
    incluir_frete = db.Column(db.Boolean, default=True)  # Se deve incluir frete na base de cálculo
    incluir_desconto = db.Column(db.Boolean, default=True)  # Se deve subtrair desconto da base de cálculo

    def __repr__(self):
        return f"<CenarioPIS {self.id} - Produto {self.produto_id} - Cliente {self.cliente_id}>"

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        base_dict = super().to_dict()
        pis_dict = {
            'cst': self.cst,
            'aliquota': float(self.aliquota) if self.aliquota else None,
            'p_red_bc': float(self.p_red_bc) if self.p_red_bc else None,
            'incluir_frete': self.incluir_frete
        }
        return {**base_dict, **pis_dict}


class CenarioCOFINS(CenarioBase):
    __tablename__ = 'cenario_cofins'

    # Campos específicos do COFINS
    cst = db.Column(db.String(3))
    aliquota = db.Column(db.Numeric(10, 4))
    p_red_bc = db.Column(db.Numeric(10, 4))  # Percentual de redução da base de cálculo
    incluir_frete = db.Column(db.Boolean, default=True)  # Se deve incluir frete na base de cálculo
    incluir_desconto = db.Column(db.Boolean, default=True)  # Se deve subtrair desconto da base de cálculo

    def __repr__(self):
        return f"<CenarioCOFINS {self.id} - Produto {self.produto_id} - Cliente {self.cliente_id}>"

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        base_dict = super().to_dict()
        cofins_dict = {
            'cst': self.cst,
            'aliquota': float(self.aliquota) if self.aliquota else None,
            'p_red_bc': float(self.p_red_bc) if self.p_red_bc else None,
            'incluir_frete': self.incluir_frete
        }
        return {**base_dict, **cofins_dict}


class CenarioDIFAL(CenarioBase):
    __tablename__ = 'cenario_difal'

    # Campos de ICMS que também são usados no DIFAL
    origem = db.Column(db.String(2))
    cst = db.Column(db.String(3))
    mod_bc = db.Column(db.String(2))
    p_red_bc = db.Column(db.Numeric(10, 4))
    aliquota = db.Column(db.Numeric(10, 4))

    # Campos específicos do DIFAL
    p_fcp_uf_dest = db.Column(db.Numeric(10, 4))
    p_icms_uf_dest = db.Column(db.Numeric(10, 4))
    p_icms_inter = db.Column(db.Numeric(10, 4))
    p_icms_inter_part = db.Column(db.Numeric(10, 4))

    def __repr__(self):
        return f"<CenarioDIFAL {self.id} - Produto {self.produto_id} - Cliente {self.cliente_id}>"

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        base_dict = super().to_dict()
        difal_dict = {
            'origem': self.origem,
            'cst': self.cst,
            'mod_bc': self.mod_bc,
            'p_red_bc': float(self.p_red_bc) if self.p_red_bc else None,
            'aliquota': float(self.aliquota) if self.aliquota else None,
            'p_fcp_uf_dest': float(self.p_fcp_uf_dest) if self.p_fcp_uf_dest else None,
            'p_icms_uf_dest': float(self.p_icms_uf_dest) if self.p_icms_uf_dest else None,
            'p_icms_inter': float(self.p_icms_inter) if self.p_icms_inter else None,
            'p_icms_inter_part': float(self.p_icms_inter_part) if self.p_icms_inter_part else None
        }
        return {**base_dict, **difal_dict}
