/**
 * Dashboard Empresa Gráficos - Auditoria Fiscal
 * Funções específicas para renderização de gráficos
 */

/**
 * Renderiza todos os gráficos
 */
function renderizarGraficos() {
  const container = document.getElementById('dashboard-empresa-graficos');
  if (!container || !graficosData) {
    return;
  }

  // Limpar container
  container.innerHTML = '';

  // Criar estrutura dos gráficos
  const graficosHTML = `
      <div class="row mb-4">
        <div class="col-12">
          <h4><i class="fas fa-chart-line"></i> Análise Temporal de Inconsistências</h4>
          <p class="tributos-help-text">Evolução dos valores inconsistentes nos últimos 12 meses</p>
        </div>
      </div>
  
      <!-- Gráfico Geral -->
      <div class="row mb-5">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i class="fas fa-chart-area"></i> Resumo Geral - Todos os Tributos
              </h5>
            </div>
            <div class="card-body">
              <canvas id="grafico-geral" height="200"></canvas>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Gráficos por Tributo -->
      <div class="row mb-4">
        <div class="col-12">
          <h5><i class="fas fa-calculator"></i> Análise por Tributo</h5>
        </div>
      </div>
  
      <div class="row">
        <div class="col-md-6 mb-4">
          <div class="card">
            <div class="card-header">
              <h6 class="card-title mb-0">ICMS</h6>
            </div>
            <div class="card-body">
              <canvas id="grafico-icms" height="150"></canvas>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-4">
          <div class="card">
            <div class="card-header">
              <h6 class="card-title mb-0">ICMS-ST</h6>
            </div>
            <div class="card-body">
              <canvas id="grafico-icms-st" height="150"></canvas>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-4">
          <div class="card">
            <div class="card-header">
              <h6 class="card-title mb-0">IPI</h6>
            </div>
            <div class="card-body">
              <canvas id="grafico-ipi" height="150"></canvas>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-4">
          <div class="card">
            <div class="card-header">
              <h6 class="card-title mb-0">PIS</h6>
            </div>
            <div class="card-body">
              <canvas id="grafico-pis" height="150"></canvas>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-4">
          <div class="card">
            <div class="card-header">
              <h6 class="card-title mb-0">COFINS</h6>
            </div>
            <div class="card-body">
              <canvas id="grafico-cofins" height="150"></canvas>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-4">
          <div class="card">
            <div class="card-header">
              <h6 class="card-title mb-0">DIFAL</h6>
            </div>
            <div class="card-body">
              <canvas id="grafico-difal" height="150"></canvas>
            </div>
          </div>
        </div>
      </div>
    `;

  container.innerHTML = graficosHTML;

  // Aguardar um pouco para garantir que os elementos foram criados
  setTimeout(() => {
    // Renderizar gráfico geral
    renderizarGraficoGeral();

    // Renderizar gráficos por tributo
    renderizarGraficosTributos();
  }, 100);
}

/**
 * Renderiza o gráfico geral com todos os tributos
 */
function renderizarGraficoGeral() {
  const ctx = document.getElementById('grafico-geral');
  if (!ctx || !graficosData) {
    console.log('Contexto ou dados não disponíveis para gráfico geral');
    return;
  }

  // Verificar se Chart.js está disponível
  if (typeof Chart === 'undefined') {
    console.error('Chart.js não está carregado');
    setTimeout(renderizarGraficoGeral, 1000); // Tentar novamente em 1 segundo
    return;
  }

  // Destruir gráfico existente se houver
  if (chartInstances['geral']) {
    chartInstances['geral'].destroy();
  }

  const dados = graficosData.dados;

  chartInstances['geral'] = new Chart(ctx, {
    type: 'line',
    data: {
      labels: dados.labels,
      datasets: [
        {
          label: 'Valor Inconsistente (R$)',
          data: dados.geral.valores_inconsistentes,
          borderColor: getChartColor('primary'),
          backgroundColor: getChartColor('primary', 0.1),
          borderWidth: 3,
          fill: true,
          tension: 0.4,
        },
      ],
    },
    options: getChartOptions(
      'Valores Inconsistentes - Todos os Tributos',
      true,
    ),
  });
}

/**
 * Renderiza os gráficos individuais por tributo
 */
function renderizarGraficosTributos() {
  // Verificar se Chart.js está disponível
  if (typeof Chart === 'undefined') {
    console.error('Chart.js não está carregado para gráficos de tributos');
    setTimeout(renderizarGraficosTributos, 1000); // Tentar novamente em 1 segundo
    return;
  }

  const tributos = ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal'];
  const cores = [
    'primary',
    'success',
    'warning',
    'danger',
    'info',
    'secondary',
  ];

  tributos.forEach((tributo, index) => {
    const ctx = document.getElementById(`grafico-${tributo.replace('_', '-')}`);
    if (!ctx || !graficosData) {
      console.log(`Contexto não encontrado para tributo: ${tributo}`);
      return;
    }

    // Destruir gráfico existente se houver
    if (chartInstances[tributo]) {
      chartInstances[tributo].destroy();
    }

    const dados = graficosData.dados.tributos[tributo];

    chartInstances[tributo] = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: graficosData.dados.labels,
        datasets: [
          {
            label: 'Valor Inconsistente (R$)',
            data: dados.valores_inconsistentes,
            backgroundColor: getChartColor(cores[index], 0.7),
            borderColor: getChartColor(cores[index]),
            borderWidth: 1,
          },
        ],
      },
      options: getChartOptions(tributo.toUpperCase().replace('_', '-'), false),
    });
  });
}

/**
 * Obtém as cores do gráfico baseadas no tema atual
 */
function getChartColor(colorName, alpha = 1) {
  const isDarkMode =
    document.body.classList.contains('dark-theme') ||
    document.documentElement.getAttribute('data-theme') === 'dark';

  const colors = {
    primary: isDarkMode
      ? `rgba(99, 179, 237, ${alpha})`
      : `rgba(13, 110, 253, ${alpha})`,
    success: isDarkMode
      ? `rgba(32, 201, 151, ${alpha})`
      : `rgba(25, 135, 84, ${alpha})`,
    warning: isDarkMode
      ? `rgba(255, 205, 86, ${alpha})`
      : `rgba(255, 193, 7, ${alpha})`,
    danger: isDarkMode
      ? `rgba(255, 99, 132, ${alpha})`
      : `rgba(220, 53, 69, ${alpha})`,
    info: isDarkMode
      ? `rgba(54, 162, 235, ${alpha})`
      : `rgba(13, 202, 240, ${alpha})`,
    secondary: isDarkMode
      ? `rgba(173, 181, 189, ${alpha})`
      : `rgba(108, 117, 125, ${alpha})`,
  };

  return colors[colorName] || colors.primary;
}

/**
 * Obtém as opções padrão para os gráficos
 */
function getChartOptions(titulo, isMainChart = false) {
  const isDarkMode =
    document.body.classList.contains('dark-theme') ||
    document.documentElement.getAttribute('data-theme') === 'dark';

  const textColor = isDarkMode ? '#ffffff' : '#333333';
  const gridColor = isDarkMode
    ? 'rgba(255, 255, 255, 0.1)'
    : 'rgba(0, 0, 0, 0.1)';

  return {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: false,
      },
      legend: {
        display: isMainChart,
        labels: {
          color: textColor,
        },
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            return `${
              context.dataset.label
            }: R$ ${context.parsed.y.toLocaleString('pt-BR', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}`;
          },
        },
      },
    },
    scales: {
      x: {
        ticks: {
          color: textColor,
        },
        grid: {
          color: gridColor,
        },
      },
      y: {
        beginAtZero: true,
        ticks: {
          color: textColor,
          callback: function (value) {
            return (
              'R$ ' +
              value.toLocaleString('pt-BR', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0,
              })
            );
          },
        },
        grid: {
          color: gridColor,
        },
      },
    },
  };
}

/**
 * Atualiza as cores dos gráficos quando o tema muda
 */
function atualizarCoresGraficos() {
  if (typeof chartInstances === 'undefined') return;
  
  // Para cada gráfico, recria com as cores atualizadas
  Object.keys(chartInstances).forEach((key) => {
    const chart = chartInstances[key];
    if (chart) {
      // Obtém o ID do canvas
      const canvasId = chart.canvas.id;
      // Destrói o gráfico existente
      chart.destroy();
      
      // Recria o gráfico com as cores atualizadas
      if (key === 'geral') {
        renderizarGraficoGeral();
      } else {
        renderizarGraficosTributos();
      }
    }
  });
}

// Inicializar o objeto chartInstances se não existir
if (typeof chartInstances === 'undefined') {
  window.chartInstances = {};
}

// Adicionar observer para detectar mudanças no tema
const themeObserver = new MutationObserver(function(mutations) {
  mutations.forEach(function(mutation) {
    if (mutation.attributeName === 'class') {
      atualizarCoresGraficos();
    }
  });
});

// Iniciar a observação do body para mudanças de classe
themeObserver.observe(document.body, {
  attributes: true,
  attributeFilter: ['class']
});

// Exportar funções para uso global
window.renderizarGraficos = renderizarGraficos;
window.atualizarCoresGraficos = atualizarCoresGraficos;
