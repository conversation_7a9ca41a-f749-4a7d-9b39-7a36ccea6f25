/**
 * Empresas.js - Auditoria Fiscal
 * Funções para gerenciar a página de empresas
 */

// Variáveis globais
let empresasTable = null;
let currentEmpresa = null;

document.addEventListener('DOMContentLoaded', function () {
  console.log('DOM carregado - empresas.js');

  // Inicializar a página de empresas
  initEmpresasPage();
});

/**
 * Inicializa a página de empresas
 */
function initEmpresasPage() {
  console.log('Inicializando página de empresas');

  // Verificar se estamos na página de empresas
  if (window.location.pathname.includes('/empresas')) {
    // Configurar o botão de adicionar empresa
    setupAddEmpresaButton();

    // Carregar dados de empresas
    loadEmpresasData();
  }
}

/**
 * Configura o botão de adicionar empresa
 */
function setupAddEmpresaButton() {
  const addButton = document.getElementById('new-empresa-btn');
  if (!addButton) return;

  addButton.addEventListener('click', function () {
    showEmpresaModal();
  });
}

/**
 * Carrega dados de empresas
 */
function loadEmpresasData() {
  console.log('Carregando dados de empresas...');

  // Mostrar indicador de carregamento
  const empresasContent = document.getElementById('empresas-content');
  if (!empresasContent) {
    console.warn(
      'Elemento #empresas-content não encontrado. Saindo de loadEmpresasData.',
    );
    return;
  }

  empresasContent.innerHTML =
    '<div class="text-center my-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">Carregando empresas...</p></div>';

  console.log('[loadEmpresasData] Antes de chamar fetch para /fiscal/api/empresas');
  // Buscar empresas da API
  fetch('/fiscal/api/empresas', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => {
      console.log('[loadEmpresasData] Resposta recebida da API:', response);
      if (!response.ok) {
        console.error(
          '[loadEmpresasData] Resposta da API não foi OK:',
          response.status,
          response.statusText,
        );
        response
          .text()
          .then((text) =>
            console.error(
              '[loadEmpresasData] Corpo da resposta de erro:',
              text,
            ),
          );
      }
      return response.json();
    })
    .then((data) => {
      console.log('[loadEmpresasData] Dados recebidos e parseados:', data);
      renderEmpresasTable(data.empresas || []);
    })
    .catch((error) => {
      console.error('Erro ao carregar empresas:', error);
      empresasContent.innerHTML =
        '<div class="alert alert-danger">Erro ao carregar empresas. Tente novamente mais tarde.</div>';
    });
}

/**
 * Renderiza a tabela de empresas
 * @param {Array} empresas - Lista de empresas
 */
function renderEmpresasTable(empresas) {
  const empresasContent = document.getElementById('empresas-content');
  if (!empresasContent) return;

  if (empresas.length === 0) {
    empresasContent.innerHTML =
      '<div class="alert alert-info">Nenhuma empresa cadastrada.</div>';
    return;
  }

  // Criar tabela
  let html = `
    <div class="table-responsive">
      <table id="empresas-table" class="table table-striped table-hover">
        <thead>
          <tr>
            <th>Razão Social</th>
            <th>CNPJ</th>
            <th>Cidade/UF</th>
            <th>Tributação</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Adicionar linhas
  empresas.forEach((empresa) => {
    const cidadeUf =
      empresa.cidade && empresa.estado
        ? `${empresa.cidade}/${empresa.estado}`
        : '-';

    html += `
      <tr>
        <td>${empresa.razao_social || '-'}</td>
        <td>${formatCNPJ(empresa.cnpj) || '-'}</td>
        <td>${cidadeUf}</td>
        <td>${empresa.tributacao || '-'}</td>
        <td>
          <div class="btn-group">
            <button class="btn btn-sm btn-primary edit-empresa-btn" data-id="${
              empresa.id
            }">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-danger delete-empresa-btn" data-id="${
              empresa.id
            }" data-nome="${empresa.razao_social}">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </td>
      </tr>
    `;
  });

  html += `
        </tbody>
      </table>
    </div>
  `;

  // Atualizar o conteúdo
  empresasContent.innerHTML = html;

  // Inicializar DataTable
  try {
    empresasTable = new DataTable('#empresas-table', {
      language: {
        url: '/fiscal/static/js/vendor/datatables/pt-BR.json',
      },
      responsive: true,
    });
  } catch (error) {
    console.error('Erro ao inicializar DataTable:', error);
  }

  // Configurar botões de edição
  document.querySelectorAll('.edit-empresa-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const empresaId = this.getAttribute('data-id');
      editEmpresa(empresaId);
    });
  });

  // Configurar botões de exclusão
  document.querySelectorAll('.delete-empresa-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const empresaId = this.getAttribute('data-id');
      const empresaNome = this.getAttribute('data-nome');
      deleteEmpresa(empresaId, empresaNome);
    });
  });
}

/**
 * Mostra o modal para adicionar/editar empresa
 * @param {Object} empresa - Dados da empresa (opcional, para edição)
 */
function showEmpresaModal(empresa = null) {
  currentEmpresa = empresa;

  const modalTitle = document.getElementById('modalTitle');
  const modalBody = document.getElementById('modalBody');

  modalTitle.textContent = empresa ? 'Editar Empresa' : 'Nova Empresa';

  // Criar conteúdo do formulário com abas
  modalBody.innerHTML = `
    <div id="form-message"></div>

    <ul class="nav nav-tabs" id="empresaTabs" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="dados-tab" data-bs-toggle="tab" data-bs-target="#dados-content" type="button" role="tab" aria-controls="dados-content" aria-selected="true">Dados Principais</button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="endereco-tab" data-bs-toggle="tab" data-bs-target="#endereco-content" type="button" role="tab" aria-controls="endereco-content" aria-selected="false">Endereço</button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="fiscal-tab" data-bs-toggle="tab" data-bs-target="#fiscal-content" type="button" role="tab" aria-controls="fiscal-content" aria-selected="false">Dados Fiscais</button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="observacoes-tab" data-bs-toggle="tab" data-bs-target="#observacoes-content" type="button" role="tab" aria-controls="observacoes-content" aria-selected="false">Observações</button>
      </li>
    </ul>

    <form id="empresa-form" class="needs-validation" novalidate>
      <div class="tab-content mt-3" id="empresaTabsContent">
        <!-- Tab Dados Principais -->
        <div class="tab-pane fade show active" id="dados-content" role="tabpanel" aria-labelledby="dados-tab">
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="cnpj" class="form-label">CNPJ *</label>
              <input type="text" class="form-control" id="cnpj" name="cnpj" maxlength="18" required value="${
                empresa ? empresa.cnpj || '' : ''
              }">
              <div class="invalid-feedback">CNPJ é obrigatório</div>
            </div>
            <div class="col-md-6 mb-3">
              <label for="inscricao_estadual" class="form-label">Inscrição Estadual</label>
              <input type="text" class="form-control" id="inscricao_estadual" name="inscricao_estadual" value="${
                empresa ? empresa.inscricao_estadual || '' : ''
              }">
            </div>
            <div class="col-md-6 mb-3">
              <label for="razao_social" class="form-label">Razão Social *</label>
              <input type="text" class="form-control" id="razao_social" name="razao_social" required value="${
                empresa ? empresa.razao_social || '' : ''
              }">
              <div class="invalid-feedback">Razão Social é obrigatório</div>
            </div>

          <div class="col-md-6 mb-3">
              <label for="nome_fantasia" class="form-label">Nome Fantasia</label>
              <input type="text" class="form-control" id="nome_fantasia" name="nome_fantasia" value="${
                empresa ? empresa.nome_fantasia || '' : ''
              }">
          </div>
          <div class="col-md-6 mb-3">
              <label for="email" class="form-label">Email</label>
              <input type="email" class="form-control" id="email" name="email" value="${
                empresa ? empresa.email || '' : ''
              }">
          </div>
          <div class="col-md-6 mb-3">
              <label for="responsavel" class="form-label">Responsável</label>
              <input type="text" class="form-control" id="responsavel" name="responsavel" value="${
                empresa ? empresa.responsavel || '' : ''
              }">
          </div>
        </div>
        </div>

        <!-- Tab Endereço -->
        <div class="tab-pane fade" id="endereco-content" role="tabpanel" aria-labelledby="endereco-tab">
          <div class="row">
            <div class="col-md-4 mb-3">
              <label for="cep" class="form-label">CEP</label>
              <div class="input-group">
                <input type="text" class="form-control" id="cep" name="cep" value="${
                  empresa ? empresa.cep || '' : ''
                }">
                <button class="btn btn-outline-secondary" type="button" id="buscar-cep">Buscar</button>
              </div>
            </div>
            <div class="col-md-8 mb-3">
              <label for="logradouro" class="form-label">Logradouro</label>
              <input type="text" class="form-control" id="logradouro" name="logradouro" value="${
                empresa ? empresa.logradouro || '' : ''
              }">
            </div>
          </div>

          <div class="row">
            <div class="col-md-4 mb-3">
              <label for="numero" class="form-label">Número</label>
              <input type="text" class="form-control" id="numero" name="numero" value="${
                empresa ? empresa.numero || '' : ''
              }">
            </div>
            <div class="col-md-8 mb-3">
              <label for="complemento" class="form-label">Complemento</label>
              <input type="text" class="form-control" id="complemento" name="complemento" value="${
                empresa ? empresa.complemento || '' : ''
              }">
            </div>
          </div>

          <div class="row">
            <div class="col-md-4 mb-3">
              <label for="bairro" class="form-label">Bairro</label>
              <input type="text" class="form-control" id="bairro" name="bairro" value="${
                empresa ? empresa.bairro || '' : ''
              }">
            </div>
            <div class="col-md-6 mb-3">
              <label for="cidade" class="form-label">Cidade</label>
              <input type="text" class="form-control" id="cidade" name="cidade" value="${
                empresa ? empresa.cidade || '' : ''
              }">
            </div>
            <div class="col-md-2 mb-3">
              <label for="estado" class="form-label">UF</label>
              <input type="text" class="form-control" id="estado" name="estado" maxlength="2" value="${
                empresa ? empresa.estado || '' : ''
              }">
            </div>
          </div>
        </div>

        <!-- Tab Dados Fiscais -->
        <div class="tab-pane fade" id="fiscal-content" role="tabpanel" aria-labelledby="fiscal-tab">
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="cnae" class="form-label">CNAE</label>
              <input type="text" class="form-control" id="cnae" name="cnae" value="${
                empresa ? empresa.cnae || '' : ''
              }">
            </div>
            <div class="col-md-6 mb-3">
              <label for="atividade" class="form-label">Atividade</label>
              <select class="form-select" id="atividade" name="atividade">
                <option value="">Selecione...</option>
                <option value="Não Aplicado" ${
                  empresa && empresa.atividade === 'Não Aplicado'
                    ? 'selected'
                    : ''
                }>Não Aplicado</option>
                <option value="Indústria" ${
                  empresa && empresa.atividade === 'Indústria' ? 'selected' : ''
                }>Indústria</option>
                <option value="Comércio Varejista" ${
                  empresa && empresa.atividade === 'Comércio Varejista'
                    ? 'selected'
                    : ''
                }>Comércio Varejista</option>
                <option value="Comércio Atacadista" ${
                  empresa && empresa.atividade === 'Comércio Atacadista'
                    ? 'selected'
                    : ''
                }>Comércio Atacadista</option>
                <option value="Distribuidor" ${
                  empresa && empresa.atividade === 'Distribuidor'
                    ? 'selected'
                    : ''
                }>Distribuidor</option>
                <option value="Produtor Rural" ${
                  empresa && empresa.atividade === 'Produtor Rural'
                    ? 'selected'
                    : ''
                }>Produtor Rural</option>
                <option value="Não Contribuinte" ${
                  empresa && empresa.atividade === 'Não Contribuinte'
                    ? 'selected'
                    : ''
                }>Não Contribuinte</option>
              </select>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="tributacao" class="form-label">Regime Tributário</label>
              <select class="form-select" id="tributacao" name="tributacao">
                <option value="">Selecione...</option>
                <option value="Lucro Presumido" ${
                  empresa && empresa.tributacao === 'Lucro Presumido'
                    ? 'selected'
                    : ''
                }>Lucro Presumido</option>
                <option value="Lucro Real" ${
                  empresa && empresa.tributacao === 'Lucro Real'
                    ? 'selected'
                    : ''
                }>Lucro Real</option>
                <option value="Simples Nacional" ${
                  empresa && empresa.tributacao === 'Simples Nacional'
                    ? 'selected'
                    : ''
                }>Simples Nacional</option>
                <option value="Simples Nacional - Sub-Limite" ${
                  empresa && empresa.tributacao === 'Simples Nacional - Sub-Limite'
                    ? 'selected'
                    : ''
                }>Simples Nacional - Sub-Limite</option>
              </select>
            </div>
            <div class="col-md-6 mb-3">
              <label for="pis_cofins" class="form-label">PIS/COFINS</label>
              <select class="form-select" id="pis_cofins" name="pis_cofins">
                <option value="">Selecione...</option>
                <option value="Cumulativo" ${
                  empresa && empresa.pis_cofins === 'Cumulativo'
                    ? 'selected'
                    : ''
                }>Cumulativo</option>
                <option value="Não cumulativo" ${
                  empresa && empresa.pis_cofins === 'Não cumulativo'
                    ? 'selected'
                    : ''
                }>Não cumulativo</option>
                <option value="Ambos" ${
                  empresa && empresa.pis_cofins === 'Ambos'
                    ? 'selected'
                    : ''
                }>Ambos</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Tab Observações -->
        <div class="tab-pane fade" id="observacoes-content" role="tabpanel" aria-labelledby="observacoes-tab">
          <div class="row">
            <div class="col-12 mb-3">
              <label for="observacoes" class="form-label">Observações</label>
              <textarea class="form-control" id="observacoes" name="observacoes" rows="6">${
                empresa ? empresa.observacoes || '' : ''
              }</textarea>
              <small class="form-text text-muted">Adicione informações importantes sobre a empresa que outros usuários devem saber.</small>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-4 d-flex justify-content-end">
        <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">Cancelar</button>
        <button type="submit" class="btn btn-primary" id="save-empresa-btn">Salvar</button>
      </div>
    </form>
  `;

  // Mostrar o modal
  const modal = new bootstrap.Modal(document.getElementById('formModal'));
  modal.show();

  // Configurar busca de CEP
  setupCepSearch();

  // Limtar CNPJ a 14 digitos
  limitCnpjInput();

  // Configurar envio do formulário
  setupEmpresaForm();
}

/**
 * Configura a busca de CEP
 */
function setupCepSearch() {
  const cepButton = document.getElementById('buscar-cep');
  if (!cepButton) return;

  cepButton.addEventListener('click', function () {
    const cep = document.getElementById('cep').value.replace(/\D/g, '');

    if (cep.length !== 8) {
      alert('CEP inválido. Informe os 8 dígitos do CEP.');
      return;
    }

    // Mostrar indicador de carregamento
    cepButton.innerHTML =
      '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
    cepButton.disabled = true;

    // Buscar CEP na API ViaCEP
    fetch(`https://viacep.com.br/ws/${cep}/json/`)
      .then((response) => response.json())
      .then((data) => {
        if (data.erro) {
          alert('CEP não encontrado.');
          return;
        }

        // Preencher campos de endereço
        document.getElementById('logradouro').value = data.logradouro || '';
        document.getElementById('bairro').value = data.bairro || '';
        document.getElementById('cidade').value = data.localidade || '';
        document.getElementById('estado').value = data.uf || '';

        // Focar no campo número
        document.getElementById('numero').focus();
      })
      .catch((error) => {
        console.error('Erro ao buscar CEP:', error);
        alert('Erro ao buscar CEP. Tente novamente.');
      })
      .finally(() => {
        // Restaurar botão
        cepButton.innerHTML = 'Buscar';
        cepButton.disabled = false;
      });
  });
}

/**
 * Limita o campo de CNPJ a 14 dígitos (ignorando pontuação)
 */
function limitCnpjInput() {
  const cnpjInput = document.getElementById('cnpj');
  if (!cnpjInput) return;

  cnpjInput.addEventListener('input', function () {
    let digits = this.value.replace(/\D/g, '');
    if (digits.length > 14) {
      digits = digits.slice(0, 14);
    }
    this.value = digits;
  });
}

/**
 * Configura o envio do formulário de empresa
 */
function setupEmpresaForm() {
  const form = document.getElementById('empresa-form');
  if (!form) return;

  form.addEventListener('submit', function (event) {
    event.preventDefault();

    // Validar formulário
    if (!form.checkValidity()) {
      event.stopPropagation();
      form.classList.add('was-validated');
      return;
    }

    // Desabilitar botão de salvar
    const saveButton = document.getElementById('save-empresa-btn');
    saveButton.disabled = true;
    saveButton.innerHTML =
      '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Salvando...';

    // Preparar dados
    const formData = {
      razao_social: document.getElementById('razao_social').value,
      cnpj: document.getElementById('cnpj').value,
      inscricao_estadual: document.getElementById('inscricao_estadual').value,
      nome_fantasia: document.getElementById('nome_fantasia').value,
      email: document.getElementById('email').value,
      responsavel: document.getElementById('responsavel').value,
      cep: document.getElementById('cep').value,
      logradouro: document.getElementById('logradouro').value,
      numero: document.getElementById('numero').value,
      complemento: document.getElementById('complemento').value,
      bairro: document.getElementById('bairro').value,
      cidade: document.getElementById('cidade').value,
      estado: document.getElementById('estado').value,
      cnae: document.getElementById('cnae').value,
      tributacao: document.getElementById('tributacao').value,
      atividade: document.getElementById('atividade').value,
      pis_cofins: document.getElementById('pis_cofins').value,
      observacoes: document.getElementById('observacoes').value,
    };

    // Adicionar escritório_id se for admin
    if (currentUser.is_admin || currentUser.tipo_usuario === 'admin') {
      formData.escritorio_id = currentUser.escritorio_id;
    }

    // Determinar se é criação ou atualização
    const isUpdate = currentEmpresa !== null;
    const url = isUpdate
      ? `/fiscal/api/empresas/${currentEmpresa.id}`
      : '/fiscal/api/empresas';
    const method = isUpdate ? 'PUT' : 'POST';

    // Enviar requisição
    fetch(url, {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify(formData),
    })
      .then(async (response) => {
        const data = await response.json();
        const formMessage = document.getElementById('form-message');

        if (!response.ok) {
          // Erro
          formMessage.innerHTML = `<div class="alert alert-danger">${data.message || 'Erro ao processar a requisição'}</div>`;
          saveButton.disabled = false;
          saveButton.innerHTML = 'Salvar';
          return;
        }

        // Sucesso
        formMessage.innerHTML = `<div class="alert alert-success">${
          isUpdate
            ? 'Empresa atualizada com sucesso!'
            : 'Empresa criada com sucesso!'
        }</div>`;

        // Fechar modal após 2 segundos
        setTimeout(() => {
          const modal = bootstrap.Modal.getInstance(
            document.getElementById('formModal'),
          );
          modal.hide();

          // Recarregar dados
          loadEmpresasData();
        }, 2000);
      })
      .catch((error) => {
        console.error('Erro ao salvar empresa:', error);
        const formMessage = document.getElementById('form-message');
        formMessage.innerHTML =
          '<div class="alert alert-danger">Erro ao salvar empresa. Tente novamente.</div>';

        // Restaurar botão
        saveButton.disabled = false;
        saveButton.innerHTML = 'Salvar';
      });
  });
}

/**
 * Edita uma empresa existente
 * @param {number} empresaId - ID da empresa
 */
function editEmpresa(empresaId) {
  // Buscar dados da empresa
  fetch(`/fiscal/api/empresas/${empresaId}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.empresa) {
        showEmpresaModal(data.empresa);
      } else {
        alert('Erro ao carregar dados da empresa.');
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar empresa:', error);
      alert('Erro ao carregar dados da empresa. Tente novamente.');
    });
}

/**
 * Exclui uma empresa
 * @param {number} empresaId - ID da empresa
 * @param {string} empresaNome - Nome da empresa para exibir na confirmação
 */
function deleteEmpresa(empresaId, empresaNome) {
  // Confirmar exclusão
  if (
    !confirm(
      `Tem certeza que deseja excluir a empresa "${empresaNome}"? Esta ação não pode ser desfeita.`,
    )
  ) {
    return;
  }

  // Enviar requisição para excluir a empresa
  fetch(`/fiscal/api/empresas/${empresaId}`, {
    method: 'DELETE',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.message) {
        alert(data.message);
        // Recarregar os dados de empresas
        loadEmpresasData();
      } else {
        alert('Erro ao excluir empresa');
      }
    })
    .catch((error) => {
      console.error('Erro ao excluir empresa:', error);
      alert('Erro ao excluir empresa');
  });
}

/**
 * Formata um CNPJ para o padrão 99.999.999/9999-99
 * @param {string} cnpj - CNPJ sem formatação
 * @returns {string} - CNPJ formatado
 */
function formatCNPJ(cnpj) {
  if (!cnpj) return '';
  return cnpj.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
}
