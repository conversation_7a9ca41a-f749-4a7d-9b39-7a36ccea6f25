# Configuração para www.audittei.com.br/fiscal
server {
    listen 80;
    server_name www.audittei.com.br;

    # Redireciona HTTP para HTTPS (recomendado para produção)
    # return 301 https://$host$request_uri;
}

# server {
#     listen 443 ssl http2;
#     server_name www.audittei.com.br;

    # Configurações SSL (descomente e configure após obter os certificados)
    # ssl_certificate /etc/letsencrypt/live/www.audittei.com.br/fullchain.pem;
    # ssl_certificate_key /etc/letsencrypt/live/www.audittei.com.br/privkey.pem;
    # ssl_trusted_certificate /etc/letsencrypt/live/www.audittei.com.br/chain.pem;

    # Otimizações de SSL
    # ssl_protocols TLSv1.2 TLSv1.3;
    # ssl_prefer_server_ciphers on;
    # ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    # ssl_session_cache shared:SSL:10m;
    # ssl_session_timeout 10m;
    # ssl_stapling on;
    # ssl_stapling_verify on;

    # Configuração para o caminho /fiscal
    location /fiscal/ {
        # Remova o /fiscal/ do caminho antes de passar para o backend
        rewrite ^/fiscal/(.*) /$1 break;
        
        # Configurações do proxy
        proxy_pass http://127.0.0.1:5004;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Script-Name /fiscal;
        
        # Configurações de timeout
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        
        # Configurações para WebSocket
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Desativa o buffer para streaming
        proxy_buffering off;
    }

    # Configuração específica para WebSocket
    location /fiscal/socket.io {
        rewrite ^/fiscal/(.*) /$1 break;
        proxy_pass http://127.0.0.1:5004/socket.io;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;  # 24 horas
    }

    # Configuração para arquivos estáticos
    location /fiscal/static/ {
        alias /caminho/completo/para/Auditoria%20Fiscal/front/static/;
        expires 30d;
        access_log off;
        add_header Cache-Control "public";
        # Tente servir o arquivo diretamente, se não existir, encaminhe para o Flask
        try_files $uri @flask_static;
    }

    # Tratamento para arquivos estáticos não encontrados
    location @flask_static {
        rewrite ^/fiscal/(.*)$ /fiscal/ permanent;
    }

    # Configuração para a raiz - redireciona para /fiscal
    location = / {
        return 301 /fiscal/;
    }

    # Configuração de log
    access_log /var/log/nginx/audittei.access.log;
    error_log /var/log/nginx/audittei.error.log;
}

# Redireciona de audittei.com.br para www.audittei.com.br
server {
    listen 80;
    server_name audittei.com.br;
    return 301 $scheme://www.audittei.com.br$request_uri;
}

# server {
#     listen 443 ssl http2;
#     server_name audittei.com.br;
#     return 301 https://www.audittei.com.br$request_uri;
# }
