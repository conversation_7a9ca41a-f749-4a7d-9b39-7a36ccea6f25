/*
 * Dashboard Empresa CSS - Estilos específicos para o dashboard de empresa
 * Inclui correções para o modo escuro
 */

/* Estilo para o texto de ajuda abaixo do título de tributos */
.tributos-help-text {
  color: var(--gray-600);
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

/* Estilo para o card de relatório geral */
.relatorio-geral-card {
  border: 1px solid var(--border-color);
  background-color: var(--card-bg);
  transition: all 0.3s ease;
  color: var(--text-color);
}

.relatorio-geral-card .card-header {
  background: var(--primary) !important;
  border-bottom: 1px solid var(--border-color);
  color: white;
}

.relatorio-geral-card .card-body {
  background-color: var(--card-bg);
  color: var(--text-color);
}

.relatorio-geral-card .text-muted {
  color: var(--text-muted) !important;
}

.relatorio-geral-card .card-text {
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.relatorio-geral-card .small {
  color: var(--text-muted);
}

/* Estilos específicos para modo escuro */
body.dark-theme .tributos-help-text {
  color: var(--dark-text-secondary);
}

body.dark-theme .relatorio-geral-card {
  border-color: var(--dark-border-primary);
  background-color: var(--dark-bg-secondary);
  color: var(--dark-text-primary);
}

body.dark-theme .relatorio-geral-card .card-body {
  background-color: var(--dark-bg-secondary);
  color: var(--dark-text-primary);
}

body.dark-theme .relatorio-geral-card .text-muted,
body.dark-theme .relatorio-geral-card .small {
  color: var(--dark-text-muted) !important;
}

body.dark-theme .relatorio-geral-card .card-text {
  color: var(--dark-text-primary);
}

/* Estilos para sistema de abas */
.dashboard-empresa-tabs {
  margin-top: 1rem;
}

.dashboard-empresa-tabs .nav-tabs {
  border-bottom: 2px solid var(--border-color);
  margin-bottom: 0;
}

.dashboard-empresa-tabs .nav-tabs .nav-link {
  color: var(--text-color);
  border: none;
  border-bottom: 3px solid transparent;
  background: none;
  padding: 1rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.dashboard-empresa-tabs .nav-tabs .nav-link:hover {
  border-bottom-color: var(--primary);
  color: var(--primary);
  background: none;
}

.dashboard-empresa-tabs .nav-tabs .nav-link.active {
  color: var(--primary);
  background: none;
  border-bottom-color: var(--primary);
  font-weight: 600;
}

.dashboard-empresa-tabs .tab-content {
  background: var(--card-bg);
  border-radius: 0 0 0.5rem 0.5rem;
  min-height: 400px;
}

/* Estilos para gráficos */
.dashboard-empresa-graficos {
  padding: 1rem;
}

.dashboard-empresa-graficos .card {
  border: 1px solid var(--border-color);
  background: var(--card-bg);
  transition: all 0.3s ease;
}

.dashboard-empresa-graficos .card:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.dashboard-empresa-graficos .card-header {
  background: var(--gray-50);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
  font-weight: 600;
}

.dashboard-empresa-graficos .card-body {
  padding: 1.5rem;
  background: var(--card-bg);
}

/* Responsividade para gráficos */
@media (max-width: 768px) {
  .dashboard-empresa-graficos .card-body {
    padding: 1rem;
  }

  .dashboard-empresa-tabs .nav-tabs .nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
}

/* Dark mode para gráficos */
[data-theme="dark"] .dashboard-empresa-graficos .card-header,
body.dark-theme .dashboard-empresa-graficos .card-header {
  background: #24282f;
  color: var(--text-color);
}

[data-theme="dark"] .dashboard-empresa-graficos .card:hover,
body.dark-theme .dashboard-empresa-graficos .card:hover {
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .dashboard-empresa-tabs .nav-tabs,
body.dark-theme .dashboard-empresa-tabs .nav-tabs {
  border-bottom-color: var(--gray-600);
}
