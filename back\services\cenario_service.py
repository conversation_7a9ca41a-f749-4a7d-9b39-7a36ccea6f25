from models import db, CenarioICMS, CenarioICMSST, CenarioIPI, CenarioPIS, CenarioCOFINS, CenarioDIFAL, Tributo, Produto
from datetime import datetime
from sqlalchemy import and_

class CenarioService:
    """
    Serviço para gerenciamento de cenários de tributos
    """

    def __init__(self, empresa_id, escritorio_id):
        """
        Inicializa o serviço de cenários

        Args:
            empresa_id (int): ID da empresa
            escritorio_id (int): ID do escritório
        """
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id

    def criar_cenario_importacao(self, cliente_id, produto_id, tipo_tributo, tributo_data):
        """
        Cria um cenário durante a importação XML

        Args:
            cliente_id (int): ID do cliente
            produto_id (int): ID do produto
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
            tributo_data (dict): Dados do tributo

        Returns:
            object: Objeto do cenário criado/atualizado
        """
        # Obter o modelo correspondente
        CenarioModel = self._get_cenario_model(tipo_tributo)

        # Obter CFOP e NCM dos dados do tributo
        cfop = tributo_data.get('cfop')
        ncm = tributo_data.get('ncm')


        # Verificar se já existe um cenário com o mesmo CFOP para esta combinação
        cenario_existente = CenarioModel.query.filter_by(
            empresa_id=self.empresa_id,
            cliente_id=cliente_id,
            produto_id=produto_id,
            cfop=cfop
        ).first()

        # Se já existe um cenário com o mesmo CFOP, verificar o NCM e outros campos específicos
        if cenario_existente:

            # Verificar se o cenário está em produção
            if cenario_existente.status == 'producao':

                # Se o NCM for diferente, criar um cenário inconsistente
                if cenario_existente.ncm != ncm:
                    # Criar cenário inconsistente
                    import time
                    suffix = str(int(time.time()))[-5:]
                    status = f"incons_{suffix}"
                    cenario = self._criar_cenario(cliente_id, produto_id, tipo_tributo, tributo_data, status)
                    return cenario

                # Para IPI, verificar também CST, alíquota e ex
                if tipo_tributo == 'ipi':
                    # Verificar se os valores são diferentes
                    cst_match = cenario_existente.cst == tributo_data.get('cst')
                    aliquota_match = self._comparar_decimal(cenario_existente.aliquota, tributo_data.get('aliquota'))
                    ex_match = cenario_existente.ex == tributo_data.get('ex')


                    if not (cst_match and aliquota_match and ex_match):
                        # Criar cenário inconsistente
                        import time
                        suffix = str(int(time.time()))[-5:]
                        status = f"incons_{suffix}"
                        cenario = self._criar_cenario(cliente_id, produto_id, tipo_tributo, tributo_data, status)
                        return cenario

                # Para outros tipos de tributo, verificar campos específicos
                elif tipo_tributo == 'icms':
                    if not self._verificar_consistencia_icms(cenario_existente, tributo_data):
                        import time
                        suffix = str(int(time.time()))[-5:]
                        status = f"incons_{suffix}"
                        cenario = self._criar_cenario(cliente_id, produto_id, tipo_tributo, tributo_data, status)
                        return cenario
                elif tipo_tributo == 'icms_st':
                    if not self._verificar_consistencia_icms_st(cenario_existente, tributo_data):
                        import time
                        suffix = str(int(time.time()))[-5:]
                        status = f"incons_{suffix}"
                        cenario = self._criar_cenario(cliente_id, produto_id, tipo_tributo, tributo_data, status)
                        return cenario
                elif tipo_tributo == 'pis':
                    if not self._verificar_consistencia_pis(cenario_existente, tributo_data):
                        import time
                        suffix = str(int(time.time()))[-5:]
                        status = f"incons_{suffix}"
                        cenario = self._criar_cenario(cliente_id, produto_id, tipo_tributo, tributo_data, status)
                        return cenario
                elif tipo_tributo == 'cofins':
                    if not self._verificar_consistencia_cofins(cenario_existente, tributo_data):
                        import time
                        suffix = str(int(time.time()))[-5:]
                        status = f"incons_{suffix}"
                        cenario = self._criar_cenario(cliente_id, produto_id, tipo_tributo, tributo_data, status)
                        return cenario
                elif tipo_tributo == 'difal':
                    if not self._verificar_consistencia_difal(cenario_existente, tributo_data):
                        import time
                        suffix = str(int(time.time()))[-5:]
                        status = f"incons_{suffix}"
                        cenario = self._criar_cenario(cliente_id, produto_id, tipo_tributo, tributo_data, status)
                        return cenario


            # Se o cenário não estiver em produção ou os valores forem iguais, atualizar o cenário existente
            # Atualizar os valores do cenário existente
            self._atualizar_valores_cenario(cenario_existente, tributo_data, tipo_tributo)
            # Atualizar data de atualização
            cenario_existente.data_atualizacao = datetime.now()
            # Salvar alterações
            db.session.commit()
            return cenario_existente

        # Se não existe um cenário com o mesmo CFOP, criar um novo cenário com status 'novo'
        # Isso implementa a regra de que CFOP diferente sempre gera um novo cenário
        cenario = self._criar_cenario(cliente_id, produto_id, tipo_tributo, tributo_data, 'novo')
        return cenario

    def buscar_cenario_vigente_por_data(self, tipo_tributo, cliente_id, produto_id, data_referencia, cfop=None):
        """
        Busca o cenário em produção vigente para a data de referência

        Args:
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
            cliente_id (int): ID do cliente
            produto_id (int): ID do produto
            data_referencia (datetime): Data de referência para buscar o cenário vigente
            cfop (str): CFOP para filtrar o cenário (opcional)

        Returns:
            object: Objeto do cenário encontrado ou None
        """
        # Obter o modelo correspondente
        CenarioModel = self._get_cenario_model(tipo_tributo)

        # Buscar cenário em produção ativo e vigente
        query = CenarioModel.query.filter(
            CenarioModel.empresa_id == self.empresa_id,
            CenarioModel.cliente_id == cliente_id,
            CenarioModel.produto_id == produto_id,
            CenarioModel.status == 'producao',
            CenarioModel.ativo == True,
            and_(
                (CenarioModel.data_inicio_vigencia <= data_referencia) | (CenarioModel.data_inicio_vigencia == None),
                (CenarioModel.data_fim_vigencia >= data_referencia) | (CenarioModel.data_fim_vigencia == None)
            )
        )

        # Adicionar filtro de CFOP se fornecido
        if cfop:
            query = query.filter(CenarioModel.cfop == cfop)

        cenario = query.first()

        return cenario

    def atualizar_status_cenario(self, cenario_id, tipo_tributo, novo_status, data_inicio_vigencia=None):
        """
        Atualiza o status de um cenário

        Args:
            cenario_id (int): ID do cenário
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
            novo_status (str): Novo status ('novo', 'producao', 'inconsistente')
            data_inicio_vigencia (date): Data de início de vigência (opcional, será usada se fornecida)

        Returns:
            object: Objeto do cenário atualizado
        """
        from models import Tributo, NotaFiscalItem
        from datetime import datetime

        # Obter o modelo correspondente
        CenarioModel = self._get_cenario_model(tipo_tributo)

        # Buscar o cenário
        cenario = CenarioModel.query.get(cenario_id)

        if not cenario:
            return None


        # Se o status for o mesmo, não fazer nada
        if cenario.status == novo_status:
            return cenario

        # Se estiver atualizando para 'producao', buscar a data de emissão da nota fiscal
        if novo_status == 'producao':

            # Se já temos uma data de início de vigência fornecida, usá-la
            if data_inicio_vigencia:
                print(f"Data de início de vigência fornecida: {data_inicio_vigencia}")
            else:
                # Buscar a nota fiscal mais recente para este produto e cliente
                nota_fiscal = NotaFiscalItem.query.filter_by(
                    empresa_id=cenario.empresa_id,
                    cliente_id=cenario.cliente_id,
                    produto_id=cenario.produto_id
                ).order_by(NotaFiscalItem.data_emissao.desc()).first()

                if nota_fiscal:
                    data_inicio_vigencia = nota_fiscal.data_emissao
                else:
                    # Se não encontrar a nota fiscal, buscar no tributo
                    tributo = Tributo.query.filter_by(
                        empresa_id=cenario.empresa_id,
                        cliente_id=cenario.cliente_id,
                        produto_id=cenario.produto_id
                    ).order_by(Tributo.data_emissao.desc()).first()

                    if tributo:
                        data_inicio_vigencia = tributo.data_emissao
                    else:
                        # Se não encontrar de jeito nenhum, usar a data atual
                        data_inicio_vigencia = datetime.now().date()


        # Buscar cenários em produção existentes para o mesmo produto e cliente
        cenarios_producao = []
        if novo_status == 'producao':

            # Buscar todos os cenários em produção para o mesmo produto/cliente/cfop
            cenarios_producao = CenarioModel.query.filter(
                CenarioModel.empresa_id == cenario.empresa_id,
                CenarioModel.cliente_id == cenario.cliente_id,
                CenarioModel.produto_id == cenario.produto_id,
                CenarioModel.cfop == cenario.cfop,  # Adicionar CFOP na busca
                CenarioModel.status == 'producao',
                CenarioModel.id != cenario_id  # Excluir o próprio cenário da busca
            ).all()

            if cenarios_producao:

                # Verificar se há sobreposição de datas com os cenários existentes
                for cenario_existente in cenarios_producao:
                    # Se o cenário existente não tem data de fim de vigência, definir como data de início do novo cenário
                    if cenario_existente.data_fim_vigencia is None and data_inicio_vigencia:
                        cenario_existente.data_fim_vigencia = data_inicio_vigencia
                    # Se o cenário existente tem data de fim de vigência anterior à data de início do novo cenário
                    # ou se a data de início do novo cenário é nula, não há sobreposição
                    elif (cenario_existente.data_fim_vigencia and data_inicio_vigencia and
                          cenario_existente.data_fim_vigencia < data_inicio_vigencia) or not data_inicio_vigencia:
                        continue
                    # Se houver sobreposição de datas, lançar exceção
                    else:
                        msg = f"Já existe um cenário em produção para o período informado. ID do cenário existente: {cenario_existente.id}"
                        raise ValueError(msg)
            else:

                # Atualizar o status do cenário
                cenario.status = novo_status

        # Se for 'producao', atualizar data de início de vigência e ativar
        if novo_status == 'producao':
            cenario.data_inicio_vigencia = data_inicio_vigencia
            cenario.ativo = True

        # Atualizar data de atualização
        cenario.data_atualizacao = datetime.now()

        # Salvar alterações
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise

        return cenario

    def verificar_cenarios_compativeis(self, cliente_id, produto_id, tipo_tributo):
        """
        Verifica cenários compatíveis com o cenário em produção
        Em vez de atualizar para 'conforme', apenas retorna o número de cenários compatíveis

        Args:
            cliente_id (int): ID do cliente
            produto_id (int): ID do produto
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')

        Returns:
            int: Número de cenários compatíveis
        """
        # Obter o modelo correspondente
        CenarioModel = self._get_cenario_model(tipo_tributo)

        # Buscar o cenário em produção
        cenario_producao = CenarioModel.query.filter_by(
            empresa_id=self.empresa_id,
            cliente_id=cliente_id,
            produto_id=produto_id,
            status='producao',
            ativo=True
        ).first()

        if not cenario_producao:
            return 0

        # Construir a query para verificar cenários compatíveis
        query = CenarioModel.query.filter_by(
            empresa_id=self.empresa_id,
            cliente_id=cliente_id,
            produto_id=produto_id
        ).filter(
            CenarioModel.status.in_(['novo', 'inconsistente']),
            CenarioModel.id != cenario_producao.id
        )

        # Adicionar condições específicas para cada tipo de tributo
        if tipo_tributo == 'icms':
            query = self._adicionar_condicoes_icms(query, cenario_producao)
        elif tipo_tributo == 'icms_st':
            query = self._adicionar_condicoes_icms_st(query, cenario_producao)
        elif tipo_tributo == 'ipi':
            query = self._adicionar_condicoes_ipi(query, cenario_producao)
        elif tipo_tributo == 'pis':
            query = self._adicionar_condicoes_pis(query, cenario_producao)
        elif tipo_tributo == 'cofins':
            query = self._adicionar_condicoes_cofins(query, cenario_producao)
        elif tipo_tributo == 'difal':
            query = self._adicionar_condicoes_difal(query, cenario_producao)

        # Contar cenários compatíveis
        cenarios_compativeis = query.count()

        return cenarios_compativeis

    def _get_cenario_model(self, tipo_tributo):
        """
        Obtém o modelo correspondente ao tipo de tributo

        Args:
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')

        Returns:
            class: Classe do modelo de cenário
        """
        return {
            'icms': CenarioICMS,
            'icms_st': CenarioICMSST,
            'ipi': CenarioIPI,
            'pis': CenarioPIS,
            'cofins': CenarioCOFINS,
            'difal': CenarioDIFAL
        }[tipo_tributo]

    def _comparar_valores(self, cenario, tributo_data, tipo_tributo):
        """
        Compara os valores de um cenário com os valores de um tributo

        Args:
            cenario (object): Objeto do cenário
            tributo_data (dict): Dados do tributo
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')

        Returns:
            bool: True se os valores são iguais, False caso contrário
        """
        # Verificar campos comuns a todos os tipos de tributos
        if cenario.direcao != tributo_data.get('direcao') or cenario.tipo_operacao != tributo_data.get('tipo_operacao'):
            return False

        # Verificar se o NCM é diferente
        if hasattr(cenario, 'ncm') and cenario.ncm != tributo_data.get('ncm'):
            return False

        if tipo_tributo == 'icms':
            return (
                cenario.origem == tributo_data.get('origem') and
                cenario.cst == tributo_data.get('cst') and
                cenario.mod_bc == tributo_data.get('mod_bc') and
                self._comparar_decimal(cenario.p_red_bc, tributo_data.get('p_red_bc')) and
                self._comparar_decimal(cenario.aliquota, tributo_data.get('aliquota')) and
                self._comparar_decimal(cenario.p_dif, tributo_data.get('p_dif'))
            )
        elif tipo_tributo == 'icms_st':
            return (
                cenario.origem == tributo_data.get('origem') and
                cenario.cst == tributo_data.get('cst') and
                cenario.mod_bc == tributo_data.get('mod_bc') and
                self._comparar_decimal(cenario.p_red_bc, tributo_data.get('p_red_bc')) and
                self._comparar_decimal(cenario.aliquota, tributo_data.get('aliquota')) and
                cenario.icms_st_mod_bc == tributo_data.get('icms_st_mod_bc') and
                self._comparar_decimal(cenario.icms_st_aliquota, tributo_data.get('icms_st_aliquota')) and
                self._comparar_decimal(cenario.icms_st_p_mva, tributo_data.get('icms_st_p_mva'))
            )
        elif tipo_tributo == 'ipi':
            return (
                cenario.cst == tributo_data.get('cst') and
                self._comparar_decimal(cenario.aliquota, tributo_data.get('aliquota')) and
                cenario.ex == tributo_data.get('ex')
            )
        elif tipo_tributo == 'pis':
            return (
                cenario.cst == tributo_data.get('cst') and
                self._comparar_decimal(cenario.aliquota, tributo_data.get('aliquota')) and
                self._comparar_decimal(cenario.p_red_bc, tributo_data.get('p_red_bc'))
            )
        elif tipo_tributo == 'cofins':
            return (
                cenario.cst == tributo_data.get('cst') and
                self._comparar_decimal(cenario.aliquota, tributo_data.get('aliquota')) and
                self._comparar_decimal(cenario.p_red_bc, tributo_data.get('p_red_bc'))
            )
        elif tipo_tributo == 'difal':
            return (
                cenario.origem == tributo_data.get('origem') and
                cenario.cst == tributo_data.get('cst') and
                cenario.mod_bc == tributo_data.get('mod_bc') and
                self._comparar_decimal(cenario.p_red_bc, tributo_data.get('p_red_bc')) and
                self._comparar_decimal(cenario.aliquota, tributo_data.get('aliquota')) and
                self._comparar_decimal(cenario.p_fcp_uf_dest, tributo_data.get('p_fcp_uf_dest')) and
                self._comparar_decimal(cenario.p_icms_uf_dest, tributo_data.get('p_icms_uf_dest')) and
                self._comparar_decimal(cenario.p_icms_inter, tributo_data.get('p_icms_inter')) and
                self._comparar_decimal(cenario.p_icms_inter_part, tributo_data.get('p_icms_inter_part'))
            )
        return False

    def _comparar_decimal(self, valor1, valor2):
        """
        Compara dois valores decimais

        Args:
            valor1: Primeiro valor
            valor2: Segundo valor

        Returns:
            bool: True se os valores são iguais, False caso contrário
        """
        # Log dos valores originais e seus tipos

        # Verificar se ambos são None
        if valor1 is None and valor2 is None:
            return True

        # Verificar se apenas um é None
        if valor1 is None or valor2 is None:
            return False

        # Converter para float para comparação
        try:
            # Tratar strings vazias como zero
            if valor1 == '':
                valor1 = 0
            if valor2 == '':
                valor2 = 0

            # Converter para float
            v1 = float(valor1)
            v2 = float(valor2)

            # Comparar com uma pequena margem de erro para evitar problemas de precisão
            diferenca = abs(v1 - v2)
            resultado = diferenca < 0.0001

            return resultado

        except (ValueError, TypeError) as e:
            return False

    def _atualizar_valores_cenario(self, cenario, tributo_data, tipo_tributo):
        """
        Atualiza os valores de um cenário existente

        Args:
            cenario (object): Objeto do cenário a ser atualizado
            tributo_data (dict): Dados do tributo
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')

        Returns:
            object: Objeto do cenário atualizado
        """
        # Atualizar campos comuns a todos os tipos de tributos
        cenario.direcao = tributo_data.get('direcao')
        cenario.tipo_operacao = tributo_data.get('tipo_operacao')
        cenario.cfop = tributo_data.get('cfop')  # Incluir CFOP
        cenario.ncm = tributo_data.get('ncm')    # Incluir NCM

        if tipo_tributo == 'icms':
            cenario.origem = tributo_data.get('origem')
            cenario.cst = tributo_data.get('cst')
            cenario.mod_bc = tributo_data.get('mod_bc')
            cenario.p_red_bc = tributo_data.get('p_red_bc')
            cenario.aliquota = tributo_data.get('aliquota')
            cenario.p_dif = tributo_data.get('p_dif')
        elif tipo_tributo == 'icms_st':
            cenario.origem = tributo_data.get('origem')
            cenario.cst = tributo_data.get('cst')
            cenario.mod_bc = tributo_data.get('mod_bc')
            cenario.p_red_bc = tributo_data.get('p_red_bc')
            cenario.aliquota = tributo_data.get('aliquota')
            cenario.icms_st_mod_bc = tributo_data.get('icms_st_mod_bc')
            cenario.icms_st_aliquota = tributo_data.get('icms_st_aliquota')
            cenario.icms_st_p_mva = tributo_data.get('icms_st_p_mva')
        elif tipo_tributo == 'ipi':
            cenario.cst = tributo_data.get('cst')
            cenario.aliquota = tributo_data.get('aliquota')
            cenario.ex = tributo_data.get('ex')
        elif tipo_tributo == 'pis':
            cenario.cst = tributo_data.get('cst')
            cenario.aliquota = tributo_data.get('aliquota')
            cenario.p_red_bc = tributo_data.get('p_red_bc')
        elif tipo_tributo == 'cofins':
            cenario.cst = tributo_data.get('cst')
            cenario.aliquota = tributo_data.get('aliquota')
            cenario.p_red_bc = tributo_data.get('p_red_bc')
        elif tipo_tributo == 'difal':
            cenario.origem = tributo_data.get('origem')
            cenario.cst = tributo_data.get('cst')
            cenario.mod_bc = tributo_data.get('mod_bc')
            cenario.p_red_bc = tributo_data.get('p_red_bc')
            cenario.aliquota = tributo_data.get('aliquota')
            cenario.p_fcp_uf_dest = tributo_data.get('p_fcp_uf_dest')
            cenario.p_icms_uf_dest = tributo_data.get('p_icms_uf_dest')
            cenario.p_icms_inter = tributo_data.get('p_icms_inter')
            cenario.p_icms_inter_part = tributo_data.get('p_icms_inter_part')
            cenario.direcao = tributo_data.get('direcao')

        return cenario

    def _criar_cenario(self, cliente_id, produto_id, tipo_tributo, tributo_data, status):
        """
        Cria um novo cenário

        Args:
            cliente_id (int): ID do cliente
            produto_id (int): ID do produto
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
            tributo_data (dict): Dados do tributo
            status (str): Status do cenário ('novo', 'producao', 'inconsistente')

        Returns:
            object: Objeto do cenário criado
        """
        # Registrar informações sobre a criação do cenário

        # Criar o cenário com os valores do tributo
        if tipo_tributo == 'icms':
            cenario = CenarioICMS(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                cliente_id=cliente_id,
                produto_id=produto_id,
                origem=tributo_data.get('origem'),
                cst=tributo_data.get('cst'),
                mod_bc=tributo_data.get('mod_bc'),
                p_red_bc=tributo_data.get('p_red_bc'),
                aliquota=tributo_data.get('aliquota'),
                p_dif=tributo_data.get('p_dif'),
                direcao=tributo_data.get('direcao'),
                tipo_operacao=tributo_data.get('tipo_operacao'),
                cfop=tributo_data.get('cfop'),  # Incluir CFOP
                ncm=tributo_data.get('ncm'),    # Incluir NCM
                status=status,
                data_criacao=datetime.now(),
                data_atualizacao=datetime.now()
            )
        elif tipo_tributo == 'icms_st':
            cenario = CenarioICMSST(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                cliente_id=cliente_id,
                produto_id=produto_id,
                origem=tributo_data.get('origem'),
                cst=tributo_data.get('cst'),
                mod_bc=tributo_data.get('mod_bc'),
                p_red_bc=tributo_data.get('p_red_bc'),
                aliquota=tributo_data.get('aliquota'),
                icms_st_mod_bc=tributo_data.get('icms_st_mod_bc'),
                icms_st_aliquota=tributo_data.get('icms_st_aliquota'),
                icms_st_p_mva=tributo_data.get('icms_st_p_mva'),
                direcao=tributo_data.get('direcao'),
                tipo_operacao=tributo_data.get('tipo_operacao'),
                cfop=tributo_data.get('cfop'),  # Incluir CFOP
                ncm=tributo_data.get('ncm'),    # Incluir NCM
                status=status,
                data_criacao=datetime.now(),
                data_atualizacao=datetime.now()
            )
        elif tipo_tributo == 'ipi':
            cenario = CenarioIPI(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                cliente_id=cliente_id,
                produto_id=produto_id,
                cst=tributo_data.get('cst'),
                aliquota=tributo_data.get('aliquota'),
                ex=tributo_data.get('ex'),
                direcao=tributo_data.get('direcao'),
                tipo_operacao=tributo_data.get('tipo_operacao'),
                cfop=tributo_data.get('cfop'),  # Incluir CFOP
                ncm=tributo_data.get('ncm'),    # Incluir NCM
                status=status,
                data_criacao=datetime.now(),
                data_atualizacao=datetime.now()
            )
        elif tipo_tributo == 'pis':
            cenario = CenarioPIS(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                cliente_id=cliente_id,
                produto_id=produto_id,
                cst=tributo_data.get('cst'),
                aliquota=tributo_data.get('aliquota'),
                p_red_bc=tributo_data.get('p_red_bc'),
                direcao=tributo_data.get('direcao'),
                tipo_operacao=tributo_data.get('tipo_operacao'),
                cfop=tributo_data.get('cfop'),  # Incluir CFOP
                ncm=tributo_data.get('ncm'),    # Incluir NCM
                status=status,
                data_criacao=datetime.now(),
                data_atualizacao=datetime.now()
            )
        elif tipo_tributo == 'cofins':
            cenario = CenarioCOFINS(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                cliente_id=cliente_id,
                produto_id=produto_id,
                cst=tributo_data.get('cst'),
                aliquota=tributo_data.get('aliquota'),
                p_red_bc=tributo_data.get('p_red_bc'),
                direcao=tributo_data.get('direcao'),
                tipo_operacao=tributo_data.get('tipo_operacao'),
                cfop=tributo_data.get('cfop'),  # Incluir CFOP
                ncm=tributo_data.get('ncm'),    # Incluir NCM
                status=status,
                data_criacao=datetime.now(),
                data_atualizacao=datetime.now()
            )
        elif tipo_tributo == 'difal':
            cenario = CenarioDIFAL(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                cliente_id=cliente_id,
                produto_id=produto_id,
                origem=tributo_data.get('origem'),
                cst=tributo_data.get('cst'),
                mod_bc=tributo_data.get('mod_bc'),
                p_red_bc=tributo_data.get('p_red_bc'),
                aliquota=tributo_data.get('aliquota'),
                p_fcp_uf_dest=tributo_data.get('p_fcp_uf_dest'),
                p_icms_uf_dest=tributo_data.get('p_icms_uf_dest'),
                p_icms_inter=tributo_data.get('p_icms_inter'),
                p_icms_inter_part=tributo_data.get('p_icms_inter_part'),
                direcao=tributo_data.get('direcao'),
                tipo_operacao=tributo_data.get('tipo_operacao'),
                cfop=tributo_data.get('cfop'),  # Incluir CFOP
                ncm=tributo_data.get('ncm'),    # Incluir NCM
                status=status,
                data_criacao=datetime.now(),
                data_atualizacao=datetime.now()
            )

        db.session.add(cenario)
        db.session.commit()

        return cenario

    def _adicionar_condicoes_icms(self, query, cenario_producao):
        """
        Adiciona condições específicas para ICMS

        Args:
            query: Query base
            cenario_producao: Cenário em produção

        Returns:
            Query com condições adicionadas
        """
        return query.filter(
            CenarioICMS.origem == cenario_producao.origem,
            CenarioICMS.cst == cenario_producao.cst,
            CenarioICMS.mod_bc == cenario_producao.mod_bc,
            CenarioICMS.p_red_bc == cenario_producao.p_red_bc,
            CenarioICMS.aliquota == cenario_producao.aliquota,
            CenarioICMS.p_dif == cenario_producao.p_dif,
            CenarioICMS.ncm == cenario_producao.ncm  # Incluir NCM na comparação
        )

    def _adicionar_condicoes_icms_st(self, query, cenario_producao):
        """
        Adiciona condições específicas para ICMS-ST

        Args:
            query: Query base
            cenario_producao: Cenário em produção

        Returns:
            Query com condições adicionadas
        """
        return query.filter(
            CenarioICMSST.origem == cenario_producao.origem,
            CenarioICMSST.cst == cenario_producao.cst,
            CenarioICMSST.mod_bc == cenario_producao.mod_bc,
            CenarioICMSST.p_red_bc == cenario_producao.p_red_bc,
            CenarioICMSST.aliquota == cenario_producao.aliquota,
            CenarioICMSST.icms_st_mod_bc == cenario_producao.icms_st_mod_bc,
            CenarioICMSST.icms_st_aliquota == cenario_producao.icms_st_aliquota,
            CenarioICMSST.icms_st_p_mva == cenario_producao.icms_st_p_mva,
            CenarioICMSST.ncm == cenario_producao.ncm  # Incluir NCM na comparação
        )

    def _adicionar_condicoes_ipi(self, query, cenario_producao):
        """
        Adiciona condições específicas para IPI

        Args:
            query: Query base
            cenario_producao: Cenário em produção

        Returns:
            Query com condições adicionadas
        """
        return query.filter(
            CenarioIPI.cst == cenario_producao.cst,
            CenarioIPI.aliquota == cenario_producao.aliquota,
            CenarioIPI.ex == cenario_producao.ex,
            CenarioIPI.ncm == cenario_producao.ncm  # Incluir NCM na comparação
        )

    def _adicionar_condicoes_pis(self, query, cenario_producao):
        """
        Adiciona condições específicas para PIS

        Args:
            query: Query base
            cenario_producao: Cenário em produção

        Returns:
            Query com condições adicionadas
        """
        return query.filter(
            CenarioPIS.cst == cenario_producao.cst,
            CenarioPIS.aliquota == cenario_producao.aliquota,
            CenarioPIS.p_red_bc == cenario_producao.p_red_bc,
            CenarioPIS.ncm == cenario_producao.ncm  # Incluir NCM na comparação
        )

    def _adicionar_condicoes_cofins(self, query, cenario_producao):
        """
        Adiciona condições específicas para COFINS

        Args:
            query: Query base
            cenario_producao: Cenário em produção

        Returns:
            Query com condições adicionadas
        """
        return query.filter(
            CenarioCOFINS.cst == cenario_producao.cst,
            CenarioCOFINS.aliquota == cenario_producao.aliquota,
            CenarioCOFINS.p_red_bc == cenario_producao.p_red_bc,
            CenarioCOFINS.ncm == cenario_producao.ncm  # Incluir NCM na comparação
        )

    def ativar_cenario(self, cenario_id, tipo_tributo):
        """
        Ativa um cenário e desativa outros cenários do mesmo tipo para a mesma combinação empresa/cliente/produto

        Args:
            cenario_id (int): ID do cenário
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')

        Returns:
            object: Objeto do cenário ativado
        """
        # Obter o modelo correspondente
        CenarioModel = self._get_cenario_model(tipo_tributo)

        # Buscar o cenário
        cenario = CenarioModel.query.get(cenario_id)

        if not cenario:
            return None

        # Verificar se o cenário está em produção
        if cenario.status != 'producao':
            raise ValueError("Apenas cenários com status 'producao' podem ser ativados")

        # Desativar outros cenários do mesmo tipo para a mesma combinação empresa/cliente/produto
        CenarioModel.query.filter_by(
            empresa_id=cenario.empresa_id,
            cliente_id=cenario.cliente_id,
            produto_id=cenario.produto_id,
            status='producao'
        ).filter(
            CenarioModel.id != cenario.id
        ).update({
            'ativo': False
        })

        # Ativar o cenário
        cenario.ativo = True
        cenario.data_atualizacao = datetime.now()

        # Salvar alterações
        db.session.commit()

        # Recalcular tributos para o produto
        self._recalcular_tributos(cenario.empresa_id, cenario.produto_id)

        return cenario

    def desativar_cenario(self, cenario_id, tipo_tributo):
        """
        Desativa um cenário

        Args:
            cenario_id (int): ID do cenário
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')

        Returns:
            object: Objeto do cenário desativado
        """
        # Obter o modelo correspondente
        CenarioModel = self._get_cenario_model(tipo_tributo)

        # Buscar o cenário
        cenario = CenarioModel.query.get(cenario_id)

        if not cenario:
            return None

        # Desativar o cenário
        cenario.ativo = False
        cenario.data_atualizacao = datetime.now()

        # Salvar alterações
        db.session.commit()

        return cenario

    def excluir_cenario(self, cenario_id, tipo_tributo):
        """
        Exclui um cenário

        Args:
            cenario_id (int): ID do cenário
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')

        Returns:
            bool: True se o cenário foi excluído com sucesso, False caso contrário
        """
        # Obter o modelo correspondente
        CenarioModel = self._get_cenario_model(tipo_tributo)

        # Buscar o cenário
        cenario = CenarioModel.query.get(cenario_id)

        if not cenario:
            return False

        # Verificar se o cenário pertence à empresa
        if cenario.empresa_id != self.empresa_id:
            return False

        # Verificar se o cenário está em produção e ativo
        if cenario.status == 'producao' and cenario.ativo:
            # Não permitir excluir cenários em produção ativos
            return False

        # Excluir o cenário
        db.session.delete(cenario)
        db.session.commit()

        return True

    def atualizar_vigencia_cenario(self, cenario_id, tipo_tributo, data_inicio_vigencia, data_fim_vigencia=None):
        """
        Atualiza a vigência de um cenário

        Args:
            cenario_id (int): ID do cenário
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
            data_inicio_vigencia (date): Data de início de vigência
            data_fim_vigencia (date): Data de fim de vigência (opcional)

        Returns:
            object: Objeto do cenário atualizado
        """
        # Obter o modelo correspondente
        CenarioModel = self._get_cenario_model(tipo_tributo)

        # Buscar o cenário
        cenario = CenarioModel.query.get(cenario_id)

        if not cenario:
            return None

        # Verificar se o cenário está em produção
        if cenario.status != 'producao':
            raise ValueError("Apenas cenários com status 'producao' podem ter sua vigência atualizada")

        # Atualizar vigência
        cenario.data_inicio_vigencia = data_inicio_vigencia
        cenario.data_fim_vigencia = data_fim_vigencia
        cenario.data_atualizacao = datetime.now()

        # Salvar alterações
        db.session.commit()

        return cenario

    def _recalcular_tributos(self, empresa_id, produto_id):
        """
        Recalcula os tributos para um produto

        Args:
            empresa_id (int): ID da empresa
            produto_id (int): ID do produto

        Returns:
            int: Número de tributos recalculados
        """
        # Buscar tributos do produto
        tributos = Tributo.query.filter_by(
            empresa_id=empresa_id,
            produto_id=produto_id
        ).all()

        # Recalcular cada tributo
        for tributo in tributos:
            self._calcular_tributo(tributo)

        # Atualizar status do produto para indicar que foi calculado
        produto = Produto.query.get(produto_id)
        if produto:
            # Usar 'calculado' em vez de 'conforme'
            produto.status = 'calculado'
            db.session.commit()

        return len(tributos)

    def _calcular_tributo(self, tributo):
        """
        Calcula os valores de um tributo com base nos cenários ativos

        Args:
            tributo (Tributo): Objeto do tributo

        Returns:
            bool: True se o cálculo foi realizado, False caso contrário
        """
        # Calcular ICMS
        self._calcular_icms(tributo)

        # Calcular ICMS-ST
        self._calcular_icms_st(tributo)

        # Calcular IPI
        self._calcular_ipi(tributo)

        # Calcular PIS
        self._calcular_pis(tributo)

        # Calcular COFINS
        self._calcular_cofins(tributo)

        # Calcular DIFAL
        self._calcular_difal(tributo)

        # Salvar alterações
        db.session.commit()

        return True

    def reverificar_status_cenarios(self, tipo_tributo):
        """
        Reverifica o status de todos os cenários de um tipo de tributo

        Args:
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')

        Returns:
            int: Número de cenários atualizados
        """
        # Obter o modelo correspondente
        CenarioModel = self._get_cenario_model(tipo_tributo)

        # Buscar cenários em produção
        cenarios_producao = CenarioModel.query.filter_by(
            empresa_id=self.empresa_id,
            status='producao'
        ).all()

        if not cenarios_producao:
            return 0

        # Buscar cenários novos e inconsistentes
        cenarios_para_verificar = CenarioModel.query.filter(
            CenarioModel.empresa_id == self.empresa_id,
            CenarioModel.status.in_(['novo', 'inconsistente'])
        ).all()

        if not cenarios_para_verificar:
            return 0

        # Contar cenários atualizados
        cenarios_atualizados = 0

        # Para cada cenário em produção
        for cenario_producao in cenarios_producao:
            # Buscar cenários do mesmo produto e cliente
            cenarios_mesmo_produto_cliente = [
                c for c in cenarios_para_verificar
                if c.produto_id == cenario_producao.produto_id and c.cliente_id == cenario_producao.cliente_id
            ]

            # Para cada cenário do mesmo produto e cliente
            for cenario in cenarios_mesmo_produto_cliente:
                # Verificar se o cenário é consistente com o cenário em produção
                if self._verificar_consistencia_cenario(cenario, cenario_producao, tipo_tributo):
                    # Não atualizar para 'conforme', apenas contar
                    cenarios_atualizados += 1
                elif cenario.status == 'novo':
                    # Atualizar status para 'inconsistente'
                    cenario.status = 'inconsistente'
                    cenario.data_atualizacao = datetime.now()
                    cenarios_atualizados += 1

        # Salvar alterações
        db.session.commit()

        return cenarios_atualizados

    def reverificar_status_cenarios_produto(self, tipo_tributo, produto_id):
        """
        Reverifica o status de cenários de um produto específico

        Args:
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
            produto_id (int): ID do produto

        Returns:
            int: Número de cenários atualizados
        """
        # Obter o modelo correspondente
        CenarioModel = self._get_cenario_model(tipo_tributo)

        # Buscar cenários em produção para o produto
        cenarios_producao = CenarioModel.query.filter_by(
            empresa_id=self.empresa_id,
            produto_id=produto_id,
            status='producao'
        ).all()

        if not cenarios_producao:
            return 0

        # Buscar cenários novos e inconsistentes para o produto
        cenarios_para_verificar = CenarioModel.query.filter(
            CenarioModel.empresa_id == self.empresa_id,
            CenarioModel.produto_id == produto_id,
            CenarioModel.status.in_(['novo', 'inconsistente'])
        ).all()

        if not cenarios_para_verificar:
            return 0

        # Contar cenários atualizados
        cenarios_atualizados = 0

        # Para cada cenário em produção
        for cenario_producao in cenarios_producao:
            # Buscar cenários do mesmo cliente
            cenarios_mesmo_cliente = [
                c for c in cenarios_para_verificar
                if c.cliente_id == cenario_producao.cliente_id
            ]

            # Para cada cenário do mesmo cliente
            for cenario in cenarios_mesmo_cliente:
                # Verificar se o cenário é consistente com o cenário em produção
                if self._verificar_consistencia_cenario(cenario, cenario_producao, tipo_tributo):
                    # Não atualizar para 'conforme', apenas contar
                    cenarios_atualizados += 1
                elif cenario.status == 'novo':
                    # Atualizar status para 'inconsistente'
                    cenario.status = 'inconsistente'
                    cenario.data_atualizacao = datetime.now()
                    cenarios_atualizados += 1

        # Salvar alterações
        db.session.commit()

        return cenarios_atualizados

    def _verificar_consistencia_cenario(self, cenario, cenario_producao, tipo_tributo):
        """
        Verifica se um cenário é consistente com um cenário em produção

        Args:
            cenario (object): Cenário a ser verificado
            cenario_producao (object): Cenário em produção
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')

        Returns:
            bool: True se o cenário é consistente, False caso contrário
        """

        # Verificar campos comuns a todos os tipos de tributos
        direcao_match = cenario.direcao == cenario_producao.direcao
        tipo_operacao_match = cenario.tipo_operacao == cenario_producao.tipo_operacao


        if not direcao_match or not tipo_operacao_match:
            return False

        # Verificar se o NCM é diferente
        if hasattr(cenario, 'ncm') and hasattr(cenario_producao, 'ncm'):
            ncm_match = cenario.ncm == cenario_producao.ncm

            if not ncm_match:
                return False

        # Verificar CFOP
        if hasattr(cenario, 'cfop') and hasattr(cenario_producao, 'cfop'):
            cfop_match = cenario.cfop == cenario_producao.cfop

            # Não retornamos False aqui porque CFOP diferente não necessariamente torna o cenário inconsistente
            # Apenas registramos no log para análise

        # Verificar consistência com base no tipo de tributo

        # Criar um dicionário com os dados do cenário de produção para comparação
        cenario_producao_data = {}

        if tipo_tributo == 'icms':
            # Extrair dados do cenário de produção para comparação
            cenario_producao_data = {
                'cst': cenario_producao.cst,
                'origem': cenario_producao.origem,
                'aliquota': cenario_producao.aliquota,
                'p_red_bc': cenario_producao.p_red_bc
            }
            result = self._verificar_consistencia_icms(cenario, cenario_producao_data)
        elif tipo_tributo == 'icms_st':
            cenario_producao_data = {
                'cst': cenario_producao.cst,
                'origem': cenario_producao.origem,
                'aliquota': cenario_producao.aliquota,
                'p_red_bc': cenario_producao.p_red_bc,
                'icms_st_p_mva': cenario_producao.icms_st_p_mva,
                'icms_st_aliquota': cenario_producao.icms_st_aliquota,
                'icms_st_p_red_bc': cenario_producao.icms_st_p_red_bc
            }
            result = self._verificar_consistencia_icms_st(cenario, cenario_producao_data)
        elif tipo_tributo == 'ipi':
            cenario_producao_data = {
                'cst': cenario_producao.cst,
                'aliquota': cenario_producao.aliquota,
                'ex': cenario_producao.ex
            }
            result = self._verificar_consistencia_ipi(cenario, cenario_producao_data)
        elif tipo_tributo == 'pis':
            cenario_producao_data = {
                'cst': cenario_producao.cst,
                'aliquota': cenario_producao.aliquota,
                'p_red_bc': cenario_producao.p_red_bc
            }
            result = self._verificar_consistencia_pis(cenario, cenario_producao_data)
        elif tipo_tributo == 'cofins':
            cenario_producao_data = {
                'cst': cenario_producao.cst,
                'aliquota': cenario_producao.aliquota,
                'p_red_bc': cenario_producao.p_red_bc
            }
            result = self._verificar_consistencia_cofins(cenario, cenario_producao_data)
        elif tipo_tributo == 'difal':
            cenario_producao_data = {
                'p_icms_uf_dest': cenario_producao.p_icms_uf_dest,
                'p_icms_inter': cenario_producao.p_icms_inter,
                'p_icms_inter_part': cenario_producao.p_icms_inter_part,
                'p_fcp_uf_dest': cenario_producao.p_fcp_uf_dest,
                'p_red_bc': cenario_producao.p_red_bc
            }
            result = self._verificar_consistencia_difal(cenario, cenario_producao_data)
        else:
            result = False

        return result

    def _verificar_consistencia_icms(self, cenario, tributo_data=None):
        """
        Verifica se um cenário de ICMS é consistente com os dados do tributo ou com outro cenário

        Args:
            cenario (CenarioICMS): Cenário a ser verificado
            tributo_data (dict, optional): Dados do tributo. Se None, assume que o segundo argumento é outro cenário.

        Returns:
            bool: True se o cenário é consistente, False caso contrário
        """
        if tributo_data is None:
            # Comparação entre dois cenários
            cenario_producao = cenario

            # Verificar cada campo individualmente e registrar no log
            cst_match = cenario.cst == cenario_producao.cst
            origem_match = cenario.origem == cenario_producao.origem

            # Usar _comparar_decimal para valores numéricos
            aliquota_match = self._comparar_decimal(cenario.aliquota, cenario_producao.aliquota)
            p_red_bc_match = self._comparar_decimal(cenario.p_red_bc, cenario_producao.p_red_bc)

            # Registrar detalhes no log

            # Resultado final
            result = cst_match and origem_match and aliquota_match and p_red_bc_match

            return result
        else:
            # Comparação com dados do tributo
            # Verificar cada campo individualmente e registrar no log
            cst_match = cenario.cst == tributo_data.get('cst')
            origem_match = cenario.origem == tributo_data.get('origem')

            # Usar _comparar_decimal para valores numéricos
            aliquota_match = self._comparar_decimal(cenario.aliquota, tributo_data.get('aliquota'))
            p_red_bc_match = self._comparar_decimal(cenario.p_red_bc, tributo_data.get('p_red_bc'))

            # Registrar detalhes no log

            # Resultado final
            result = cst_match and origem_match and aliquota_match and p_red_bc_match

            return result

    def _verificar_consistencia_icms_st(self, cenario, tributo_data=None):
        """
        Verifica se um cenário de ICMS-ST é consistente com os dados do tributo ou com outro cenário

        Args:
            cenario (CenarioICMSST): Cenário a ser verificado
            tributo_data (dict, optional): Dados do tributo. Se None, assume que o segundo argumento é outro cenário.

        Returns:
            bool: True se o cenário é consistente, False caso contrário
        """
        if tributo_data is None:
            # Comparação entre dois cenários
            cenario_producao = cenario
            return (
                cenario.cst == cenario_producao.cst and
                cenario.origem == cenario_producao.origem and
                cenario.aliquota == cenario_producao.aliquota and
                cenario.p_red_bc == cenario_producao.p_red_bc and
                cenario.icms_st_p_mva == cenario_producao.icms_st_p_mva and
                cenario.icms_st_aliquota == cenario_producao.icms_st_aliquota and
                cenario.icms_st_p_red_bc == cenario_producao.icms_st_p_red_bc
            )
        else:
            # Comparação com dados do tributo
            return (
                cenario.cst == tributo_data.get('cst') and
                cenario.origem == tributo_data.get('origem') and
                cenario.aliquota == tributo_data.get('aliquota') and
                cenario.p_red_bc == tributo_data.get('p_red_bc') and
                cenario.icms_st_p_mva == tributo_data.get('icms_st_p_mva') and
                cenario.icms_st_aliquota == tributo_data.get('icms_st_aliquota') and
                cenario.icms_st_p_red_bc == tributo_data.get('icms_st_p_red_bc')
            )

    def _verificar_consistencia_ipi(self, cenario, tributo_data=None):
        """
        Verifica se um cenário de IPI é consistente com os dados do tributo ou com outro cenário

        Args:
            cenario (CenarioIPI): Cenário a ser verificado
            tributo_data (dict, optional): Dados do tributo. Se None, assume que o segundo argumento é outro cenário.

        Returns:
            bool: True se o cenário é consistente, False caso contrário
        """
        if tributo_data is None:
            # Comparação entre dois cenários
            cenario_producao = cenario
            # Verificar apenas os campos que existem no modelo CenarioIPI
            return (
                cenario.cst == cenario_producao.cst and
                cenario.aliquota == cenario_producao.aliquota and
                cenario.ex == cenario_producao.ex
            )
        else:
            # Comparação com dados do tributo
            return (
                cenario.cst == tributo_data.get('cst') and
                cenario.aliquota == tributo_data.get('aliquota') and
                cenario.ex == tributo_data.get('ex')
            )

    def _verificar_consistencia_pis(self, cenario, tributo_data=None):
        """
        Verifica se um cenário de PIS é consistente com os dados do tributo ou com outro cenário

        Args:
            cenario (CenarioPIS): Cenário a ser verificado
            tributo_data (dict, optional): Dados do tributo. Se None, assume que o segundo argumento é outro cenário.

        Returns:
            bool: True se o cenário é consistente, False caso contrário
        """
        if tributo_data is None:
            # Comparação entre dois cenários
            cenario_producao = cenario
            return (
                cenario.cst == cenario_producao.cst and
                cenario.aliquota == cenario_producao.aliquota and
                cenario.p_red_bc == cenario_producao.p_red_bc
            )
        else:
            # Comparação com dados do tributo
            return (
                cenario.cst == tributo_data.get('cst') and
                cenario.aliquota == tributo_data.get('aliquota') and
                cenario.p_red_bc == tributo_data.get('p_red_bc')
            )

    def _verificar_consistencia_cofins(self, cenario, tributo_data=None):
        """
        Verifica se um cenário de COFINS é consistente com os dados do tributo ou com outro cenário

        Args:
            cenario (CenarioCOFINS): Cenário a ser verificado
            tributo_data (dict, optional): Dados do tributo. Se None, assume que o segundo argumento é outro cenário.

        Returns:
            bool: True se o cenário é consistente, False caso contrário
        """
        if tributo_data is None:
            # Comparação entre dois cenários
            cenario_producao = cenario
            return (
                cenario.cst == cenario_producao.cst and
                cenario.aliquota == cenario_producao.aliquota and
                cenario.p_red_bc == cenario_producao.p_red_bc
            )
        else:
            # Comparação com dados do tributo
            return (
                cenario.cst == tributo_data.get('cst') and
                cenario.aliquota == tributo_data.get('aliquota') and
                cenario.p_red_bc == tributo_data.get('p_red_bc')
            )

    def _verificar_consistencia_difal(self, cenario, tributo_data=None):
        """
        Verifica se um cenário de DIFAL é consistente com os dados do tributo ou com outro cenário

        Args:
            cenario (CenarioDIFAL): Cenário a ser verificado
            tributo_data (dict, optional): Dados do tributo. Se None, assume que o segundo argumento é outro cenário.

        Returns:
            bool: True se o cenário é consistente, False caso contrário
        """
        if tributo_data is None:
            # Comparação entre dois cenários
            cenario_producao = cenario
            return (
                cenario.p_icms_uf_dest == cenario_producao.p_icms_uf_dest and
                cenario.p_icms_inter == cenario_producao.p_icms_inter and
                cenario.p_icms_inter_part == cenario_producao.p_icms_inter_part and
                cenario.p_fcp_uf_dest == cenario_producao.p_fcp_uf_dest and
                cenario.p_red_bc == cenario_producao.p_red_bc
            )
        else:
            # Comparação com dados do tributo
            return (
                cenario.p_icms_uf_dest == tributo_data.get('p_icms_uf_dest') and
                cenario.p_icms_inter == tributo_data.get('p_icms_inter') and
                cenario.p_icms_inter_part == tributo_data.get('p_icms_inter_part') and
                cenario.p_fcp_uf_dest == tributo_data.get('p_fcp_uf_dest') and
                cenario.p_red_bc == tributo_data.get('p_red_bc')
            )

    def _adicionar_condicoes_difal(self, query, cenario_producao):
        """
        Adiciona condições específicas para DIFAL

        Args:
            query: Query base
            cenario_producao: Cenário em produção

        Returns:
            Query com condições adicionadas
        """
        return query.filter(
            CenarioDIFAL.origem == cenario_producao.origem,
            CenarioDIFAL.cst == cenario_producao.cst,
            CenarioDIFAL.mod_bc == cenario_producao.mod_bc,
            CenarioDIFAL.p_red_bc == cenario_producao.p_red_bc,
            CenarioDIFAL.aliquota == cenario_producao.aliquota,
            CenarioDIFAL.p_fcp_uf_dest == cenario_producao.p_fcp_uf_dest,
            CenarioDIFAL.p_icms_uf_dest == cenario_producao.p_icms_uf_dest,
            CenarioDIFAL.p_icms_inter == cenario_producao.p_icms_inter,
            CenarioDIFAL.p_icms_inter_part == cenario_producao.p_icms_inter_part,
            CenarioDIFAL.ncm == cenario_producao.ncm  # Incluir NCM na comparação
        )