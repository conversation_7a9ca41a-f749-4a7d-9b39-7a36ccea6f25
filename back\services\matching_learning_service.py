"""
Serviço de Aprendizado para Matching de Produtos
Utiliza histórico de aprovações para melhorar matching futuro
"""

from typing import Dict, List, Optional, Tuple
from sqlalchemy import and_, or_
from models import db
from models.auditoria_comparativa_impostos import HistoricoMatchingAprendizado
from models.nota_fiscal_item import NotaFiscalItem
from models.item_nota_entrada import ItemNotaEntrada
from models.produto import Produto
from models.produto_entrada import ProdutoEntrada


class MatchingLearningService:
    def __init__(self, empresa_id: int, escritorio_id: int):
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id

    def consultar_match_aprendido(self, xml_codigo_produto: str, sped_codigo_produto: str, 
                                  cliente_id: Optional[int] = None) -> Optional[Dict]:
        """
        Consulta se existe um match aprendido para os códigos de produto
        
        Args:
            xml_codigo_produto: Código do produto no XML
            sped_codigo_produto: Código do produto no SPED
            cliente_id: ID do cliente (opcional, para aprendizado específico)
            
        Returns:
            Dict com informações do match aprendido ou None
        """
        query = db.session.query(HistoricoMatchingAprendizado).filter(
            HistoricoMatchingAprendizado.empresa_id == self.empresa_id,
            HistoricoMatchingAprendizado.xml_codigo_produto == xml_codigo_produto,
            HistoricoMatchingAprendizado.sped_codigo_produto == sped_codigo_produto,
            HistoricoMatchingAprendizado.acao_usuario == 'aprovado'
        )
        
        # Priorizar match específico do cliente
        if cliente_id:
            match_cliente = query.filter(
                HistoricoMatchingAprendizado.cliente_id == cliente_id
            ).first()
            
            if match_cliente:
                return {
                    'tipo': 'cliente_especifico',
                    'historico': match_cliente.to_dict(),
                    'confianca': 0.95
                }
        
        # Buscar match geral (qualquer cliente)
        match_geral = query.first()
        if match_geral:
            return {
                'tipo': 'geral',
                'historico': match_geral.to_dict(),
                'confianca': 0.85
            }
        
        return None

    def verificar_inconsistencia_aprendizado(self, xml_codigo_produto: str, 
                                           sped_codigo_produto: str,
                                           cliente_id: Optional[int] = None) -> Optional[Dict]:
        """
        Verifica se existe inconsistência no aprendizado
        (mesmo produto XML pareado com diferentes produtos SPED)
        """
        # Buscar todos os matches aprovados para o produto XML
        matches_xml = db.session.query(HistoricoMatchingAprendizado).filter(
            HistoricoMatchingAprendizado.empresa_id == self.empresa_id,
            HistoricoMatchingAprendizado.xml_codigo_produto == xml_codigo_produto,
            HistoricoMatchingAprendizado.acao_usuario == 'aprovado'
        ).all()
        
        if len(matches_xml) <= 1:
            return None
        
        # Verificar se há diferentes produtos SPED para o mesmo XML
        produtos_sped = set(m.sped_codigo_produto for m in matches_xml)
        
        if len(produtos_sped) > 1:
            return {
                'tipo': 'inconsistencia_xml',
                'xml_codigo': xml_codigo_produto,
                'produtos_sped': list(produtos_sped),
                'matches': [m.to_dict() for m in matches_xml]
            }
        
        return None

    def obter_sugestoes_similares(self, xml_codigo_produto: str, 
                                  cliente_id: Optional[int] = None) -> List[Dict]:
        """
        Obtém sugestões de produtos SPED similares baseado no histórico
        """
        # Buscar matches aprovados para produtos XML similares
        query = db.session.query(HistoricoMatchingAprendizado).filter(
            HistoricoMatchingAprendizado.empresa_id == self.empresa_id,
            HistoricoMatchingAprendizado.acao_usuario == 'aprovado'
        )
        
        if cliente_id:
            query = query.filter(HistoricoMatchingAprendizado.cliente_id == cliente_id)
        
        # Buscar por códigos similares (primeiros caracteres)
        codigo_base = xml_codigo_produto[:6] if len(xml_codigo_produto) >= 6 else xml_codigo_produto
        
        matches_similares = query.filter(
            HistoricoMatchingAprendizado.xml_codigo_produto.like(f'{codigo_base}%')
        ).limit(10).all()
        
        sugestoes = []
        for match in matches_similares:
            sugestoes.append({
                'sped_codigo_produto': match.sped_codigo_produto,
                'xml_codigo_produto': match.xml_codigo_produto,
                'cliente_id': match.cliente_id,
                'data_aprendizado': match.data_acao.isoformat() if match.data_acao else None,
                'similaridade': self._calcular_similaridade_codigo(xml_codigo_produto, match.xml_codigo_produto)
            })
        
        # Ordenar por similaridade
        sugestoes.sort(key=lambda x: x['similaridade'], reverse=True)
        
        return sugestoes

    def aplicar_aprendizado_automatico(self, itens_xml: List[Dict], 
                                     itens_sped: List[Dict]) -> List[Dict]:
        """
        Aplica aprendizado automático para sugerir matches
        
        Args:
            itens_xml: Lista de itens XML
            itens_sped: Lista de itens SPED
            
        Returns:
            Lista de matches sugeridos pelo aprendizado
        """
        matches_aprendidos = []
        
        for xml_item in itens_xml:
            xml_codigo = xml_item.get('codigo_produto')
            cliente_id = xml_item.get('cliente_id')
            
            if not xml_codigo:
                continue
            
            # Buscar match exato aprendido
            for sped_item in itens_sped:
                sped_codigo = sped_item.get('cod_item')
                
                if not sped_codigo:
                    continue
                
                match_aprendido = self.consultar_match_aprendido(
                    xml_codigo, sped_codigo, cliente_id
                )
                
                if match_aprendido:
                    matches_aprendidos.append({
                        'xml_item': xml_item,
                        'sped_item': sped_item,
                        'match_type': 'learned',
                        'confidence_score': match_aprendido['confianca'],
                        'details': {
                            'method': 'Aprendizado automático',
                            'tipo_aprendizado': match_aprendido['tipo'],
                            'historico_id': match_aprendido['historico']['id']
                        }
                    })
                    break
        
        return matches_aprendidos

    def _calcular_similaridade_codigo(self, codigo1: str, codigo2: str) -> float:
        """
        Calcula similaridade entre dois códigos de produto
        """
        if not codigo1 or not codigo2:
            return 0.0
        
        if codigo1 == codigo2:
            return 1.0
        
        # Similaridade baseada em caracteres comuns no início
        min_len = min(len(codigo1), len(codigo2))
        chars_comuns = 0
        
        for i in range(min_len):
            if codigo1[i] == codigo2[i]:
                chars_comuns += 1
            else:
                break
        
        return chars_comuns / max(len(codigo1), len(codigo2))

    def obter_estatisticas_aprendizado(self) -> Dict:
        """
        Obtém estatísticas do aprendizado para a empresa
        """
        total_matches = db.session.query(HistoricoMatchingAprendizado).filter(
            HistoricoMatchingAprendizado.empresa_id == self.empresa_id
        ).count()
        
        aprovados = db.session.query(HistoricoMatchingAprendizado).filter(
            HistoricoMatchingAprendizado.empresa_id == self.empresa_id,
            HistoricoMatchingAprendizado.acao_usuario == 'aprovado'
        ).count()
        
        rejeitados = db.session.query(HistoricoMatchingAprendizado).filter(
            HistoricoMatchingAprendizado.empresa_id == self.empresa_id,
            HistoricoMatchingAprendizado.acao_usuario == 'rejeitado'
        ).count()
        
        produtos_unicos = db.session.query(
            HistoricoMatchingAprendizado.xml_codigo_produto
        ).filter(
            HistoricoMatchingAprendizado.empresa_id == self.empresa_id,
            HistoricoMatchingAprendizado.acao_usuario == 'aprovado'
        ).distinct().count()
        
        return {
            'total_matches': total_matches,
            'aprovados': aprovados,
            'rejeitados': rejeitados,
            'produtos_unicos_aprendidos': produtos_unicos,
            'taxa_aprovacao': (aprovados / total_matches * 100) if total_matches > 0 else 0
        }
