"""
Gerenciador de transações para controle de concorrência e isolamento
"""
import threading
import time
from contextlib import contextmanager
from typing import Dict, Set, Optional, Any
from sqlalchemy import text
from sqlalchemy.exc import OperationalError, IntegrityError
from models import db

class TransactionManager:
    """
    Gerenciador de transações com controle de locks e deadlock detection
    """
    
    def __init__(self):
        # Controle de locks por tabela/empresa
        self.table_locks: Dict[str, threading.RLock] = {}
        self.company_locks: Dict[int, threading.RLock] = {}
        self.lock_registry = threading.Lock()
        
        # Controle de transações ativas
        self.active_transactions: Dict[str, Dict[str, Any]] = {}
        self.transaction_lock = threading.Lock()
        
        # Estatísticas
        self.stats = {
            'total_transactions': 0,
            'successful_transactions': 0,
            'failed_transactions': 0,
            'deadlocks_detected': 0,
            'retries_performed': 0
        }

    def _get_table_lock(self, table_name: str) -> threading.RLock:
        """Obtém ou cria um lock para uma tabela específica"""
        with self.lock_registry:
            if table_name not in self.table_locks:
                self.table_locks[table_name] = threading.RLock()
            return self.table_locks[table_name]

    def _get_company_lock(self, empresa_id: int) -> threading.RLock:
        """Obtém ou cria um lock para uma empresa específica"""
        with self.lock_registry:
            if empresa_id not in self.company_locks:
                self.company_locks[empresa_id] = threading.RLock()
            return self.company_locks[empresa_id]

    @contextmanager
    def transaction(self, 
                   empresa_id: Optional[int] = None,
                   tables: Optional[Set[str]] = None,
                   isolation_level: str = 'READ_COMMITTED',
                   timeout: int = 30,
                   max_retries: int = 3):
        """
        Context manager para transações com controle de concorrência
        
        Args:
            empresa_id: ID da empresa (para lock por empresa)
            tables: Conjunto de tabelas que serão modificadas
            isolation_level: Nível de isolamento da transação
            timeout: Timeout em segundos
            max_retries: Número máximo de tentativas em caso de deadlock
        """
        transaction_id = f"{threading.current_thread().ident}_{time.time()}"
        
        # Registrar transação ativa
        with self.transaction_lock:
            self.active_transactions[transaction_id] = {
                'empresa_id': empresa_id,
                'tables': tables or set(),
                'started_at': time.time(),
                'thread_id': threading.current_thread().ident
            }
            self.stats['total_transactions'] += 1

        # Obter locks necessários
        acquired_locks = []
        
        try:
            # Lock por empresa (se especificado)
            if empresa_id:
                company_lock = self._get_company_lock(empresa_id)
                if company_lock.acquire(timeout=timeout):
                    acquired_locks.append(('company', empresa_id, company_lock))
                else:
                    raise TimeoutError(f"Timeout ao obter lock da empresa {empresa_id}")

            # Locks por tabela (ordenados para evitar deadlocks)
            if tables:
                sorted_tables = sorted(tables)  # Ordem consistente para evitar deadlocks
                for table_name in sorted_tables:
                    table_lock = self._get_table_lock(table_name)
                    if table_lock.acquire(timeout=timeout):
                        acquired_locks.append(('table', table_name, table_lock))
                    else:
                        raise TimeoutError(f"Timeout ao obter lock da tabela {table_name}")

            # Executar transação com retry em caso de deadlock
            for attempt in range(max_retries + 1):
                try:
                    with db.session.begin():
                        # Configurar nível de isolamento
                        if isolation_level != 'READ_COMMITTED':
                            db.session.execute(text(f"SET TRANSACTION ISOLATION LEVEL {isolation_level}"))
                        
                        # Configurar timeout da transação
                        db.session.execute(text(f"SET statement_timeout = '{timeout}s'"))
                        
                        
                        yield db.session
                        
                        
                        with self.transaction_lock:
                            self.stats['successful_transactions'] += 1
                        
                        break  # Sucesso, sair do loop de retry
                        
                except (OperationalError, IntegrityError) as e:
                    error_msg = str(e).lower()
                    
                    # Detectar deadlock
                    if 'deadlock' in error_msg or 'lock timeout' in error_msg:
                        with self.transaction_lock:
                            self.stats['deadlocks_detected'] += 1
                        
                        if attempt < max_retries:
                            with self.transaction_lock:
                                self.stats['retries_performed'] += 1
                            
                            # Backoff exponencial
                            wait_time = (2 ** attempt) * 0.1
                            time.sleep(wait_time)
                            continue
                        else:
                            raise
                    else:
                        # Outro tipo de erro, não tentar novamente
                        raise

        except Exception as e:
            with self.transaction_lock:
                self.stats['failed_transactions'] += 1
            raise
            
        finally:
            # Liberar todos os locks na ordem inversa
            for lock_type, lock_id, lock_obj in reversed(acquired_locks):
                try:
                    lock_obj.release()
                except Exception as e:
                    print(f"Erro ao liberar {lock_type} lock {lock_id}: {str(e)}")
            
            # Remover transação do registro
            with self.transaction_lock:
                self.active_transactions.pop(transaction_id, None)

    @contextmanager
    def bulk_operation(self, empresa_id: int, table_name: str, batch_size: int = 1000):
        """
        Context manager para operações em lote com controle de memória
        
        Args:
            empresa_id: ID da empresa
            table_name: Nome da tabela principal
            batch_size: Tamanho do lote para commit
        """
        with self.transaction(
            empresa_id=empresa_id,
            tables={table_name},
            isolation_level='READ_COMMITTED',
            timeout=300  # 5 minutos para operações em lote
        ) as session:
            
            class BatchProcessor:
                def __init__(self, session, batch_size):
                    self.session = session
                    self.batch_size = batch_size
                    self.current_batch = 0
                    self.total_processed = 0
                
                def add(self, obj):
                    """Adiciona um objeto ao lote"""
                    self.session.add(obj)
                    self.current_batch += 1
                    self.total_processed += 1
                    
                    if self.current_batch >= self.batch_size:
                        self.flush()
                
                def flush(self):
                    """Força o flush do lote atual"""
                    if self.current_batch > 0:
                        self.session.flush()
                        self.current_batch = 0
                
                def finish(self):
                    """Finaliza o processamento"""
                    self.flush()
            
            yield BatchProcessor(session, batch_size)

    def get_active_transactions(self) -> Dict[str, Dict[str, Any]]:
        """Retorna informações sobre transações ativas"""
        with self.transaction_lock:
            current_time = time.time()
            active = {}
            
            for tx_id, tx_info in self.active_transactions.items():
                duration = current_time - tx_info['started_at']
                active[tx_id] = {
                    **tx_info,
                    'duration_seconds': duration
                }
            
            return active

    def get_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do gerenciador de transações"""
        with self.transaction_lock:
            active_count = len(self.active_transactions)
            
            return {
                **self.stats,
                'active_transactions': active_count,
                'success_rate': (
                    self.stats['successful_transactions'] / max(1, self.stats['total_transactions'])
                ) * 100
            }

    def detect_long_running_transactions(self, max_duration: int = 300) -> Dict[str, Dict[str, Any]]:
        """
        Detecta transações que estão rodando há muito tempo
        
        Args:
            max_duration: Duração máxima em segundos
            
        Returns:
            Dict com transações de longa duração
        """
        current_time = time.time()
        long_running = {}
        
        with self.transaction_lock:
            for tx_id, tx_info in self.active_transactions.items():
                duration = current_time - tx_info['started_at']
                if duration > max_duration:
                    long_running[tx_id] = {
                        **tx_info,
                        'duration_seconds': duration
                    }
        
        if long_running:
            print(f"Transações de longa duração detectadas: {long_running}")
        
        return long_running

    def force_cleanup_old_locks(self, max_age: int = 3600):
        """
        Força a limpeza de locks antigos (uso em emergência)
        
        Args:
            max_age: Idade máxima em segundos
        """
        current_time = time.time()
        cleaned = 0
        
        with self.transaction_lock:
            to_remove = []
            for tx_id, tx_info in self.active_transactions.items():
                if current_time - tx_info['started_at'] > max_age:
                    to_remove.append(tx_id)
            
            for tx_id in to_remove:
                del self.active_transactions[tx_id]
                cleaned += 1
        
        if cleaned > 0:
            print(f"Limpeza forçada: {cleaned} transações antigos foram removidas")
        
        return cleaned

# Instância global do gerenciador de transações
_transaction_manager = None

def get_transaction_manager() -> TransactionManager:
    """Obtém a instância global do gerenciador de transações"""
    global _transaction_manager
    if _transaction_manager is None:
        _transaction_manager = TransactionManager()
    return _transaction_manager

def init_transaction_manager() -> TransactionManager:
    """Inicializa o gerenciador de transações"""
    global _transaction_manager
    _transaction_manager = TransactionManager()
    return _transaction_manager
