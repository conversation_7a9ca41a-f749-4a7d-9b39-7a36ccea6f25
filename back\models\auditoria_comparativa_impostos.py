"""
Modelo para Auditoria Comparativa de Impostos
Armazena comparações detalhadas entre XML e SPED por tributo
"""

from sqlalchemy import func
from models import db

class AuditoriaComparativaImpostos(db.Model):
    __tablename__ = 'auditoria_comparativa_impostos'
    
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.Integer, db.Foreign<PERSON>ey('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.<PERSON><PERSON>, db.<PERSON>ey('escritorio.id'), nullable=False)
    
    # Identificação da nota
    chave_nf = db.Column(db.String(44), nullable=False)
    numero_nf = db.Column(db.String(20), nullable=False)
    
    # IDs dos itens relacionados
    xml_item_id = db.<PERSON>umn(db.<PERSON><PERSON>, db.<PERSON><PERSON><PERSON>('nota_fiscal_item.id'))
    sped_item_id = db.Column(db.Integer, db.<PERSON>('item_nota_entrada.id'))
    cliente_id = db.Column(db.Integer, db.ForeignKey('cliente.id'))  # ID do cliente da nota fiscal
    
    # Informações do matching
    match_type = db.Column(db.String(20))  # 'direct', 'embedding', 'manual', 'unmatched_xml', 'unmatched_sped'
    confidence_score = db.Column(db.Numeric(6, 4))  # Score de confiança do match (0-99.9999)
    match_details = db.Column(db.JSON)  # Detalhes do algoritmo de matching
    
    # Status geral da auditoria
    status_auditoria = db.Column(db.String(20), default='pendente')  # pendente, em_analise, aprovado, rejeitado
    data_auditoria = db.Column(db.DateTime, server_default=func.now())
    usuario_auditoria = db.Column(db.Integer, db.ForeignKey('usuario.id'))
    
    # === DADOS XML ===
    xml_descricao = db.Column(db.String(255))
    xml_quantidade = db.Column(db.Numeric(15, 4))
    xml_valor_unitario = db.Column(db.Numeric(15, 4))
    xml_valor_total = db.Column(db.Numeric(15, 2))
    xml_unidade = db.Column(db.String(10))
    xml_ncm = db.Column(db.String(20))
    xml_cfop = db.Column(db.String(10))

    # Dados tributários XML
    xml_icms_origem = db.Column(db.String(2))
    xml_icms_cst = db.Column(db.String(3))
    xml_icms_csosn = db.Column(db.String(3))
    xml_icms_bc = db.Column(db.Numeric(15, 2))
    xml_icms_aliquota = db.Column(db.Numeric(5, 2))
    xml_ipi_valor = db.Column(db.Numeric(15, 2))
    xml_ipi_bc = db.Column(db.Numeric(15, 2))
    xml_ipi_aliquota = db.Column(db.Numeric(5, 2))
    xml_ipi_cst = db.Column(db.String(2))
    xml_pis_valor = db.Column(db.Numeric(15, 2))
    xml_pis_bc = db.Column(db.Numeric(15, 2))
    xml_pis_aliquota = db.Column(db.Numeric(5, 4))
    xml_pis_cst = db.Column(db.String(2))
    xml_cofins_valor = db.Column(db.Numeric(15, 2))
    xml_cofins_bc = db.Column(db.Numeric(15, 2))
    xml_cofins_aliquota = db.Column(db.Numeric(5, 4))
    xml_cofins_cst = db.Column(db.String(2))
    # ICMS-ST XML
    xml_icms_st_valor = db.Column(db.Numeric(15, 2))
    xml_icms_st_bc = db.Column(db.Numeric(15, 2))
    xml_icms_st_aliquota = db.Column(db.Numeric(5, 2))
    xml_icms_st_cst = db.Column(db.String(3))

    # === DADOS SPED ===
    sped_descricao = db.Column(db.String(255))
    sped_quantidade = db.Column(db.Numeric(15, 4))
    sped_valor_item = db.Column(db.Numeric(15, 2))
    sped_unidade = db.Column(db.String(10))
    sped_cfop = db.Column(db.String(10))
    sped_cod_item = db.Column(db.String(50))
    sped_ncm = db.Column(db.String(20))  # NCM do SPED
    sped_icms_origem = db.Column(db.String(2))  # Origem ICMS do SPED
    sped_tipo_item = db.Column(db.String(2))  # Tipo do item do SPED

    # === ICMS ===
    # XML (limitado)
    xml_icms_valor = db.Column(db.Numeric(15, 2))

    # SPED (completo)
    sped_cst_icms = db.Column(db.String(3))
    sped_icms_bc = db.Column(db.Numeric(15, 2))
    sped_icms_aliquota = db.Column(db.Numeric(5, 2))
    sped_icms_valor = db.Column(db.Numeric(15, 2))
    sped_icms_reducao = db.Column(db.Numeric(5, 4))
    sped_icms_csosn = db.Column(db.String(3))  # CSOSN para Simples Nacional
    sped_icms_credito = db.Column(db.Numeric(5, 4))  # % Crédito ICMS Simples Nacional
    
    # Status e observações ICMS
    status_icms = db.Column(db.String(20), default='pendente')  # conforme, divergente, pendente, aprovado
    obs_icms = db.Column(db.Text)
    
    # === ICMS-ST ===
    # SPED
    sped_icms_st_bc = db.Column(db.Numeric(15, 2))
    sped_icms_st_aliquota = db.Column(db.Numeric(5, 2))
    sped_icms_st_valor = db.Column(db.Numeric(15, 2))
    sped_icms_st_mva = db.Column(db.Numeric(5, 4))
    sped_icms_st_reducao = db.Column(db.Numeric(5, 4))
    
    # Status e observações ICMS-ST
    status_icms_st = db.Column(db.String(20), default='pendente')
    obs_icms_st = db.Column(db.Text)
    
    # === IPI ===
    # SPED
    sped_cst_ipi = db.Column(db.String(2))
    sped_ipi_bc = db.Column(db.Numeric(15, 2))
    sped_ipi_aliquota = db.Column(db.Numeric(5, 2))
    sped_ipi_valor = db.Column(db.Numeric(15, 2))
    sped_ipi_reducao = db.Column(db.Numeric(5, 4))
    
    # Status e observações IPI
    status_ipi = db.Column(db.String(20), default='pendente')
    obs_ipi = db.Column(db.Text)
    
    # === PIS ===
    # SPED
    sped_cst_pis = db.Column(db.String(2))
    sped_pis_bc = db.Column(db.Numeric(15, 2))
    sped_pis_aliquota = db.Column(db.Numeric(5, 4))
    sped_pis_valor = db.Column(db.Numeric(15, 2))
    
    # Status e observações PIS
    status_pis = db.Column(db.String(20), default='pendente')
    obs_pis = db.Column(db.Text)
    
    # === COFINS ===
    # SPED
    sped_cst_cofins = db.Column(db.String(2))
    sped_cofins_bc = db.Column(db.Numeric(15, 2))
    sped_cofins_aliquota = db.Column(db.Numeric(5, 4))
    sped_cofins_valor = db.Column(db.Numeric(15, 2))
    
    # Status e observações COFINS
    status_cofins = db.Column(db.String(20), default='pendente')
    obs_cofins = db.Column(db.Text)
    
    # === CAMPOS DE CONTROLE ===
    data_criacao = db.Column(db.DateTime, server_default=func.now())
    data_atualizacao = db.Column(db.DateTime, server_default=func.now(), onupdate=func.now())
    usuario_criacao = db.Column(db.Integer, db.ForeignKey('usuario.id'))
    usuario_atualizacao = db.Column(db.Integer, db.ForeignKey('usuario.id'))
    
    # Campos para rastreamento de alterações nos dados SPED
    sped_alterado = db.Column(db.Boolean, default=False)
    historico_alteracoes = db.Column(db.JSON)  # Log de alterações feitas
    
    def __repr__(self):
        return f"<AuditoriaComparativaImpostos {self.chave_nf} - Item {self.xml_item_id}/{self.sped_item_id}>"

    def get_status_tributo(self, tributo):
        """Obtém status de um tributo específico"""
        status_map = {
            'icms': self.status_icms,
            'icms_st': self.status_icms_st,
            'ipi': self.status_ipi,
            'pis': self.status_pis,
            'cofins': self.status_cofins
        }
        return status_map.get(tributo, 'pendente')

    def set_status_tributo(self, tributo, status, usuario_id, feedback=None):
        """Define status de um tributo específico"""
        if tributo == 'icms':
            self.status_icms = status
            if feedback:
                self.obs_icms = feedback
        elif tributo == 'icms_st':
            self.status_icms_st = status
            if feedback:
                self.obs_icms_st = feedback
        elif tributo == 'ipi':
            self.status_ipi = status
            if feedback:
                self.obs_ipi = feedback
        elif tributo == 'pis':
            self.status_pis = status
            if feedback:
                self.obs_pis = feedback
        elif tributo == 'cofins':
            self.status_cofins = status
            if feedback:
                self.obs_cofins = feedback

        # Atualizar campos de controle
        self.usuario_atualizacao = usuario_id
        self.data_atualizacao = func.now()

    def is_tributo_aprovado(self, tributo):
        """Verifica se um tributo foi aprovado"""
        return self.get_status_tributo(tributo) == 'aprovado'
    
    def to_dict(self):
        """Converte o modelo para dicionário"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'chave_nf': self.chave_nf,
            'numero_nf': self.numero_nf,
            'xml_item_id': self.xml_item_id,
            'sped_item_id': self.sped_item_id,
            'cliente_id': self.cliente_id,
            'match_type': self.match_type,
            'confidence_score': float(self.confidence_score) if self.confidence_score else None,
            'match_details': self.match_details,
            'status_auditoria': self.status_auditoria,
            'data_auditoria': self.data_auditoria.isoformat() if self.data_auditoria else None,
            
            # Dados XML
            'xml_data': {
                'descricao': self.xml_descricao,
                'quantidade': float(self.xml_quantidade) if self.xml_quantidade else None,
                'valor_unitario': float(self.xml_valor_unitario) if self.xml_valor_unitario else None,
                'valor_total': float(self.xml_valor_total) if self.xml_valor_total else None,
                'unidade': self.xml_unidade,
                'ncm': self.xml_ncm,
                'cfop': self.xml_cfop,
                'origem': self.xml_icms_origem,
                'cst': self.xml_icms_cst,
                'csosn': self.xml_icms_csosn,
                # Valores tributários XML
                'icms_valor': float(self.xml_icms_valor) if self.xml_icms_valor else None,
                'icms_bc': float(self.xml_icms_bc) if self.xml_icms_bc else None,
                'icms_aliquota': float(self.xml_icms_aliquota) if self.xml_icms_aliquota else None,
                'ipi_valor': float(self.xml_ipi_valor) if self.xml_ipi_valor else None,
                'ipi_bc': float(self.xml_ipi_bc) if self.xml_ipi_bc else None,
                'ipi_aliquota': float(self.xml_ipi_aliquota) if self.xml_ipi_aliquota else None,
                'ipi_cst': self.xml_ipi_cst,
                'pis_valor': float(self.xml_pis_valor) if self.xml_pis_valor else None,
                'pis_bc': float(self.xml_pis_bc) if self.xml_pis_bc else None,
                'pis_aliquota': float(self.xml_pis_aliquota) if self.xml_pis_aliquota else None,
                'pis_cst': self.xml_pis_cst,
                'cofins_valor': float(self.xml_cofins_valor) if self.xml_cofins_valor else None,
                'cofins_bc': float(self.xml_cofins_bc) if self.xml_cofins_bc else None,
                'cofins_aliquota': float(self.xml_cofins_aliquota) if self.xml_cofins_aliquota else None,
                'cofins_cst': self.xml_cofins_cst,
                # ICMS-ST XML
                'icms_st_valor': float(self.xml_icms_st_valor) if self.xml_icms_st_valor else None,
                'icms_st_bc': float(self.xml_icms_st_bc) if self.xml_icms_st_bc else None,
                'icms_st_aliquota': float(self.xml_icms_st_aliquota) if self.xml_icms_st_aliquota else None,
                'icms_st_cst': self.xml_icms_st_cst
            },

            # Dados SPED
            'sped_data': {
                'descricao': self.sped_descricao,
                'quantidade': float(self.sped_quantidade) if self.sped_quantidade else None,
                'valor_item': float(self.sped_valor_item) if self.sped_valor_item else None,
                'unidade': self.sped_unidade,
                'cfop': self.sped_cfop,
                'cod_item': self.sped_cod_item,
                'ncm': self.sped_ncm,
                'origem_icms': self.sped_icms_origem,
                'tipo_item': self.sped_tipo_item
            },

            # Campos diretos para facilitar acesso no frontend
            'xml_descricao': self.xml_descricao,
            'xml_ncm': self.xml_ncm,
            'xml_cfop': self.xml_cfop,
            'xml_origem': self.xml_icms_origem,
            'xml_cst': self.xml_icms_cst,
            'xml_csosn': self.xml_icms_csosn,
            'sped_descricao': self.sped_descricao,
            'sped_cfop': self.sped_cfop,
            'sped_cod_item': self.sped_cod_item,
            'sped_ncm': self.sped_ncm,
            
            # Tributos
            'tributos': {
                'icms': {
                    'status': self.status_icms,
                    'cst': self.sped_cst_icms,
                    'bc': float(self.sped_icms_bc) if self.sped_icms_bc else None,
                    'aliquota': float(self.sped_icms_aliquota) if self.sped_icms_aliquota else None,
                    'valor': float(self.sped_icms_valor) if self.sped_icms_valor else None,
                    'reducao': float(self.sped_icms_reducao) if self.sped_icms_reducao else None,
                    'csosn': self.sped_icms_csosn,
                    'credito_icms': float(self.sped_icms_credito) if self.sped_icms_credito else None,
                    'origem_icms': self.sped_icms_origem,
                    'cfop': self.sped_cfop,
                    'ncm': self.sped_ncm,
                    'tipo_item': self.sped_tipo_item,
                    'obs': self.obs_icms
                },
                'icms_st': {
                    'status': self.status_icms_st,
                    'bc': float(self.sped_icms_st_bc) if self.sped_icms_st_bc else None,
                    'aliquota': float(self.sped_icms_st_aliquota) if self.sped_icms_st_aliquota else None,
                    'valor': float(self.sped_icms_st_valor) if self.sped_icms_st_valor else None,
                    'mva': float(self.sped_icms_st_mva) if self.sped_icms_st_mva else None,
                    'reducao': float(self.sped_icms_st_reducao) if self.sped_icms_st_reducao else None,
                    'cst': self.sped_cst_icms,  # CST ICMS para ICMS-ST
                    'cfop': self.sped_cfop,
                    'ncm': self.sped_ncm,
                    'obs': self.obs_icms_st
                },
                'ipi': {
                    'status': self.status_ipi,
                    'cst': self.sped_cst_ipi,
                    'bc': float(self.sped_ipi_bc) if self.sped_ipi_bc else None,
                    'aliquota': float(self.sped_ipi_aliquota) if self.sped_ipi_aliquota else None,
                    'valor': float(self.sped_ipi_valor) if self.sped_ipi_valor else None,
                    'reducao': float(self.sped_ipi_reducao) if self.sped_ipi_reducao else None,
                    'cfop': self.sped_cfop,
                    'ncm': self.sped_ncm,
                    'obs': self.obs_ipi
                },
                'pis': {
                    'status': self.status_pis,
                    'cst': self.sped_cst_pis,
                    'bc': float(self.sped_pis_bc) if self.sped_pis_bc else None,
                    'aliquota': float(self.sped_pis_aliquota) if self.sped_pis_aliquota else None,
                    'valor': float(self.sped_pis_valor) if self.sped_pis_valor else None,
                    'cfop': self.sped_cfop,
                    'ncm': self.sped_ncm,
                    'obs': self.obs_pis
                },
                'cofins': {
                    'status': self.status_cofins,
                    'cst': self.sped_cst_cofins,
                    'bc': float(self.sped_cofins_bc) if self.sped_cofins_bc else None,
                    'aliquota': float(self.sped_cofins_aliquota) if self.sped_cofins_aliquota else None,
                    'valor': float(self.sped_cofins_valor) if self.sped_cofins_valor else None,
                    'cfop': self.sped_cfop,
                    'ncm': self.sped_ncm,
                    'obs': self.obs_cofins
                }
            },
            
            'sped_alterado': self.sped_alterado,
            'historico_alteracoes': self.historico_alteracoes,
            'data_criacao': self.data_criacao.isoformat() if self.data_criacao else None,
            'data_atualizacao': self.data_atualizacao.isoformat() if self.data_atualizacao else None
        }

class HistoricoMatchingAprendizado(db.Model):
    """
    Armazena histórico de matches aprovados/rejeitados pelo usuário
    para melhorar o algoritmo de matching
    """
    __tablename__ = 'historico_matching_aprendizado'

    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.Integer, db.ForeignKey('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.Integer, db.ForeignKey('escritorio.id'), nullable=False)

    # IDs dos itens
    xml_item_id = db.Column(db.Integer, db.ForeignKey('nota_fiscal_item.id'), nullable=False)
    sped_item_id = db.Column(db.Integer, db.ForeignKey('item_nota_entrada.id'), nullable=False)
    cliente_id = db.Column(db.Integer, db.ForeignKey('cliente.id'))  # ID do cliente para aprendizado

    # Códigos dos produtos para aprendizado futuro
    xml_codigo_produto = db.Column(db.String(50))  # Código do produto no XML
    sped_codigo_produto = db.Column(db.String(50))  # Código do produto no SPED

    # Dados do matching original
    match_type_original = db.Column(db.String(20))
    confidence_score_original = db.Column(db.Numeric(6, 4))

    # Ação do usuário
    acao_usuario = db.Column(db.String(20), nullable=False)  # 'aprovado', 'rejeitado', 'corrigido'
    usuario_id = db.Column(db.Integer, db.ForeignKey('usuario.id'), nullable=False)
    data_acao = db.Column(db.DateTime, server_default=func.now())

    # Características dos itens para aprendizado
    caracteristicas_match = db.Column(db.JSON)  # Características que levaram ao match
    feedback_usuario = db.Column(db.Text)  # Comentário opcional do usuário

    def __repr__(self):
        return f"<HistoricoMatchingAprendizado {self.xml_item_id}/{self.sped_item_id} - {self.acao_usuario}>"

    def to_dict(self):
        """Converte o modelo para dicionário"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'xml_item_id': self.xml_item_id,
            'sped_item_id': self.sped_item_id,
            'cliente_id': self.cliente_id,
            'xml_codigo_produto': self.xml_codigo_produto,
            'sped_codigo_produto': self.sped_codigo_produto,
            'match_type_original': self.match_type_original,
            'confidence_score_original': float(self.confidence_score_original) if self.confidence_score_original else None,
            'acao_usuario': self.acao_usuario,
            'usuario_id': self.usuario_id,
            'data_acao': self.data_acao.isoformat() if self.data_acao else None,
            'caracteristicas_match': self.caracteristicas_match,
            'feedback_usuario': self.feedback_usuario
        }
