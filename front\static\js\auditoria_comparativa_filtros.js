/**
 * Componente de filtros dropdown com checkboxes para auditoria comparativa
 * Implementa filtros cascata onde a seleção de um filtro afeta as opções dos outros
 */

// Variáveis globais para armazenar dados dos filtros
/**
 * Mapeia os valores de match_types para rótulos amigáveis
 * @param {string} value - Valor do tipo de match
 * @returns {string} Rótulo amigável
 */
function getMatchTypeLabel(value) {
  if (!value) return value;
  
  // Converter para minúsculas para comparação case-insensitive
  const normalizedValue = value.toLowerCase();
  
  // Mapeia todos os valores possíveis de match_type para seus rótulos
  const labels = {
    'direct': 'Direto',
    'ia': 'IA',
    'sped_without_match': 'SPED sem par',
    'rejected': 'Rejeitado',
    'pending': 'Pendente',
    'embedding': 'IA',
    'unmatched_sped': 'SPED sem par',
    'manual': 'Manual',
    'unmatched_xml': 'XML sem par',
    // Adiciona mapeamento para valores que podem vir do banco de dados
    'sped sem par': 'SPED sem par',
    'xml sem par': 'XML sem par'
  };
  
  // Retorna o rótulo correspondente ou o próprio valor se não encontrar correspondência
  return labels[normalizedValue] || value;
}

window.auditoriaComparativaDropdownFilters = {
  opcoes: {},
  filtrosSelecionados: {
    parceiros: [],
    cfops_sped: [],
    cfops_xml: [],
    csts_sped: [],
    csts_xml: [],
    ncms_sped: [],
    ncms_xml: [],
    origens_sped: [],
    origens_xml: [],
    aliquotas_sped: [],
    aliquotas_xml: [],
    status: [],
    match_types: [],
    regimes: [],
    tipos: [],
    csosns: [],
    reducoes: [],
  },
  isLoading: false,
};

/**
 * Carrega as opções de filtros do backend
 * @param {number} empresaId - ID da empresa
 * @param {number} mes - Mês
 * @param {number} ano - Ano
 * @param {string} tributo - Tributo (icms, icms_st, ipi, pis, cofins)
 */
async function carregarOpcoesFiltrosAuditoriaComparativa(
  empresaId,
  mes,
  ano,
  tributo,
) {
  if (window.auditoriaComparativaDropdownFilters.isLoading) {
    return;
  }

  window.auditoriaComparativaDropdownFilters.isLoading = true;

  try {
    const url = new URL('/api/auditoria-comparativa/filter-options', window.location.origin);
    url.searchParams.append('empresa_id', empresaId);
    url.searchParams.append('mes', mes);
    url.searchParams.append('ano', ano);
    if (tributo) {
      url.searchParams.append('tributo', tributo);
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success) {
      window.auditoriaComparativaDropdownFilters.opcoes = data.opcoes;
      console.log('Opções de filtros de auditoria comparativa carregadas:', data.opcoes);
    } else {
      throw new Error(data.message || 'Erro ao carregar opções de filtros');
    }
  } catch (error) {
    console.error('Erro ao carregar opções de filtros:', error);
    // Em caso de erro, usar opções vazias
    window.auditoriaComparativaDropdownFilters.opcoes = {
      parceiros: [],
      cfops_sped: [],
      cfops_xml: [],
      csts_sped: [],
      csts_xml: [],
      ncms_sped: [],
      ncms_xml: [],
      origens_sped: [],
      origens_xml: [],
      aliquotas_sped: [],
      aliquotas_xml: [],
      status: [],
      match_types: [],
      regimes: [],
      tipos: [],
      csosns: [],
      reducoes: [],
    };
  } finally {
    window.auditoriaComparativaDropdownFilters.isLoading = false;
  }
}

/**
 * Cria HTML do dropdown de filtro
 * @param {string} tipo - Tipo do filtro
 * @param {string} placeholder - Placeholder do input
 * @param {string} columnName - Nome da coluna
 * @param {number} originalIndex - Índice original da coluna
 */
function criarDropdownFiltroAuditoriaComparativa(
  tipo,
  placeholder,
  columnName,
  originalIndex,
) {
  const inputId = `filter-${tipo}-${originalIndex}`;
  const dropdownId = `dropdown-${tipo}-${originalIndex}`;

  return `
    <div class="dropdown w-100">
      <input type="text"
             class="form-control form-control-sm dropdown-toggle column-filter-dropdown"
             id="${inputId}"
             data-bs-toggle="dropdown"
             data-bs-auto-close="outside"
             data-column="${originalIndex}"
             data-column-name="${columnName}"
             data-original-index="${originalIndex}"
             data-filter-type="${tipo}"
             placeholder="${placeholder}"
             readonly
             style="cursor: pointer; font-size: 0.8rem;">

      <div class="dropdown-menu p-2" id="${dropdownId}" style="min-width: 250px; max-height: 300px; overflow-y: auto;">
        <div class="mb-2">
          <input type="text"
                 class="form-control form-control-sm"
                 id="busca-${tipo}-${originalIndex}"
                 placeholder="Buscar ${tipo}..."
                 onkeyup="filtrarOpcoesDropdownAuditoriaComparativa('${tipo}', ${originalIndex})">
        </div>

        <div class="mb-2">
          <div class="d-flex justify-content-between">
            <button type="button"
                    class="btn btn-sm btn-outline-primary"
                    onclick="selecionarTodosDropdownAuditoriaComparativa('${tipo}', ${originalIndex})">
              Todos
            </button>
            <button type="button"
                    class="btn btn-sm btn-outline-secondary"
                    onclick="limparSelecaoDropdownAuditoriaComparativa('${tipo}', ${originalIndex})">
              Limpar
            </button>
          </div>
        </div>

        <div id="container-${tipo}-${originalIndex}">
          <!-- Opções serão preenchidas dinamicamente -->
        </div>

        <div class="mt-2">
          <button type="button"
                  class="btn btn-sm btn-primary w-100"
                  onclick="aplicarFiltroDropdownAuditoriaComparativa('${tipo}', ${originalIndex})">
            Aplicar
          </button>
        </div>
      </div>
    </div>
  `;
}

/**
 * Popula dropdown com opções
 * @param {string} tipo - Tipo do filtro
 * @param {number} originalIndex - Índice original da coluna
 */
function popularDropdownAuditoriaComparativa(tipo, originalIndex) {
  const container = document.getElementById(`container-${tipo}-${originalIndex}`);
  if (!container) return;

  const opcoes = window.auditoriaComparativaDropdownFilters.opcoes[tipo] || [];
  const filtrosSelecionados = window.auditoriaComparativaDropdownFilters.filtrosSelecionados;

  // Limpar container
  container.innerHTML = '';

  // Se não há opções, mostrar mensagem
  if (opcoes.length === 0) {
    container.innerHTML = '<div class="text-muted small">Nenhuma opção disponível</div>';
    return;
  }

  // Criar checkboxes para cada opção
  opcoes.forEach((opcao, index) => {
    const value = opcao.value;
    const isSelected = filtrosSelecionados[tipo].includes(value);

    const checkboxDiv = document.createElement('div');
    checkboxDiv.className = 'form-check opcao-checkbox';
    checkboxDiv.dataset.value = value;
    const label = tipo === 'match_types' ? getMatchTypeLabel(value) : value;
    checkboxDiv.innerHTML = `
      <input class="form-check-input"
             type="checkbox"
             id="checkbox-${tipo}-${originalIndex}-${index}"
             ${isSelected ? 'checked' : ''}
             onchange="atualizarSelecaoDropdownAuditoriaComparativa('${tipo}', ${originalIndex})">
      <label class="form-check-label small" for="checkbox-${tipo}-${originalIndex}-${index}">
        ${label}
      </label>
    `;

    container.appendChild(checkboxDiv);
  });

  // Atualizar texto do input
  atualizarTextoInputDropdownAuditoriaComparativa(tipo, originalIndex);
}

/**
 * Filtra as opções do dropdown baseado na busca
 * @param {string} tipo - Tipo do filtro
 * @param {number} originalIndex - Índice original da coluna
 */
function filtrarOpcoesDropdownAuditoriaComparativa(tipo, originalIndex) {
  const buscaInput = document.getElementById(`busca-${tipo}-${originalIndex}`);
  const container = document.getElementById(`container-${tipo}-${originalIndex}`);

  if (!buscaInput || !container) return;

  const termoBusca = buscaInput.value.toLowerCase();
  const checkboxes = container.querySelectorAll('.opcao-checkbox');

  checkboxes.forEach((checkbox) => {
    const valor = checkbox.dataset.value.toLowerCase();
    const shouldShow = valor.includes(termoBusca);
    checkbox.style.display = shouldShow ? 'block' : 'none';
  });
}

/**
 * Seleciona todas as opções do dropdown
 * @param {string} tipo - Tipo do filtro
 * @param {number} originalIndex - Índice original da coluna
 */
function selecionarTodosDropdownAuditoriaComparativa(tipo, originalIndex) {
  const container = document.getElementById(`container-${tipo}-${originalIndex}`);
  if (!container) return;

  const checkboxes = container.querySelectorAll('.form-check-input:not([style*="display: none"])');
  checkboxes.forEach(checkbox => {
    checkbox.checked = true;
  });

  atualizarSelecaoDropdownAuditoriaComparativa(tipo, originalIndex);
}

/**
 * Limpa seleção do dropdown
 * @param {string} tipo - Tipo do filtro
 * @param {number} originalIndex - Índice original da coluna
 */
function limparSelecaoDropdownAuditoriaComparativa(tipo, originalIndex) {
  const container = document.getElementById(`container-${tipo}-${originalIndex}`);
  if (!container) return;

  const checkboxes = container.querySelectorAll('.form-check-input');
  checkboxes.forEach(checkbox => {
    checkbox.checked = false;
  });

  atualizarSelecaoDropdownAuditoriaComparativa(tipo, originalIndex);
}

/**
 * Atualiza seleção do dropdown
 * @param {string} tipo - Tipo do filtro
 * @param {number} originalIndex - Índice original da coluna
 */
function atualizarSelecaoDropdownAuditoriaComparativa(tipo, originalIndex) {
  const container = document.getElementById(`container-${tipo}-${originalIndex}`);
  if (!container) return;

  const checkboxes = container.querySelectorAll('.form-check-input:checked');
  const valoresSelecionados = Array.from(checkboxes).map(cb => {
    return cb.closest('.opcao-checkbox').dataset.value;
  });

  // Atualizar estado global
  window.auditoriaComparativaDropdownFilters.filtrosSelecionados[tipo] = valoresSelecionados;

  // Atualizar texto do input
  atualizarTextoInputDropdownAuditoriaComparativa(tipo, originalIndex);
}

/**
 * Atualiza texto do input do dropdown
 * @param {string} tipo - Tipo do filtro
 * @param {number} originalIndex - Índice original da coluna
 */
function atualizarTextoInputDropdownAuditoriaComparativa(tipo, originalIndex) {
  const input = document.getElementById(`filter-${tipo}-${originalIndex}`);
  if (!input) return;

  const valoresSelecionados = window.auditoriaComparativaDropdownFilters.filtrosSelecionados[tipo];
  
  if (valoresSelecionados.length === 0) {
    input.value = '';
    input.placeholder = `Filtrar ${tipo}...`;
  } else if (valoresSelecionados.length === 1) {
    input.value = valoresSelecionados[0];
  } else {
    input.value = `${valoresSelecionados.length} selecionados`;
  }
}

/**
 * Aplica filtro do dropdown
 * @param {string} tipo - Tipo do filtro
 * @param {number} originalIndex - Índice original da coluna
 */
function aplicarFiltroDropdownAuditoriaComparativa(tipo, originalIndex) {
  // Aplicar filtros na tabela
  aplicarFiltrosAuditoriaComparativa();

  // Fechar dropdown
  const dropdown = bootstrap.Dropdown.getInstance(document.getElementById(`filter-${tipo}-${originalIndex}`));
  if (dropdown) {
    dropdown.hide();
  }
}

/**
 * Aplica todos os filtros na tabela de auditoria comparativa
 */
function aplicarFiltrosAuditoriaComparativa() {
  const filtros = window.auditoriaComparativaDropdownFilters.filtrosSelecionados;

  // Salvar filtros ativos no estado global
  auditoriaComparativaData.filtrosAtivos = filtros;

  // Aplicar filtros nas linhas da tabela
  const rows = document.querySelectorAll('#auditoria-comparativa-table tbody tr');
  let visibleCount = 0;

  rows.forEach(row => {
    let shouldShow = true;
    const auditoriaId = row.dataset.auditoriaId;
    const auditoria = auditoriaComparativaData.auditorias.find(a => a.id == auditoriaId);

    if (!auditoria) return;

    // Verificar cada filtro
    Object.entries(filtros).forEach(([filterType, selectedValues]) => {
      if (selectedValues.length === 0) return;

      let cellValue = '';
      switch (filterType) {
        case 'parceiros':
          cellValue = auditoria.parceiro_nome || auditoria.cliente_nome || '';
          break;
        case 'regimes':
          cellValue = auditoria.regime_parceiro || '';
          break;
        case 'tipos':
          cellValue = auditoria.tipo_produto || '';
          break;
        case 'cfops_sped':
          cellValue = auditoria.sped_cfop || '';
          break;
        case 'cfops_xml':
          cellValue = auditoria.xml_cfop || '';
          break;
        case 'csts_sped':
          const tributoData = auditoria.tributos[auditoriaComparativaData.currentTributo];
          cellValue = tributoData?.cst || '';
          break;
        case 'csts_xml':
          cellValue = auditoria.xml_cst || auditoria.xml_data?.cst || '';
          break;
        case 'csosns':
          cellValue = auditoria.xml_csosn || auditoria.xml_data?.csosn || '';
          break;
        case 'ncms_sped':
          cellValue = auditoria.sped_ncm || '';
          break;
        case 'ncms_xml':
          cellValue = auditoria.xml_ncm || '';
          break;
        case 'status':
          cellValue = getStatusTributo(auditoria, auditoriaComparativaData.currentTributo);
          break;
        case 'origens_sped':
          cellValue = auditoria.sped_origem || '';
          break;
        case 'origens_xml':
          cellValue = auditoria.xml_origem || auditoria.xml_data?.origem || '';
          break;
        case 'reducoes':
          const tributoRed = auditoria.tributos[auditoriaComparativaData.currentTributo];
          cellValue = String(tributoRed?.reducao || '');
          break;
        case 'aliquotas_sped':
          const tributoAliqSped = auditoria.tributos[auditoriaComparativaData.currentTributo];
          const valSped = tributoAliqSped?.aliquota;
          cellValue = valSped !== undefined && valSped !== null && valSped !== ''
            ? parseFloat(valSped).toFixed(2)
            : '';
          break;
        case 'aliquotas_xml':
          const xmlAliq = getXmlTributoValue(auditoria, auditoriaComparativaData.currentTributo, 'aliquota');
          cellValue = xmlAliq !== undefined && xmlAliq !== null && xmlAliq !== ''
            ? parseFloat(xmlAliq).toFixed(2)
            : '';
          break;
        case 'match_types':
          cellValue = auditoria.match_type || '';
          break;
      }

      if (!selectedValues.includes(cellValue)) {
        shouldShow = false;
      }
    });

    if (shouldShow) {
      const textFilters = document.querySelectorAll('input.column-filter:not([data-filter-type])');
      textFilters.forEach(input => {
        if (!shouldShow) return;
        const value = input.value.trim().toLowerCase();
        if (value === '') return;
        const colIndex = parseInt(input.dataset.originalIndex);
        const cell = row.cells[colIndex];
        const cellText = cell ? cell.textContent.toLowerCase() : '';
        if (!cellText.includes(value)) {
          shouldShow = false;
        }
      });
    }

    if (shouldShow) {
      row.style.display = '';
      visibleCount++;
    } else {
      row.style.display = 'none';
      // Desmarcar checkbox se linha estiver oculta
      const checkbox = row.querySelector('.match-checkbox');
      if (checkbox && checkbox.checked) {
        checkbox.checked = false;
      }
    }
  });

  // Atualizar contador
  updateSelectedCount();

  // Atualizar badge de produtos visíveis
  const badge = document.querySelector('.card-header .badge');
  if (badge) {
    badge.textContent = `${visibleCount} produtos`;
  }

  // Atualizar info de filtros ativos
  atualizarInfoFiltrosAtivosAuditoriaComparativa();
  
  // Atualizar opções dos dropdowns com base nos registros visíveis
  atualizarOpcoesDropdownComBaseNosFiltros();
}

/**
 * Substitui um input de filtro de texto por um dropdown com checkboxes
 * @param {HTMLElement} input - Elemento input original
 * @param {string} tipo - Tipo do filtro
 */
function substituirInputPorDropdownAuditoriaComparativa(input, tipo) {
  const columnName = input.dataset.columnName;
  const originalIndex = parseInt(input.dataset.originalIndex);
  const placeholder = input.placeholder;

  // Criar dropdown
  const dropdownHtml = criarDropdownFiltroAuditoriaComparativa(
    tipo,
    placeholder,
    columnName,
    originalIndex,
  );

  // Substituir o input pelo dropdown
  input.outerHTML = dropdownHtml;

  // Popular o dropdown com as opções
  popularDropdownAuditoriaComparativa(tipo, originalIndex);
}

/**
 * Identifica o tipo de filtro baseado no nome da coluna
 * @param {string} columnName - Nome da coluna
 * @returns {string|null} Tipo do filtro ou null se não identificado
 */
function identificarTipoFiltroAuditoriaComparativa(columnName) {
  const mapeamento = {
    'Nome/Razão Social': 'parceiros',
    'Regime Parceiro': 'regimes',
    'Tipo': 'tipos',
    'CFOP SPED': 'cfops_sped',
    'CFOP Nota': 'cfops_xml',
    'CST SPED': 'csts_sped',
    'CST Nota': 'csts_xml',
    'CST PIS SPED': 'csts_sped',
    'CST COFINS SPED': 'csts_sped',
    'CST PIS Nota': 'csts_xml',
    'CST COFINS Nota': 'csts_xml',
    'CSOSN Nota': 'csosns',
    'NCM NFe': 'ncms_xml',
    'NCM SPED': 'ncms_sped',
    'Status': 'status',
    'Origem SPED': 'origens_sped',
    'Origem Nota': 'origens_xml',
    'Redução SPED %': 'reducoes',
    'Alíquota SPED %': 'aliquotas_sped',
    'Alíquota Nota %': 'aliquotas_xml',
    'Alíq PIS SPED %': 'aliquotas_sped',
    'Alíq COFINS SPED %': 'aliquotas_sped',
    'Alíq PIS Nota %': 'aliquotas_xml',
    'Alíq COFINS Nota %': 'aliquotas_xml',
    'Match': 'match_types'
  };

  return mapeamento[columnName] || null;
}

/**
 * Inicializa os filtros dropdown para auditoria comparativa
 */
async function inicializarFiltrosDropdownAuditoriaComparativa() {
  // Aguardar um pouco para garantir que a tabela esteja renderizada
  await new Promise((resolve) => setTimeout(resolve, 100));

  const tableContainer = document.querySelector('#auditoria-comparativa-table')?.closest('.table-responsive');
  if (!tableContainer) {
    console.warn('Container da tabela de auditoria comparativa não encontrado');
    return;
  }

  // Encontrar todos os inputs de filtro na linha de filtros
  const filterInputs = tableContainer.querySelectorAll('.column-filter');

  filterInputs.forEach(input => {
    const columnName = input.dataset.columnName;
    const tipoFiltro = identificarTipoFiltroAuditoriaComparativa(columnName);

    if (tipoFiltro) {
      substituirInputPorDropdownAuditoriaComparativa(input, tipoFiltro);
    }
  });
  
  const textInputs = tableContainer.querySelectorAll('input.column-filter:not([data-filter-type])');
  textInputs.forEach(input => {
    input.addEventListener('input', debounce(aplicarFiltrosAuditoriaComparativa, 300));
  });

  aplicarFiltrosAuditoriaComparativa();
  atualizarOpcoesDropdownComBaseNosFiltros();
}

/**
 * Limpa todos os filtros dropdown de auditoria comparativa
 */
function limparTodosFiltrosDropdownAuditoriaComparativa() {
  // Limpar estado global
  window.auditoriaComparativaDropdownFilters.filtrosSelecionados = {
    parceiros: [],
    cfops_sped: [],
    cfops_xml: [],
    csts_sped: [],
    csts_xml: [],
    ncms_sped: [],
    ncms_xml: [],
    origens_sped: [],
    origens_xml: [],
    aliquotas_sped: [],
    aliquotas_xml: [],
    status: [],
    match_types: [],
    regimes: [],
    tipos: [],
    csosns: [],
    reducoes: [],
  };

  // Atualizar todos os dropdowns
  ['parceiros', 'cfops_sped', 'cfops_xml', 'csts_sped', 'csts_xml', 'ncms_sped', 'ncms_xml', 'origens_sped', 'origens_xml', 'aliquotas_sped', 'aliquotas_xml', 'status', 'match_types', 'regimes', 'tipos', 'csosns', 'reducoes'].forEach(
    (tipo) => {
      const dropdowns = document.querySelectorAll(`[data-filter-type="${tipo}"]`);

      dropdowns.forEach((dropdown) => {
        const originalIndex = parseInt(dropdown.dataset.originalIndex);
        if (!isNaN(originalIndex)) {
          // Limpar texto do input
          dropdown.value = '';
          dropdown.placeholder = `Filtrar ${tipo}...`;

          // Repopular dropdown
          popularDropdownAuditoriaComparativa(tipo, originalIndex);
        }
      });
    },
  );

  // Limpar também os filtros de análise fiscal
  if (typeof clearAnalysisFilter === 'function') {
    clearAnalysisFilter();
  }

  // Aplicar filtros (que agora estão vazios)
  aplicarFiltrosAuditoriaComparativa();
}

/**
 * Atualiza informações dos filtros ativos
 */
function atualizarInfoFiltrosAtivosAuditoriaComparativa() {
  const filtros = window.auditoriaComparativaDropdownFilters.filtrosSelecionados;
  const totalFiltros = Object.keys(filtros).filter(key => filtros[key].length > 0).length;
  const totalItens = Object.values(filtros).reduce((acc, values) => acc + values.length, 0);

  const infoElement = document.getElementById('filtros-ativos-info');

  if (infoElement) {
    if (totalFiltros > 0) {
      infoElement.textContent = `${totalItens} filtro${totalItens > 1 ? 's' : ''} ativo${totalItens > 1 ? 's' : ''}`;
      infoElement.className = 'text-primary ms-2';
    } else {
      infoElement.textContent = '';
      infoElement.className = 'text-muted ms-2';
    }
  }
}

/**
 * Obtém os valores atualmente selecionados em todos os filtros
 * @returns {Object} Objeto com os filtros selecionados
 */
function obterFiltrosSelecionadosAuditoriaComparativa() {
  return { ...window.auditoriaComparativaDropdownFilters.filtrosSelecionados };
}
function coletarValoresVisiveisAuditoriaComparativa() {
  const tipos = ['parceiros','cfops_sped','cfops_xml','csts_sped','csts_xml','ncms_sped','ncms_xml','origens_sped','origens_xml','aliquotas_sped','aliquotas_xml','status','match_types','regimes','tipos','csosns','reducoes'];
  const valores = {};
  tipos.forEach(t => valores[t] = new Set());
  const rows = document.querySelectorAll('#auditoria-comparativa-table tbody tr:not([style*="display: none"])');
  rows.forEach(row => {
    const auditoriaId = row.dataset.auditoriaId;
    const auditoria = auditoriaComparativaData.auditorias.find(a => a.id == auditoriaId);
    if (!auditoria) return;
    const tributoData = auditoria.tributos[auditoriaComparativaData.currentTributo] || {};
    valores.parceiros.add(auditoria.parceiro_nome || auditoria.cliente_nome || '');
    valores.regimes.add(auditoria.regime_parceiro || '');
    valores.tipos.add(auditoria.tipo_produto || '');
    valores.cfops_sped.add(auditoria.sped_cfop || '');
    valores.cfops_xml.add(auditoria.xml_cfop || '');
    valores.csts_sped.add(tributoData.cst || '');
    valores.csts_xml.add(auditoria.xml_cst || auditoria.xml_data?.cst || '');
    valores.csosns.add(auditoria.xml_csosn || auditoria.xml_data?.csosn || '');
    valores.ncms_sped.add(auditoria.sped_ncm || '');
    valores.ncms_xml.add(auditoria.xml_ncm || '');
    valores.status.add(getStatusTributo(auditoria, auditoriaComparativaData.currentTributo));
    valores.origens_sped.add(auditoria.sped_origem || '');
    valores.origens_xml.add(auditoria.xml_origem || auditoria.xml_data?.origem || '');
    const redData = tributoData.reducao;
    valores.reducoes.add(redData !== undefined && redData !== null && redData !== '' ? String(redData) : '');
    const aliqSped = tributoData.aliquota;
    valores.aliquotas_sped.add(aliqSped !== undefined && aliqSped !== null && aliqSped !== '' ? parseFloat(aliqSped).toFixed(2) : '');
    const aliqXml = getXmlTributoValue(auditoria, auditoriaComparativaData.currentTributo, 'aliquota');
    valores.aliquotas_xml.add(aliqXml !== undefined && aliqXml !== null && aliqXml !== '' ? parseFloat(aliqXml).toFixed(2) : '');
    valores.match_types.add(auditoria.match_type || '');
  });
  return valores;
}

function atualizarOpcoesDropdownComBaseNosFiltros() {
  const valores = coletarValoresVisiveisAuditoriaComparativa();
  Object.entries(valores).forEach(([tipo, set]) => {
    const valoresValidos = new Set(Array.from(set).filter(v => v !== ''));
    const dropdowns = document.querySelectorAll(`[data-filter-type="${tipo}"]`);
    dropdowns.forEach(dropdown => {
      const originalIndex = parseInt(dropdown.dataset.originalIndex);
      const container = document.getElementById(`container-${tipo}-${originalIndex}`);
      if (!container) return;
      container.querySelectorAll('.opcao-checkbox').forEach(opt => {
        opt.style.display = valoresValidos.has(opt.dataset.value) ? 'block' : 'none';
      });
    });
  });
}