"""
Serviço otimizado para importação de XMLs com cache, processamento em lote e suporte a ZIP
"""
import zipfile
import io
import time
import gc
from typing import List, Dict, Set, Tuple, Optional
from collections import defaultdict
from datetime import datetime

from models import db, Empresa, Cliente, Produto, Tributo, ImportacaoXML, NotaFiscalItem
from utils import XMLProcessor
from utils.api_client import fetch_cnpj_data
from services.cenario_service import CenarioService


class OptimizedXMLImportService:
    """
    Serviço otimizado para importação de XMLs com:
    - Cache de entidades (clientes, produtos, cenários)
    - Processamento em lote
    - Suporte a arquivos ZIP
    - Otimização de consultas SQL
    """

    def __init__(self, empresa_id: int, escritorio_id: int, usuario_id: int):
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id
        self.usuario_id = usuario_id
        
        # Cache de entidades para evitar consultas repetitivas
        self.cache_clientes: Dict[str, Cliente] = {}  # {cnpj: cliente}
        self.cache_produtos: Dict[str, Produto] = {}  # {codigo: produto}
        self.cache_cenarios: Dict[str, object] = {}   # {hash_cenario: cenario}
        self.cache_empresas: Dict[int, Empresa] = {}  # {empresa_id: empresa}
        
        # Dados para inserção em lote
        self.batch_data = {
            'clientes': [],
            'produtos': [],
            'tributos': [],
            'nota_fiscal_items': [],
            'importacoes_xml': [],
            'cenarios': defaultdict(list)  # {tipo_tributo: [cenarios]}
        }
        
        # Estatísticas de performance
        self.stats = {
            'cache_hits': {'clientes': 0, 'produtos': 0, 'cenarios': 0},
            'cache_misses': {'clientes': 0, 'produtos': 0, 'cenarios': 0},
            'total_xmls': 0,
            'successful_imports': 0,
            'failed_imports': 0,
            'start_time': None,
            'end_time': None
        }

    def import_zip_file(self, zip_content: bytes, filename: str) -> Dict:
        """
        Importa um arquivo ZIP contendo múltiplos XMLs
        
        Args:
            zip_content (bytes): Conteúdo do arquivo ZIP
            filename (str): Nome do arquivo ZIP
            
        Returns:
            Dict: Resultado da importação
        """
        self.stats['start_time'] = time.time()
        
        try:
            # Extrair XMLs do ZIP
            xml_files = self._extract_xmls_from_zip(zip_content, filename)
            
            if not xml_files:
                return {
                    'success': False,
                    'message': 'Nenhum arquivo XML válido encontrado no ZIP',
                    'stats': self.stats
                }
            
            # Processar XMLs em lote
            result = self.import_xml_batch(xml_files)
            
            self.stats['end_time'] = time.time()
            result['stats'] = self.stats
            result['performance'] = self._calculate_performance_metrics()
            
            return result
            
        except Exception as e:
            self.stats['end_time'] = time.time()
            return {
                'success': False,
                'message': f'Erro ao processar arquivo ZIP: {str(e)}',
                'stats': self.stats
            }

    def import_xml_batch(self, xml_files: List[Dict]) -> Dict:
        """
        Importa múltiplos XMLs em lote com otimizações COMPLETAS
        Implementa todas as regras de negócio do XMLImportService original

        Args:
            xml_files (List[Dict]): Lista de dicionários {filename, content}

        Returns:
            Dict: Resultado da importação
        """
        self.stats['start_time'] = self.stats['start_time'] or time.time()
        self.stats['total_xmls'] = len(xml_files)

        results = {
            'success': True,
            'message': 'Importação em lote otimizada concluída',
            'successful_imports': 0,
            'failed_imports': 0,
            'errors': [],
            'warnings': []
        }

        try:
            print(f"🚀 Iniciando importação OTIMIZADA de {len(xml_files)} XMLs")

            # Pré-processar e validar XMLs
            valid_xmls, invalid_xmls = self._preprocess_and_validate_xmls(xml_files)

            # Adicionar XMLs inválidos aos erros
            for invalid_xml in invalid_xmls:
                results['errors'].append(invalid_xml)
                results['failed_imports'] += 1

            if not valid_xmls:
                results['success'] = False
                results['message'] = 'Nenhum XML válido encontrado'
                return results

            print(f"📊 XMLs válidos: {len(valid_xmls)}, inválidos: {len(invalid_xmls)}")

            # Pré-carregar dados da empresa no cache
            self._preload_company_data(self.empresa_id)

            # Processar XMLs em chunks para controle de memória
            chunk_size = 50

            for i in range(0, len(valid_xmls), chunk_size):
                chunk = valid_xmls[i:i + chunk_size]
                print(f"📦 Processando chunk {i//chunk_size + 1}/{(len(valid_xmls)-1)//chunk_size + 1} ({len(chunk)} XMLs)")

                chunk_result = self._process_xml_chunk_optimized(chunk)

                results['successful_imports'] += chunk_result['successful_imports']
                results['failed_imports'] += chunk_result['failed_imports']
                results['errors'].extend(chunk_result['errors'])

                # Inserir dados em lote após cada chunk
                try:
                    self._bulk_insert_chunk_data()
                    print(f"✅ Chunk {i//chunk_size + 1} inserido com sucesso")
                except Exception as e:
                    print(f"❌ Erro ao inserir chunk {i//chunk_size + 1}: {str(e)}")
                    # Marcar todos os XMLs do chunk como falha
                    results['failed_imports'] += chunk_result['successful_imports']
                    results['successful_imports'] -= chunk_result['successful_imports']
                    results['errors'].append({
                        'chunk': i//chunk_size + 1,
                        'error': f'Erro na inserção em lote: {str(e)}'
                    })

                # Limpar dados do chunk para liberar memória
                self._clear_chunk_data()
                gc.collect()

            self.stats['successful_imports'] = results['successful_imports']
            self.stats['failed_imports'] = results['failed_imports']

            print(f"🎉 Importação concluída: {results['successful_imports']} sucessos, {results['failed_imports']} falhas")
            return results

        except Exception as e:
            import traceback
            print(f"💥 Erro crítico na importação: {str(e)}")
            print(f"Stack trace: {traceback.format_exc()}")
            results['success'] = False
            results['message'] = f'Erro crítico na importação em lote: {str(e)}'
            return results

    def _extract_xmls_from_zip(self, zip_content: bytes, zip_filename: str) -> List[Dict]:
        """
        Extrai arquivos XML de um ZIP
        """
        xml_files = []
        
        try:
            with zipfile.ZipFile(io.BytesIO(zip_content), 'r') as zip_file:
                for file_info in zip_file.filelist:
                    if file_info.filename.lower().endswith('.xml') and not file_info.is_dir():
                        try:
                            xml_content = zip_file.read(file_info.filename).decode('utf-8')
                            xml_files.append({
                                'filename': file_info.filename,
                                'content': xml_content,
                                'source_zip': zip_filename
                            })
                        except Exception as e:
                            # Log do erro mas continua processando outros arquivos
                            print(f"Erro ao extrair {file_info.filename}: {str(e)}")
                            
        except Exception as e:
            raise ValueError(f"Erro ao processar arquivo ZIP: {str(e)}")
            
        return xml_files

    def _preprocess_and_validate_xmls(self, xml_files: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """
        Pré-processa e valida XMLs antes da importação
        """
        valid_xmls = []
        invalid_xmls = []
        
        for xml_file in xml_files:
            try:
                # Validação básica do XML
                processor = XMLProcessor(xml_file['content'])
                info_nfe = processor.get_info_nfe()
                emitente = processor.get_emitente()
                destinatario = processor.get_destinatario()
                
                # Verificar se tem dados mínimos necessários
                if not info_nfe.get('chave') or not info_nfe.get('numero'):
                    invalid_xmls.append({
                        'filename': xml_file['filename'],
                        'error': 'XML sem chave ou número da nota fiscal'
                    })
                    continue
                
                # Adicionar dados processados ao XML para evitar reprocessamento
                xml_file['processed_data'] = {
                    'info_nfe': info_nfe,
                    'emitente': emitente,
                    'destinatario': destinatario,
                    'produtos': processor.get_produtos()
                }
                
                valid_xmls.append(xml_file)
                
            except Exception as e:
                invalid_xmls.append({
                    'filename': xml_file['filename'],
                    'error': f'Erro ao processar XML: {str(e)}'
                })
        
        return valid_xmls, invalid_xmls



    def _get_empresa_cached(self, empresa_id: int) -> Optional[Empresa]:
        """
        Obtém empresa do cache ou banco de dados
        """
        if empresa_id not in self.cache_empresas:
            empresa = Empresa.query.get(empresa_id)
            if empresa:
                self.cache_empresas[empresa_id] = empresa
        
        return self.cache_empresas.get(empresa_id)

    def _safe_clean_document(self, document) -> Optional[str]:
        """
        Limpa documento de forma segura (mesmo método do XMLImportService)
        """
        if not document or not isinstance(document, str):
            return None

        try:
            return document.replace('.', '').replace('/', '').replace('-', '').strip()
        except (AttributeError, TypeError):
            return None



    def _preload_company_data(self, empresa_id: int):
        """
        Pré-carrega dados da empresa no cache para otimizar processamento
        """
        try:
            # Carregar clientes mais utilizados da empresa
            clientes_frequentes = db.session.query(Cliente).filter(
                Cliente.empresa_id == empresa_id
            ).limit(100).all()  # Top 100 clientes

            for cliente in clientes_frequentes:
                self.cache_clientes[cliente.cnpj] = cliente

            # Carregar produtos mais utilizados da empresa
            produtos_frequentes = db.session.query(Produto).filter(
                Produto.empresa_id == empresa_id
            ).limit(200).all()  # Top 200 produtos

            for produto in produtos_frequentes:
                self.cache_produtos[produto.codigo] = produto

        except Exception as e:
            print(f"Aviso: Erro ao pré-carregar dados da empresa {empresa_id}: {str(e)}")

    def _process_xml_chunk_optimized(self, xml_chunk: List[Dict]) -> Dict:
        """
        Processa um chunk de XMLs com TODAS as regras de negócio do sistema original
        """
        result = {
            'successful_imports': 0,
            'failed_imports': 0,
            'errors': []
        }

        for xml_file in xml_chunk:
            try:
                print(f"🔄 Processando XML: {xml_file['filename']}")

                # Usar dados já processados para evitar reprocessamento
                processed_data = xml_file['processed_data']

                # Processar com TODAS as regras de negócio
                import_result = self._process_single_xml_complete(xml_file, processed_data)

                if import_result['success']:
                    result['successful_imports'] += 1
                    print(f"✅ Sucesso: {xml_file['filename']}")

                    # Adicionar aos dados de lote para inserção posterior
                    self._add_to_batch_data(import_result)
                else:
                    result['failed_imports'] += 1
                    error_msg = import_result.get('message', 'Erro desconhecido')
                    print(f"❌ Falha: {xml_file['filename']} - {error_msg}")
                    result['errors'].append({
                        'filename': xml_file['filename'],
                        'error': error_msg
                    })

            except Exception as e:
                result['failed_imports'] += 1
                error_msg = f'Erro no processamento: {str(e)}'
                print(f"❌ Exceção: {xml_file['filename']} - {error_msg}")
                result['errors'].append({
                    'filename': xml_file['filename'],
                    'error': error_msg
                })
                import traceback
                print(f"Stack trace: {traceback.format_exc()}")

        return result

    def _process_single_xml_complete(self, xml_file: Dict, processed_data: Dict) -> Dict:
        """
        Processa um XML individual com TODAS as regras de negócio do XMLImportService original
        INCLUINDO VERIFICAÇÃO DE DUPLICATAS
        """
        try:
            info_nfe = processed_data['info_nfe']
            emitente = processed_data['emitente']
            destinatario = processed_data['destinatario']
            produtos = processed_data['produtos']

            # 1. Verificar se a empresa existe (mesmo que no XMLImportService)
            empresa = self._get_empresa_cached(self.empresa_id)
            if not empresa:
                return {
                    'success': False,
                    'message': 'Empresa não encontrada'
                }

            # 2. Determinar tipo de nota com FALLBACK (implementação completa)
            tipo_nota, emitente_final, destinatario_final = self._determine_note_type_complete(
                emitente, destinatario, empresa
            )

            if tipo_nota is None:
                # Erro: empresa não encontrada no XML
                cnpj_empresa = self._safe_clean_document(empresa.cnpj)
                doc_emitente = self._safe_clean_document(emitente.get('cnpj')) or self._safe_clean_document(emitente.get('cpf'))
                doc_destinatario = self._safe_clean_document(destinatario.get('cnpj')) or self._safe_clean_document(destinatario.get('cpf'))

                error_msg = f'O documento do emitente ({doc_emitente}) e do destinatário ({doc_destinatario}) não correspondem ao CNPJ da empresa ({cnpj_empresa})'
                return {
                    'success': False,
                    'message': error_msg
                }

            # 3. Processar cliente com cache E busca de dados na API
            # Para notas de entrada, verificar se o emitente é Simples Nacional
            crt_emitente = None
            if tipo_nota == '0':  # Nota de entrada
                crt_emitente = emitente_final.get('crt')
                print(f"DEBUG - Nota de entrada detectada. CRT do emitente: {crt_emitente}")

            cliente = self._get_or_create_cliente_complete(destinatario_final, crt_emitente)

            # 4. Verificar se XML já foi importado (verificação de duplicata por chave)
            chave_nf = info_nfe.get('chave')
            if chave_nf:
                importacao_existente = ImportacaoXML.query.filter_by(
                    empresa_id=self.empresa_id,
                    chave_nf=chave_nf
                ).first()

                if importacao_existente:
                    print(f"⚠️ XML {xml_file['filename']} já foi importado (chave: {chave_nf})")
                    return {
                        'success': False,
                        'message': f'XML já foi importado anteriormente (chave: {chave_nf})'
                    }

            # 5. Processar produtos e tributos com verificação de duplicatas
            produtos_processados = []
            tributos_processados = []
            nota_fiscal_items = []
            cenarios_criados = []

            for produto_data in produtos:
                produto = self._get_or_create_produto_cached(produto_data)

                # 6. Verificar se NotaFiscalItem já existe (MESMA LÓGICA DO XMLImportService)
                nota_fiscal_item_existente = NotaFiscalItem.query.filter_by(
                    empresa_id=self.empresa_id,
                    cliente_id=cliente.id,
                    produto_id=produto.id,
                    data_emissao=info_nfe.get('data_emissao'),
                    numero_nf=info_nfe.get('numero'),
                    num_item=produto_data.get('num_item')
                ).first()

                if not nota_fiscal_item_existente:
                    # Criar novo NotaFiscalItem apenas se não existir
                    nota_fiscal_item_data = {
                        'empresa_id': self.empresa_id,
                        'escritorio_id': self.escritorio_id,
                        'cliente_id': cliente.id,
                        'produto_id': produto.id,
                        'num_item': produto_data.get('num_item'),
                        'data_emissao': info_nfe.get('data_emissao'),
                        'numero_nf': info_nfe.get('numero'),
                        'chave_nf': info_nfe.get('chave'),
                        'cfop': produto_data.get('cfop'),
                        'ncm': produto_data.get('ncm'),
                        'unidade_comercial': produto_data.get('unidade_comercial'),
                        'quantidade': produto_data.get('quantidade'),
                        'valor_unitario': produto_data.get('valor_unitario'),
                        'valor_total': produto_data.get('valor_total'),
                        # Utilizar o tipo_nota determinado após validação da empresa
                        'tipo_nota': tipo_nota,
                        # Definir data de entrada apenas para notas classificadas como de entrada
                        'data_entrada': info_nfe.get('data_emissao') if tipo_nota == '0' else None
                    }
                    nota_fiscal_items.append(nota_fiscal_item_data)

                # 7. Verificar se Tributo já existe (MESMA LÓGICA DO XMLImportService)
                tributo_existente = (
                    db.session.query(Tributo)
                    .join(NotaFiscalItem, Tributo.nota_fiscal_item_id == NotaFiscalItem.id)
                    .filter(
                        Tributo.empresa_id == self.empresa_id,
                        Tributo.cliente_id == cliente.id,
                        Tributo.produto_id == produto.id,
                        Tributo.data_emissao == info_nfe.get('data_emissao'),
                        Tributo.numero_nf == info_nfe.get('numero'),
                        NotaFiscalItem.num_item == produto_data.get('num_item')
                    )
                    .first()
                )

                if not tributo_existente:
                    # Criar novo tributo apenas se não existir
                    tributo_data = self._prepare_tributo_data_complete(
                        produto_data, cliente.id, produto.id, info_nfe, tipo_nota
                    )
                    tributos_processados.append(tributo_data)

                    # 8. Criar cenários APENAS para notas de saída (tipo_nota = '1') e tributos novos
                    if tipo_nota == '1':
                        cenarios_produto = self._create_cenarios_for_produto(
                            produto_data, cliente.id, produto.id, info_nfe, tipo_nota
                        )
                        cenarios_criados.extend(cenarios_produto)
                else:
                    print(f"⚠️ Tributo já existe para produto {produto.id}, cliente {cliente.id}, NF {info_nfe.get('numero')}")

                produtos_processados.append(produto)

            # 9. Se não há dados novos para inserir, considerar como já processado
            if not nota_fiscal_items and not tributos_processados:
                return {
                    'success': False,
                    'message': f'XML {xml_file["filename"]} já foi processado anteriormente'
                }

            # 10. Preparar dados da importação XML
            importacao_data = {
                'empresa_id': self.empresa_id,
                'escritorio_id': self.escritorio_id,
                'usuario_id': self.usuario_id,
                'arquivo_nome': xml_file['filename'],
                'chave_nf': info_nfe.get('chave'),
                'numero_nf': info_nfe.get('numero'),
                'data_emissao': info_nfe.get('data_emissao'),
                'cnpj_emitente': emitente_final.get('cnpj'),
                'razao_social_emitente': emitente_final.get('nome'),
                'tipo_nota': tipo_nota,
                'data_entrada': info_nfe.get('data_emissao'),
                'data_emissao_original': info_nfe.get('data_emissao'),
                'crt_emitente': emitente_final.get('crt'),  # CRT do emitente
                'csosn': produtos[0].get('icms_csosn') if produtos and produtos[0].get('icms_csosn') else None,  # CSOSN do primeiro produto (informativo)
                'status': 'concluido',
                'status_validacao': 'validado'
            }

            return {
                'success': True,
                'importacao_data': importacao_data,
                'cliente': cliente,
                'produtos': produtos_processados,
                'tributos': tributos_processados,
                'nota_fiscal_items': nota_fiscal_items,
                'cenarios': cenarios_criados
            }

        except Exception as e:
            import traceback
            print(f"💥 Erro ao processar XML {xml_file['filename']}: {str(e)}")
            print(f"Stack trace: {traceback.format_exc()}")
            return {
                'success': False,
                'message': f'Erro ao processar XML {xml_file["filename"]}: {str(e)}'
            }

    def _determine_note_type_complete(self, emitente: Dict, destinatario: Dict, empresa) -> tuple:
        """
        Determina o tipo de nota com FALLBACK completo (mesmo que XMLImportService)

        Returns:
            tuple: (tipo_nota, emitente_final, destinatario_final) ou (None, None, None) se erro
        """
        try:
            # Extrair documentos com tratamento seguro
            cnpj_emitente = self._safe_clean_document(emitente.get('cnpj'))
            cpf_emitente = self._safe_clean_document(emitente.get('cpf'))
            doc_emitente = cnpj_emitente or cpf_emitente

            cnpj_destinatario = self._safe_clean_document(destinatario.get('cnpj'))
            cpf_destinatario = self._safe_clean_document(destinatario.get('cpf'))
            doc_destinatario = cnpj_destinatario or cpf_destinatario

            cnpj_empresa = self._safe_clean_document(empresa.cnpj)

            print(f"🔍 DEBUG - Doc Emitente: {doc_emitente} (CNPJ: {cnpj_emitente}, CPF: {cpf_emitente})")
            print(f"🔍 DEBUG - Doc Destinatário: {doc_destinatario} (CNPJ: {cnpj_destinatario}, CPF: {cpf_destinatario})")
            print(f"🔍 DEBUG - CNPJ Empresa: {cnpj_empresa}")

            # Determinar tipo de nota baseado na empresa e no tpNF
            if doc_emitente == cnpj_empresa:
                # Empresa é emitente - verificar tpNF do XML (implementação completa)
                # TODO: Implementar verificação do tpNF quando disponível
                tipo_nota = '1'  # Saída (padrão quando empresa é emitente)
                print(f"✅ Empresa como emitente. Processando como nota de saída.")
                return tipo_nota, emitente, destinatario

            elif doc_destinatario == cnpj_empresa:
                # Empresa é destinatário = sempre nota de entrada
                tipo_nota = '0'
                print(f"✅ FALLBACK ATIVADO: Empresa {empresa.razao_social} encontrada como destinatário. Processando como nota de entrada.")
                # Trocar emitente e destinatário para processar corretamente
                # IMPORTANTE: Preservar o CRT do emitente original
                emitente_original = emitente.copy()
                crt_original = emitente_original.get('crt')  # Preservar CRT
                emitente_final = destinatario.copy()
                emitente_final['crt'] = crt_original  # Restaurar CRT no novo emitente
                destinatario_final = emitente_original
                print(f"✅ Emitente e destinatário trocados. Novo emitente: {emitente_final.get('nome')}, CRT preservado: {crt_original}")
                return tipo_nota, emitente_final, destinatario_final
            else:
                # Empresa não encontrada no XML
                print(f"❌ ERRO: Empresa não encontrada no XML")
                return None, None, None

        except Exception as e:
            print(f"❌ Erro ao determinar tipo de nota: {str(e)}")
            return None, None, None

    def _get_or_create_cliente_complete(self, destinatario: Dict, crt_emitente: str = None) -> Cliente:
        """
        Obtém ou cria cliente com TODAS as regras do XMLImportService (incluindo API)

        Args:
            destinatario (Dict): Dados do destinatário
            crt_emitente (str): CRT do emitente (para notas de entrada)
        """
        # Obter CNPJ ou CPF do destinatário com tratamento seguro
        cnpj = self._safe_clean_document(destinatario.get('cnpj'))
        cpf = self._safe_clean_document(destinatario.get('cpf'))

        # Se for pessoa física (CPF), usar o CPF como CNPJ para manter compatibilidade
        documento = cnpj if cnpj else cpf if cpf else None

        # Registrar o tipo de documento para diagnóstico
        tipo_documento = "CNPJ" if cnpj else "CPF" if cpf else "Nenhum"
        print(f"🔍 DEBUG - Processando cliente: {tipo_documento} = {documento}")

        if not documento:
            raise ValueError("Destinatário sem CNPJ ou CPF. Não é possível processar o cliente.")

        # Verificar cache primeiro
        if documento in self.cache_clientes:
            self.stats['cache_hits']['clientes'] += 1
            cliente = self.cache_clientes[documento]
            # Atualizar informações do cliente existente
            self._update_cliente_data(cliente, destinatario)
            return cliente

        self.stats['cache_misses']['clientes'] += 1

        # Buscar no banco de dados
        cliente = Cliente.query.filter_by(
            empresa_id=self.empresa_id,
            cnpj=documento
        ).first()

        # Garantir que a inscrição estadual seja uma string
        inscricao_estadual = destinatario.get('ie')
        if inscricao_estadual is not None and not isinstance(inscricao_estadual, str):
            try:
                inscricao_estadual = str(inscricao_estadual)
            except:
                inscricao_estadual = None

        # Determinar atividade com base no indIEDest
        ind_ie_dest = destinatario.get('ind_ie_dest')
        atividade = None
        if ind_ie_dest == '9':
            atividade = 'Não Contribuinte'

        # Determinar destinação com base no indFinal
        ind_final = destinatario.get('ind_final')
        destinacao = None
        if ind_final == '1':
            destinacao = 'Uso e Consumo'

        # Variáveis para armazenar dados da API
        cnae = None
        simples_nacional = False

        if cliente:
            # Cliente já existe, atualizar informações
            self._update_cliente_data(cliente, destinatario)

            # Atualizar novos campos apenas se não estiverem definidos
            if ind_ie_dest:
                cliente.ind_ie_dest = ind_ie_dest
            if ind_final:
                cliente.ind_final = ind_final
            if atividade and not cliente.atividade:
                cliente.atividade = atividade
            if destinacao and not cliente.destinacao:
                cliente.destinacao = destinacao

            # Atualizar status do Simples Nacional baseado no CRT do emitente (para notas de entrada)
            if crt_emitente:
                cliente.simples_nacional = (crt_emitente == '1')
                print(f"DEBUG - Cliente {cliente.razao_social} atualizado: Simples Nacional = {cliente.simples_nacional} (CRT: {crt_emitente})")

            # Adicionar ao cache
            self.cache_clientes[documento] = cliente
            return cliente

        # Se for um novo cliente com CNPJ (não CPF), buscar dados adicionais na API
        if cnpj and not cpf:
            try:
                print(f"🌐 Buscando dados do CNPJ {cnpj} na API...")
                api_data = fetch_cnpj_data(cnpj)

                if api_data:
                    # Extrair CNAE, atividade, destinação e status do Simples Nacional
                    cnae = api_data.get('cnae')
                    simples_nacional = api_data.get('simples_nacional', False)

                    # Verificar se a natureza jurídica é 'Produtor Rural' e tem prioridade
                    natureza_juridica = api_data.get('natureza_juridica', {})
                    descricao_natureza_juridica = natureza_juridica.get('descricao', '').lower() if natureza_juridica else ''

                    if 'produtor rural' in descricao_natureza_juridica:
                        atividade = 'Produtor Rural'
                    elif not atividade and api_data.get('atividade'):
                        atividade = api_data.get('atividade')

                    # Se não tiver destinação definida pelo indFinal, usar a da API
                    if not destinacao and api_data.get('destinacao'):
                        destinacao = api_data.get('destinacao')

                    print(f"✅ Dados obtidos da API: CNAE={cnae}, Atividade={atividade}, Simples Nacional={simples_nacional}")
                else:
                    print(f"⚠️ Não foi possível obter dados do CNPJ {cnpj} na API.")
            except Exception as e:
                print(f"❌ Erro ao buscar dados do CNPJ {cnpj} na API: {str(e)}")

        # Determinar status do Simples Nacional
        # Prioridade: CRT do emitente (para notas de entrada) > API (para novos clientes)
        if crt_emitente:
            simples_nacional = (crt_emitente == '1')
            print(f"DEBUG - Novo cliente: Simples Nacional definido pelo CRT = {simples_nacional} (CRT: {crt_emitente})")

        # Criar novo cliente
        cliente = Cliente(
            empresa_id=self.empresa_id,
            escritorio_id=self.escritorio_id,
            cnpj=documento,
            razao_social=destinatario.get('nome', ''),
            inscricao_estadual=inscricao_estadual,
            logradouro=destinatario.get('logradouro'),
            numero=destinatario.get('numero'),
            bairro=destinatario.get('bairro'),
            municipio=destinatario.get('municipio'),
            uf=destinatario.get('uf'),
            cep=destinatario.get('cep'),
            pais=destinatario.get('pais'),
            codigo_pais=destinatario.get('codigo_pais'),
            ind_ie_dest=ind_ie_dest,
            ind_final=ind_final,
            atividade=atividade,
            destinacao=destinacao,
            cnae=cnae,
            simples_nacional=simples_nacional,
            status='novo',
            data_cadastro=datetime.now()
        )

        # Adicionar ao banco imediatamente para obter ID
        db.session.add(cliente)
        db.session.flush()  # Obter ID sem commit

        # Adicionar ao cache
        self.cache_clientes[documento] = cliente

        return cliente

    def _update_cliente_data(self, cliente: Cliente, destinatario: Dict):
        """
        Atualiza dados do cliente existente (mesmo que XMLImportService)
        """
        # Garantir que a inscrição estadual seja uma string
        inscricao_estadual = destinatario.get('ie')
        if inscricao_estadual is not None and not isinstance(inscricao_estadual, str):
            try:
                inscricao_estadual = str(inscricao_estadual)
            except:
                inscricao_estadual = None

        # Atualizar informações básicas
        cliente.razao_social = destinatario.get('nome', cliente.razao_social)
        cliente.inscricao_estadual = inscricao_estadual if inscricao_estadual is not None else cliente.inscricao_estadual
        cliente.logradouro = destinatario.get('logradouro', cliente.logradouro)
        cliente.numero = destinatario.get('numero', cliente.numero)
        cliente.bairro = destinatario.get('bairro', cliente.bairro)
        cliente.municipio = destinatario.get('municipio', cliente.municipio)
        cliente.uf = destinatario.get('uf', cliente.uf)
        cliente.cep = destinatario.get('cep', cliente.cep)
        cliente.pais = destinatario.get('pais', cliente.pais)
        cliente.codigo_pais = destinatario.get('codigo_pais', cliente.codigo_pais)

    def _get_or_create_produto_cached(self, produto_data: Dict) -> Produto:
        """
        Obtém ou cria produto usando cache
        """
        codigo = produto_data.get('codigo', '')

        # Verificar cache primeiro
        if codigo in self.cache_produtos:
            self.stats['cache_hits']['produtos'] += 1
            return self.cache_produtos[codigo]

        self.stats['cache_misses']['produtos'] += 1

        # Buscar no banco de dados
        produto = Produto.query.filter_by(
            empresa_id=self.empresa_id,
            codigo=codigo
        ).first()

        if produto:
            # Adicionar ao cache
            self.cache_produtos[codigo] = produto
            return produto

        # Criar novo produto
        cfop = str(produto_data.get('cfop', '')).strip()
        cfops_produto_acabado = ['5101', '6101', '5401', '6401', '6107-8', '61078']

        produto = Produto(
            empresa_id=self.empresa_id,
            escritorio_id=self.escritorio_id,
            codigo=codigo,
            descricao=produto_data.get('descricao', ''),
            unidade_comercial=produto_data.get('unidade_comercial'),
            unidade_tributavel=produto_data.get('unidade_tributavel'),
            codigo_ean=produto_data.get('codigo_ean'),
            codigo_ean_tributavel=produto_data.get('codigo_ean_tributavel'),
            unidade_tributaria=produto_data.get('unidade_tributaria'),
            tipo_sped='Produto Acabado' if cfop in cfops_produto_acabado else None,
            cest=produto_data.get('cest'),
            status='novo'
        )

        # Adicionar ao banco imediatamente para obter ID
        db.session.add(produto)
        db.session.flush()  # Obter ID sem commit

        # Adicionar ao cache
        self.cache_produtos[codigo] = produto

        return produto

    def _prepare_tributo_data_complete(self, produto_data: Dict, cliente_id: int,
                                     produto_id: int, info_nfe: Dict, tipo_nota: str) -> Dict:
        """
        Prepara dados COMPLETOS do tributo para inserção em lote (todos os campos do XMLImportService)
        """
        return {
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'cliente_id': cliente_id,
            'produto_id': produto_id,
            'data_emissao': info_nfe.get('data_emissao'),
            'data_saida': info_nfe.get('data_saida'),
            'numero_nf': info_nfe.get('numero'),
            'chave_nf': info_nfe.get('chave'),
            'tipo_operacao': tipo_nota,  # Usar o tipo_nota determinado
            'status': 'novo',
            'auditoria_status': 'pendente',

            # ICMS - TODOS os campos
            'icms_origem': produto_data.get('icms_origem'),
            'icms_cst': produto_data.get('icms_cst'),
            'icms_csosn': produto_data.get('icms_csosn'),  # CSOSN para Simples Nacional
            'icms_mod_bc': produto_data.get('icms_mod_bc'),
            'icms_p_red_bc': produto_data.get('icms_p_red_bc'),
            'icms_vbc': produto_data.get('icms_vbc'),
            'icms_aliquota': produto_data.get('icms_aliquota'),
            'icms_valor': produto_data.get('icms_valor'),
            'icms_v_op': produto_data.get('icms_v_op'),
            'icms_p_dif': produto_data.get('icms_p_dif'),
            'icms_v_dif': produto_data.get('icms_v_dif'),
            # Campos do Simples Nacional
            'icms_p_cred_sn': produto_data.get('icms_p_cred_sn'),  # % Crédito Simples Nacional
            'icms_v_cred_sn': produto_data.get('icms_v_cred_sn'),  # Valor Crédito Simples Nacional

            # ICMS-ST - TODOS os campos
            'icms_st_mod_bc': produto_data.get('icms_st_mod_bc'),
            'icms_st_p_mva': produto_data.get('icms_st_p_mva'),
            'icms_st_vbc': produto_data.get('icms_st_vbc'),
            'icms_st_aliquota': produto_data.get('icms_st_aliquota'),
            'icms_st_valor': produto_data.get('icms_st_valor'),

            # IPI - TODOS os campos
            'ipi_cst': produto_data.get('ipi_cst'),
            'ipi_vbc': produto_data.get('ipi_vbc'),
            'ipi_aliquota': produto_data.get('ipi_aliquota'),
            'ipi_valor': produto_data.get('ipi_valor'),
            'ipi_codigo_enquadramento': produto_data.get('ipi_codigo_enquadramento'),
            'ipi_ex': produto_data.get('extipi'),  # Campo EXTIPI do produto

            # PIS - TODOS os campos
            'pis_cst': produto_data.get('pis_cst'),
            'pis_vbc': produto_data.get('pis_vbc'),
            'pis_aliquota': produto_data.get('pis_aliquota'),
            'pis_valor': produto_data.get('pis_valor'),
            'pis_p_red_bc': produto_data.get('pis_p_red_bc'),

            # COFINS - TODOS os campos
            'cofins_cst': produto_data.get('cofins_cst'),
            'cofins_vbc': produto_data.get('cofins_vbc'),
            'cofins_aliquota': produto_data.get('cofins_aliquota'),
            'cofins_valor': produto_data.get('cofins_valor'),
            'cofins_p_red_bc': produto_data.get('cofins_p_red_bc'),

            # DIFAL - TODOS os campos
            'difal_vbc': produto_data.get('difal_vbc'),
            'difal_p_fcp_uf_dest': produto_data.get('difal_p_fcp_uf_dest'),
            'difal_p_icms_uf_dest': produto_data.get('difal_p_icms_uf_dest'),
            'difal_p_icms_inter': produto_data.get('difal_p_icms_inter'),
            'difal_p_icms_inter_part': produto_data.get('difal_p_icms_inter_part'),
            'difal_v_fcp_uf_dest': produto_data.get('difal_v_fcp_uf_dest'),
            'difal_v_icms_uf_dest': produto_data.get('difal_v_icms_uf_dest'),
            'difal_v_icms_uf_remet': produto_data.get('difal_v_icms_uf_remet'),

            # Valores do produto - TODOS os campos
            'quantidade': produto_data.get('quantidade'),
            'valor_unitario': produto_data.get('valor_unitario'),
            'valor_total': produto_data.get('valor_total'),
            'valor_frete': produto_data.get('valor_frete'),
            'valor_desconto': produto_data.get('valor_desconto'),

            # Campos de auditoria (TODOS os tributos)
            'auditoria_icms_status': 'pendente',
            'auditoria_icms_st_status': 'pendente',
            'auditoria_ipi_status': 'pendente',
            'auditoria_pis_status': 'pendente',
            'auditoria_cofins_status': 'pendente',
            'auditoria_difal_status': 'pendente'
        }

    def _create_cenarios_for_produto(self, produto_data: Dict, cliente_id: int,
                                   produto_id: int, info_nfe: Dict, tipo_nota: str) -> List[Dict]:
        """
        Cria cenários para um produto (APENAS para notas de saída - tipo_nota = '1')
        Implementa EXATAMENTE a mesma lógica do XMLImportService
        """
        cenarios_criados = []

        if tipo_nota != '1':
            # Não criar cenários para notas de entrada
            return cenarios_criados

        # Inicializar o serviço de cenários
        cenario_service = CenarioService(self.empresa_id, self.escritorio_id)

        # Determinar a direção com base no tipo_operacao
        direcao = 'entrada' if info_nfe.get('tipo_operacao') == '0' else 'saida'

        # Obter CFOP e NCM
        cfop = produto_data.get('cfop')
        ncm = produto_data.get('ncm')

        try:
            # ICMS
            if produto_data.get('icms_valor') is not None:
                icms_data = {
                    'origem': produto_data.get('icms_origem'),
                    'cst': produto_data.get('icms_cst'),
                    'mod_bc': produto_data.get('icms_mod_bc'),
                    'p_red_bc': produto_data.get('icms_p_red_bc'),
                    'aliquota': produto_data.get('icms_aliquota'),
                    'p_dif': produto_data.get('icms_p_dif'),
                    'direcao': direcao,
                    'tipo_operacao': info_nfe.get('tipo_operacao'),
                    'cfop': cfop,
                    'ncm': ncm
                }
                cenario_icms = cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'icms', icms_data)
                if cenario_icms:
                    cenarios_criados.append({
                        'tipo': 'icms',
                        'cenario_id': cenario_icms.id,
                        'tributo_field': 'cenario_icms_id'
                    })

            # ICMS-ST
            if produto_data.get('icms_st_valor') is not None:
                icms_st_data = {
                    'origem': produto_data.get('icms_origem'),
                    'cst': produto_data.get('icms_cst'),
                    'mod_bc': produto_data.get('icms_mod_bc'),
                    'p_red_bc': produto_data.get('icms_p_red_bc'),
                    'aliquota': produto_data.get('icms_aliquota'),
                    'icms_st_mod_bc': produto_data.get('icms_st_mod_bc'),
                    'icms_st_aliquota': produto_data.get('icms_st_aliquota'),
                    'icms_st_p_mva': produto_data.get('icms_st_p_mva'),
                    'direcao': direcao,
                    'tipo_operacao': info_nfe.get('tipo_operacao'),
                    'cfop': cfop,
                    'ncm': ncm
                }
                cenario_icms_st = cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'icms_st', icms_st_data)
                if cenario_icms_st:
                    cenarios_criados.append({
                        'tipo': 'icms_st',
                        'cenario_id': cenario_icms_st.id,
                        'tributo_field': 'cenario_icms_st_id'
                    })

            # IPI
            if produto_data.get('ipi_valor') is not None:
                ipi_data = {
                    'cst': produto_data.get('ipi_cst'),
                    'aliquota': produto_data.get('ipi_aliquota'),
                    'ex': produto_data.get('extipi'),
                    'direcao': direcao,
                    'tipo_operacao': info_nfe.get('tipo_operacao'),
                    'cfop': cfop,
                    'ncm': ncm
                }
                cenario_ipi = cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'ipi', ipi_data)
                if cenario_ipi:
                    cenarios_criados.append({
                        'tipo': 'ipi',
                        'cenario_id': cenario_ipi.id,
                        'tributo_field': 'cenario_ipi_id'
                    })

            # PIS
            if produto_data.get('pis_valor') is not None:
                pis_data = {
                    'cst': produto_data.get('pis_cst'),
                    'aliquota': produto_data.get('pis_aliquota'),
                    'p_red_bc': produto_data.get('pis_p_red_bc'),
                    'direcao': direcao,
                    'tipo_operacao': info_nfe.get('tipo_operacao'),
                    'cfop': cfop,
                    'ncm': ncm
                }
                cenario_pis = cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'pis', pis_data)
                if cenario_pis:
                    cenarios_criados.append({
                        'tipo': 'pis',
                        'cenario_id': cenario_pis.id,
                        'tributo_field': 'cenario_pis_id'
                    })

            # COFINS
            if produto_data.get('cofins_valor') is not None:
                cofins_data = {
                    'cst': produto_data.get('cofins_cst'),
                    'aliquota': produto_data.get('cofins_aliquota'),
                    'p_red_bc': produto_data.get('cofins_p_red_bc'),
                    'direcao': direcao,
                    'tipo_operacao': info_nfe.get('tipo_operacao'),
                    'cfop': cfop,
                    'ncm': ncm
                }
                cenario_cofins = cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'cofins', cofins_data)
                if cenario_cofins:
                    cenarios_criados.append({
                        'tipo': 'cofins',
                        'cenario_id': cenario_cofins.id,
                        'tributo_field': 'cenario_cofins_id'
                    })

            # DIFAL
            if produto_data.get('difal_v_icms_uf_dest') is not None:
                difal_data = {
                    'origem': produto_data.get('icms_origem'),
                    'cst': produto_data.get('icms_cst'),
                    'mod_bc': produto_data.get('icms_mod_bc'),
                    'p_red_bc': produto_data.get('icms_p_red_bc'),
                    'aliquota': produto_data.get('icms_aliquota'),
                    'p_fcp_uf_dest': produto_data.get('difal_p_fcp_uf_dest'),
                    'p_icms_uf_dest': produto_data.get('difal_p_icms_uf_dest'),
                    'p_icms_inter': produto_data.get('difal_p_icms_inter'),
                    'p_icms_inter_part': produto_data.get('difal_p_icms_inter_part'),
                    'direcao': direcao,
                    'tipo_operacao': info_nfe.get('tipo_operacao'),
                    'cfop': cfop,
                    'ncm': ncm
                }
                cenario_difal = cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'difal', difal_data)
                if cenario_difal:
                    cenarios_criados.append({
                        'tipo': 'difal',
                        'cenario_id': cenario_difal.id,
                        'tributo_field': 'cenario_difal_id'
                    })

            print(f"🎯 Cenários criados para produto {produto_id}: {len(cenarios_criados)}")
            return cenarios_criados

        except Exception as e:
            print(f"❌ Erro ao criar cenários para produto {produto_id}: {str(e)}")
            return cenarios_criados

    def _add_to_batch_data(self, import_result: Dict):
        """
        Adiciona dados do resultado da importação aos lotes para inserção
        """
        # Adicionar importação XML
        self.batch_data['importacoes_xml'].append(import_result['importacao_data'])

        # Adicionar tributos com IDs de cenários
        for i, tributo_data in enumerate(import_result.get('tributos', [])):
            # Adicionar IDs de cenários aos tributos
            cenarios = import_result.get('cenarios', [])
            for cenario in cenarios:
                if cenario.get('tributo_field'):
                    tributo_data[cenario['tributo_field']] = cenario['cenario_id']

            self.batch_data['tributos'].append(tributo_data)

        # Adicionar nota fiscal items
        for item_data in import_result.get('nota_fiscal_items', []):
            self.batch_data['nota_fiscal_items'].append(item_data)

    def _bulk_insert_chunk_data(self):
        """
        Insere dados do chunk atual em lote no banco de dados
        """
        try:
            print(f"📊 Inserindo chunk em lote...")
            print(f"   Importações XML: {len(self.batch_data['importacoes_xml'])}")
            print(f"   Tributos: {len(self.batch_data['tributos'])}")
            print(f"   Nota Fiscal Items: {len(self.batch_data.get('nota_fiscal_items', []))}")

            # Inserir importações XML em lote
            if self.batch_data['importacoes_xml']:
                db.session.bulk_insert_mappings(ImportacaoXML, self.batch_data['importacoes_xml'])
                print(f"✅ Importações XML inseridas")

            # Inserir nota fiscal items em lote
            if self.batch_data.get('nota_fiscal_items'):
                db.session.bulk_insert_mappings(NotaFiscalItem, self.batch_data['nota_fiscal_items'])
                print(f"✅ Nota Fiscal Items inseridos")

            # Inserir tributos em lote
            if self.batch_data['tributos']:
                db.session.bulk_insert_mappings(Tributo, self.batch_data['tributos'])
                print(f"✅ Tributos inseridos")

            # Commit todas as inserções
            db.session.commit()
            print(f"🎉 Inserção em lote do chunk concluída!")

        except Exception as e:
            db.session.rollback()
            print(f"💥 Erro na inserção em lote do chunk: {str(e)}")
            import traceback
            print(f"Stack trace: {traceback.format_exc()}")
            raise Exception(f"Erro na inserção em lote do chunk: {str(e)}")

    def _clear_chunk_data(self):
        """
        Limpa dados do chunk atual para liberar memória
        """
        self.batch_data['importacoes_xml'].clear()
        self.batch_data['tributos'].clear()
        self.batch_data['nota_fiscal_items'].clear()

        # Forçar coleta de lixo
        gc.collect()

    def _calculate_performance_metrics(self) -> Dict:
        """
        Calcula métricas de performance da importação
        """
        if not self.stats['start_time'] or not self.stats['end_time']:
            return {}

        total_time = self.stats['end_time'] - self.stats['start_time']
        total_xmls = self.stats['total_xmls']

        return {
            'total_time_seconds': round(total_time, 2),
            'total_xmls': total_xmls,
            'xmls_per_second': round(total_xmls / total_time, 2) if total_time > 0 else 0,
            'average_time_per_xml': round(total_time / total_xmls, 3) if total_xmls > 0 else 0,
            'cache_efficiency': {
                'clientes': {
                    'hits': self.stats['cache_hits']['clientes'],
                    'misses': self.stats['cache_misses']['clientes'],
                    'hit_rate': round(
                        self.stats['cache_hits']['clientes'] /
                        (self.stats['cache_hits']['clientes'] + self.stats['cache_misses']['clientes']) * 100, 2
                    ) if (self.stats['cache_hits']['clientes'] + self.stats['cache_misses']['clientes']) > 0 else 0
                },
                'produtos': {
                    'hits': self.stats['cache_hits']['produtos'],
                    'misses': self.stats['cache_misses']['produtos'],
                    'hit_rate': round(
                        self.stats['cache_hits']['produtos'] /
                        (self.stats['cache_hits']['produtos'] + self.stats['cache_misses']['produtos']) * 100, 2
                    ) if (self.stats['cache_hits']['produtos'] + self.stats['cache_misses']['produtos']) > 0 else 0
                }
            },
            'success_rate': round(
                self.stats['successful_imports'] / total_xmls * 100, 2
            ) if total_xmls > 0 else 0
        }

    def clear_cache(self):
        """
        Limpa todos os caches para liberar memória
        """
        self.cache_clientes.clear()
        self.cache_produtos.clear()
        self.cache_cenarios.clear()
        self.cache_empresas.clear()

        # Limpar dados de lote
        for key in self.batch_data:
            if isinstance(self.batch_data[key], list):
                self.batch_data[key].clear()
            elif isinstance(self.batch_data[key], dict):
                self.batch_data[key].clear()

        # Forçar coleta de lixo
        gc.collect()

    def get_cache_stats(self) -> Dict:
        """
        Retorna estatísticas dos caches
        """
        return {
            'cache_sizes': {
                'clientes': len(self.cache_clientes),
                'produtos': len(self.cache_produtos),
                'cenarios': len(self.cache_cenarios),
                'empresas': len(self.cache_empresas)
            },
            'batch_data_sizes': {
                'clientes': len(self.batch_data['clientes']),
                'produtos': len(self.batch_data['produtos']),
                'tributos': len(self.batch_data['tributos']),
                'importacoes_xml': len(self.batch_data['importacoes_xml'])
            },
            'performance_stats': self.stats
        }
