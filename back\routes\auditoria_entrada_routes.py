from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, Usuario, Empresa, ImportacaoXML, NotasFaltantes, AuditoriaEntrada
from models import HistoricoAlteracaoXML, NotaFiscalItem, Tributo, Produto, Cliente
from models import NotaEntrada, ItemNotaEntrada, ProdutoEntrada
from services.auditoria_entrada_service import AuditoriaEntradaService
from services.machine_learning_service import MachineLearningService
from services.notas_faltantes_service import NotasFaltantesService
from datetime import datetime, date
from sqlalchemy import and_, or_, func, desc
import json

# Criar blueprint
auditoria_entrada_bp = Blueprint('auditoria_entrada', __name__)

@auditoria_entrada_bp.route('/api/auditoria-entrada/xmls', methods=['GET'])
@jwt_required()
def listar_xmls_importados():
    """
    Lista XMLs importados com filtros por tipo (entrada/saída/faltantes)
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404
        
        # Parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        ano = request.args.get('ano', type=int)
        mes = request.args.get('mes', type=int)
        tipo = request.args.get('tipo', 'entrada')  # entrada, saida, faltantes-entrada, faltantes-saida
        
        if not empresa_id:
            return jsonify({"message": "ID da empresa é obrigatório"}), 400
        
        # Verificar permissão
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403
        
        if tipo in ['faltantes-entrada', 'faltantes-saida']:
            # Determinar tipo de nota baseado no tipo solicitado
            tipo_nota_filtro = '0' if tipo == 'faltantes-entrada' else '1'

            # Usar o novo serviço para obter notas faltantes
            service = NotasFaltantesService(empresa_id, usuario.escritorio_id)

            if ano and mes:
                resultado = service.identificar_notas_faltantes_completo(mes, ano, tipo_nota_filtro)

                if resultado['success']:
                    # Combinar faltantes XML vs SPED e pulos de numeração
                    todas_faltantes = resultado['faltantes_xml_sped'] + resultado['pulos_numeracao']

                    return jsonify({
                        "success": True,
                        "notas": todas_faltantes,
                        "total": len(todas_faltantes),
                        "resumo": resultado['resumo']
                    })
                else:
                    return jsonify({
                        "success": False,
                        "message": resultado['message'],
                        "notas": [],
                        "total": 0
                    })
            else:
                # Se não tem mês/ano específico, buscar notas faltantes registradas
                query = db.session.query(NotasFaltantes).filter(
                    NotasFaltantes.empresa_id == empresa_id
                )

                if ano:
                    query = query.filter(NotasFaltantes.ano_referencia == ano)

                notas = query.order_by(desc(NotasFaltantes.data_identificacao)).all()

                return jsonify({
                    "success": True,
                    "notas": [nota.to_dict() for nota in notas],
                    "total": len(notas)
                })

        elif tipo == 'faltantes':
            # Manter compatibilidade com código antigo
            query = db.session.query(NotasFaltantes).filter(
                NotasFaltantes.empresa_id == empresa_id
            )

            if ano:
                query = query.filter(NotasFaltantes.ano_referencia == ano)
            if mes:
                query = query.filter(NotasFaltantes.mes_referencia == mes)

            notas = query.order_by(desc(NotasFaltantes.data_identificacao)).all()

            return jsonify({
                "success": True,
                "notas": [nota.to_dict() for nota in notas],
                "total": len(notas)
            })
        
        else:
            # Listar XMLs de entrada ou saída
            tipo_nota = '0' if tipo == 'entrada' else '1'

            query = db.session.query(ImportacaoXML).filter(
                ImportacaoXML.empresa_id == empresa_id,
                ImportacaoXML.tipo_nota == tipo_nota,
                ImportacaoXML.status_validacao != 'cancelado'  # Não mostrar cancelados
            )
            
            if ano and mes:
                query = query.filter(
                    func.extract('year', ImportacaoXML.data_entrada) == ano,
                    func.extract('month', ImportacaoXML.data_entrada) == mes
                )
            elif ano:
                query = query.filter(func.extract('year', ImportacaoXML.data_entrada) == ano)
            
            xmls = query.order_by(desc(ImportacaoXML.data_importacao)).all()

            primeira_xml = query.order_by(
                ImportacaoXML.data_emissao.asc(),
                func.cast(ImportacaoXML.numero_nf, db.Integer).asc()
            ).first()
            ultima_xml = query.order_by(
                ImportacaoXML.data_emissao.desc(),
                func.cast(ImportacaoXML.numero_nf, db.Integer).desc()
            ).first()

            primeira_nota = None
            if primeira_xml:
                data_primeira = primeira_xml.data_emissao or primeira_xml.data_entrada
                primeira_nota = {
                    "numero_nf": primeira_xml.numero_nf,
                    "data": data_primeira.isoformat() if data_primeira else None
                }

            ultima_nota = None
            if ultima_xml:
                data_ultima = ultima_xml.data_emissao or ultima_xml.data_entrada
                ultima_nota = {
                    "numero_nf": ultima_xml.numero_nf,
                    "data": data_ultima.isoformat() if data_ultima else None
                }

            
            return jsonify({
                "success": True,
                "xmls": [xml.to_dict() for xml in xmls],
                "total": len(xmls),
                "primeira_nota": primeira_nota,
                "ultima_nota": ultima_nota
            })
    
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Erro ao listar XMLs: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/identificar-faltantes', methods=['POST'])
@jwt_required()
def identificar_notas_faltantes():
    """
    Identifica notas que estão no XML mas não no SPED ou vice-versa
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404
        
        data = request.get_json()
        empresa_id = data.get('empresa_id')
        mes = data.get('mes')
        ano = data.get('ano')
        
        if not all([empresa_id, mes, ano]):
            return jsonify({"message": "Empresa, mês e ano são obrigatórios"}), 400
        
        # Verificar permissão
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403
        
        # Obter tipo de nota do parâmetro (padrão entrada)
        tipo_nota = data.get('tipo_nota', '0')  # '0' para entrada, '1' para saída

        print(f"DEBUG - Filtros identificar faltantes: {data}")

        # Usar o novo serviço de notas faltantes
        service = NotasFaltantesService(empresa_id, empresa.escritorio_id)
        resultado = service.identificar_notas_faltantes_completo(mes, ano, tipo_nota)

        if resultado['success']:
            return jsonify({
                "success": True,
                "message": "Identificação concluída com sucesso",
                "total_faltantes": resultado['total_faltantes'],
                "resumo": resultado['resumo'],
                "detalhes": resultado
            })
        else:
            return jsonify({
                "success": False,
                "message": resultado['message'],
                "total_faltantes": 0
            }), 500
    
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Erro ao identificar notas faltantes: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/alterar-data-xml', methods=['POST'])
@jwt_required()
def alterar_data_xml():
    """
    Altera a data de entrada de XMLs (individual ou em massa)
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404
        
        data = request.get_json()
        xml_ids = data.get('xml_ids', [])  # Lista de IDs para alteração em massa
        nova_data = data.get('nova_data')
        motivo = data.get('motivo', '')
        
        if not xml_ids or not nova_data:
            return jsonify({"message": "IDs dos XMLs e nova data são obrigatórios"}), 400
        
        try:
            nova_data_obj = datetime.strptime(nova_data, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({"message": "Formato de data inválido. Use YYYY-MM-DD"}), 400
        
        alteracoes_realizadas = []
        
        for xml_id in xml_ids:
            xml = db.session.get(ImportacaoXML, xml_id)
            if not xml:
                continue
            
            # Verificar permissão
            empresa = db.session.get(Empresa, xml.empresa_id)
            if not empresa or empresa.escritorio_id != usuario.escritorio_id:
                continue
            
            # Salvar data original se ainda não foi salva
            if not xml.data_emissao_original:
                xml.data_emissao_original = xml.data_emissao
            
            # Criar histórico
            historico = HistoricoAlteracaoXML(
                importacao_xml_id=xml.id,
                usuario_id=usuario_id,
                data_anterior=xml.data_entrada or xml.data_emissao,
                data_nova=nova_data_obj,
                motivo=motivo
            )
            
            # Alterar data
            xml.data_entrada = nova_data_obj
            xml.status_validacao = 'validado'
            
            db.session.add(historico)
            alteracoes_realizadas.append({
                'xml_id': xml.id,
                'numero_nf': xml.numero_nf,
                'data_anterior': historico.data_anterior.isoformat() if historico.data_anterior else None,
                'data_nova': nova_data
            })
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": f"{len(alteracoes_realizadas)} XMLs alterados com sucesso",
            "alteracoes": alteracoes_realizadas
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"Erro ao alterar datas: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/excluir-xml', methods=['DELETE'])
@jwt_required()
def excluir_xml():
    """
    Exclui XMLs (marca como cancelado)
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404
        
        data = request.get_json()
        xml_ids = data.get('xml_ids', [])
        motivo = data.get('motivo', 'Cancelado pelo usuário')
        
        if not xml_ids:
            return jsonify({"message": "IDs dos XMLs são obrigatórios"}), 400
        
        exclusoes_realizadas = []
        
        for xml_id in xml_ids:
            xml = db.session.get(ImportacaoXML, xml_id)
            if not xml:
                continue
            
            # Verificar permissão
            empresa = db.session.get(Empresa, xml.empresa_id)
            if not empresa or empresa.escritorio_id != usuario.escritorio_id:
                continue
            
            # Marcar como cancelado
            xml.status_validacao = 'cancelado'
            xml.observacoes_validacao = motivo
            
            exclusoes_realizadas.append({
                'xml_id': xml.id,
                'numero_nf': xml.numero_nf
            })
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": f"{len(exclusoes_realizadas)} XMLs cancelados com sucesso",
            "exclusoes": exclusoes_realizadas
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"Erro ao cancelar XMLs: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/gerar-auditoria', methods=['POST'])
@jwt_required()
def gerar_auditoria():
    """
    Gera auditoria comparativa entre XML, SPED e Cenários (auditoria completa)
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        data = request.get_json()
        empresa_id = data.get('empresa_id')
        mes = data.get('mes')
        ano = data.get('ano')
        forcar_recalculo = data.get('forcar_recalculo', False)
        tolerancia = data.get('tolerancia', 0.01)

        if not all([empresa_id, mes, ano]):
            return jsonify({"message": "Empresa, mês e ano são obrigatórios"}), 400

        # Verificar permissão
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        # Executar geração de auditoria
        service = AuditoriaEntradaService(empresa_id, empresa.escritorio_id, usuario_id)
        resultado = service.gerar_auditoria_entrada(mes, ano, forcar_recalculo)

        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Erro ao gerar auditoria: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/gerar-escrituracao', methods=['POST'])
@jwt_required()
def gerar_auditoria_escrituracao():
    """
    Gera auditoria de escrituração comparando valores totais XML vs SPED
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        data = request.get_json()
        empresa_id = data.get('empresa_id')
        mes = data.get('mes')
        ano = data.get('ano')
        forcar_recalculo = data.get('forcar_recalculo', False)
        tolerancia = float(data.get('tolerancia', 0.01))  # Valor padrão de 0.01 (1%)

        if not all([empresa_id, mes, ano]):
            return jsonify({"message": "Empresa, mês e ano são obrigatórios"}), 400

        # Verificar permissão
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        # Executar geração de auditoria de escrituração
        service = AuditoriaEntradaService(empresa_id, empresa.escritorio_id, usuario_id)
        resultado = service.gerar_auditoria_escrituracao(mes, ano, forcar_recalculo, tolerancia)

        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Erro ao gerar auditoria de escrituração: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/notas-faltantes/<int:nota_id>', methods=['DELETE'])
@jwt_required()
def excluir_nota_faltante(nota_id):
    """
    Exclui uma nota faltante completamente do sistema
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar a nota faltante
        nota_faltante = db.session.get(NotasFaltantes, nota_id)
        if not nota_faltante:
            return jsonify({"message": "Nota faltante não encontrada"}), 404

        # Verificar permissão
        empresa = db.session.get(Empresa, nota_faltante.empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        chave_nf = nota_faltante.chave_nf

        # Excluir XML relacionado se existir
        xml_relacionado = db.session.query(ImportacaoXML).filter(
            ImportacaoXML.empresa_id == nota_faltante.empresa_id,
            ImportacaoXML.chave_nf == chave_nf
        ).first()

        if xml_relacionado:
            # Marcar como cancelado ao invés de excluir fisicamente
            xml_relacionado.status_validacao = 'cancelado'
            xml_relacionado.observacoes_validacao = f'Excluído via gestão de notas faltantes por {usuario.nome}'

        # Excluir nota SPED relacionada se existir
        nota_sped = db.session.query(NotaEntrada).filter(
            NotaEntrada.empresa_id == nota_faltante.empresa_id,
            NotaEntrada.chave_nf == chave_nf
        ).first()

        if nota_sped:
            # Excluir itens da nota SPED primeiro
            itens_sped = db.session.query(ItemNotaEntrada).filter(
                ItemNotaEntrada.nota_entrada_id == nota_sped.id
            ).all()

            for item in itens_sped:
                db.session.delete(item)

            # Excluir a nota SPED
            db.session.delete(nota_sped)

        # Excluir itens de nota fiscal relacionados
        itens_nf = db.session.query(NotaFiscalItem).filter(
            NotaFiscalItem.empresa_id == nota_faltante.empresa_id,
            NotaFiscalItem.chave_nf == chave_nf
        ).all()

        for item in itens_nf:
            db.session.delete(item)

        # Excluir a nota faltante
        db.session.delete(nota_faltante)

        db.session.commit()

        return jsonify({
            "success": True,
            "message": "Nota faltante excluída completamente do sistema"
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"Erro ao excluir nota faltante: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/listar', methods=['GET'])
@jwt_required()
def listar_auditoria():
    """
    Lista registros de auditoria de entrada
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404
        
        # Parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        ano = request.args.get('ano', type=int)
        mes = request.args.get('mes', type=int)
        tributo = request.args.get('tributo')  # icms, icms_st, ipi, pis, cofins
        status = request.args.get('status')  # pendente, conforme, divergente, auditado
        
        if not empresa_id:
            return jsonify({"message": "ID da empresa é obrigatório"}), 400
        
        # Verificar permissão
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403
        
        query = db.session.query(AuditoriaEntrada).filter(
            AuditoriaEntrada.empresa_id == empresa_id
        )
        
        if ano:
            query = query.filter(AuditoriaEntrada.ano_referencia == ano)
        if mes:
            query = query.filter(AuditoriaEntrada.mes_referencia == mes)
        
        # Filtro por status de tributo específico
        if tributo and status:
            if tributo == 'icms':
                query = query.filter(AuditoriaEntrada.status_icms == status)
            elif tributo == 'icms_st':
                query = query.filter(AuditoriaEntrada.status_icms_st == status)
            elif tributo == 'ipi':
                query = query.filter(AuditoriaEntrada.status_ipi == status)
            elif tributo == 'pis':
                query = query.filter(AuditoriaEntrada.status_pis == status)
            elif tributo == 'cofins':
                query = query.filter(AuditoriaEntrada.status_cofins == status)
        elif status:
            query = query.filter(AuditoriaEntrada.status_geral == status)
        
        auditorias = query.order_by(desc(AuditoriaEntrada.data_criacao)).all()
        
        return jsonify({
            "success": True,
            "auditorias": [auditoria.to_dict() for auditoria in auditorias],
            "total": len(auditorias)
        })
    
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Erro ao listar auditoria: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/escrituracao', methods=['GET'])
@jwt_required()
def listar_auditoria_escrituracao():
    """
    Lista registros de auditoria de escrituração com paginação e filtros
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        ano = request.args.get('ano', type=int)
        mes = request.args.get('mes', type=int)
        status = request.args.get('status')  # pendente, conforme, divergente, aprovado
        tolerancia = request.args.get('tolerancia', type=float, default=0.01)
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 500, type=int)  # Aumentar limite padrão

        if not empresa_id:
            return jsonify({"message": "ID da empresa é obrigatório"}), 400

        # Verificar permissão
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        # Query base para auditoria de escrituração com join para buscar dados do cliente
        query = db.session.query(AuditoriaEntrada).outerjoin(
            Cliente, AuditoriaEntrada.cliente_id == Cliente.id
        ).filter(
            AuditoriaEntrada.empresa_id == empresa_id,
            AuditoriaEntrada.xml_valor_total_nota.isnot(None),
            AuditoriaEntrada.sped_valor_total_nota.isnot(None)
        )

        if ano:
            query = query.filter(AuditoriaEntrada.ano_referencia == ano)
        if mes:
            query = query.filter(AuditoriaEntrada.mes_referencia == mes)
        if status:
            if status == 'conforme':
                query = query.filter(func.abs(AuditoriaEntrada.xml_valor_total_nota - AuditoriaEntrada.sped_valor_total_nota) <= tolerancia)
            elif status == 'divergente':
                query = query.filter(func.abs(AuditoriaEntrada.xml_valor_total_nota - AuditoriaEntrada.sped_valor_total_nota) > tolerancia)
            elif status in ['pendente', 'aprovado']:
                query = query.filter(AuditoriaEntrada.status_escrituracao == status)

        # Paginação
        total = query.count()
        auditorias = query.options(db.joinedload(AuditoriaEntrada.cliente)).order_by(desc(AuditoriaEntrada.data_criacao)).offset((page - 1) * per_page).limit(per_page).all()

        # Processar dados para incluir informações de conformidade
        auditorias_processadas = []
        resumo_xml_valor = 0.0
        resumo_sped_valor = 0.0
        resumo_divergentes = 0
        resumo_div_valor = 0.0
        for auditoria in auditorias:
            auditoria_dict = auditoria.to_dict()

            # Adicionar informações do cliente/participante
            if auditoria.cliente:
                auditoria_dict['participante_nome'] = auditoria.cliente.razao_social
                auditoria_dict['participante_cnpj'] = auditoria.cliente.cnpj or auditoria.cliente.cpf or 'N/A'
            else:
                auditoria_dict['participante_nome'] = 'N/A'
                auditoria_dict['participante_cnpj'] = 'N/A'

            # Verificar se é auditoria de escrituração (tem valores totais da nota)
            if auditoria.xml_valor_total_nota is not None and auditoria.sped_valor_total_nota is not None:
                # Auditoria de escrituração - usar valores totais da nota
                xml_valor = float(auditoria.xml_valor_total_nota or 0)
                sped_valor = float(auditoria.sped_valor_total_nota or 0)
                divergencia = abs(xml_valor - sped_valor)

                auditoria_dict['tipo_auditoria'] = 'escrituracao'
                auditoria_dict['xml_valor_total_formatado'] = f"R$ {xml_valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
                auditoria_dict['sped_valor_total_formatado'] = f"R$ {sped_valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
                auditoria_dict['divergencia_valor'] = divergencia
                if auditoria.status_escrituracao in ['conforme', 'aprovado']:
                    auditoria_dict['status_conformidade'] = 'conforme'
                else:
                    auditoria_dict['status_conformidade'] = 'conforme' if divergencia <= tolerancia else 'divergente'
                auditoria_dict['percentual_divergencia'] = (divergencia / xml_valor * 100) if xml_valor > 0 else 0

                # Status de escrituração
                auditoria_dict['status_escrituracao'] = auditoria.status_escrituracao or 'pendente'
                auditoria_dict['justificativa_escrituracao'] = auditoria.justificativa_escrituracao
                auditoria_dict['data_aprovacao_escrituracao'] = auditoria.data_aprovacao_escrituracao.isoformat() if auditoria.data_aprovacao_escrituracao else None

            else:
                # Auditoria completa - usar valores de itens
                xml_valor = float(auditoria.xml_valor_total or 0)
                sped_valor = float(auditoria.sped_valor_item or 0)
                divergencia = abs(xml_valor - sped_valor)

                auditoria_dict['tipo_auditoria'] = 'completa'
                auditoria_dict['xml_valor_total_formatado'] = f"R$ {xml_valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
                auditoria_dict['sped_valor_total_formatado'] = f"R$ {sped_valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
                auditoria_dict['divergencia_valor'] = divergencia
                if auditoria.status_escrituracao in ['conforme', 'aprovado']:
                    auditoria_dict['status_conformidade'] = 'conforme'
                else:
                    auditoria_dict['status_conformidade'] = 'conforme' if divergencia <= tolerancia else 'divergente'
                auditoria_dict['percentual_divergencia'] = (divergencia / xml_valor * 100) if xml_valor > 0 else 0

            resumo_xml_valor += xml_valor
            resumo_sped_valor += sped_valor
            resumo_div_valor += divergencia
            if divergencia > tolerancia:
                resumo_divergentes += 1

            auditorias_processadas.append(auditoria_dict)

        return jsonify({
            "success": True,
            "auditorias": auditorias_processadas,
            "total": total,
            "page": page,
            "per_page": per_page,
            "total_pages": (total + per_page - 1) // per_page,
            "resumo": {
                "notas_xml": total,
                "total_xml": resumo_xml_valor,
                "notas_sped": total,
                "total_sped": resumo_sped_valor,
                "divergentes": resumo_divergentes,
                "valor_divergente": resumo_div_valor
            }
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Erro ao listar auditoria de escrituração: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/detalhes-comparativo/<int:auditoria_id>', methods=['GET'])
@jwt_required()
def obter_detalhes_comparativo(auditoria_id):
    """
    Obtém detalhes comparativos entre XML e SPED para uma auditoria específica
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar auditoria
        auditoria = db.session.get(AuditoriaEntrada, auditoria_id)
        if not auditoria:
            return jsonify({"message": "Auditoria não encontrada"}), 404

        # Verificar permissão
        empresa = db.session.get(Empresa, auditoria.empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        # Buscar dados do XML
        xml_data = db.session.query(ImportacaoXML).filter(
            ImportacaoXML.chave_nf == auditoria.chave_nf,
            ImportacaoXML.empresa_id == auditoria.empresa_id
        ).first()

        # Buscar itens do XML com join no produto
        itens_xml = db.session.query(
            NotaFiscalItem,
            db.func.coalesce(
                db.func.substring(Produto.descricao, 1, 15),
                'Sem descrição'
            ).label('produto_descricao')
        ).outerjoin(
            Produto, NotaFiscalItem.produto_id == Produto.id
        ).filter(
            NotaFiscalItem.chave_nf == auditoria.chave_nf,
            NotaFiscalItem.empresa_id == auditoria.empresa_id
        ).all()

        # Buscar dados do SPED
        nota_sped = db.session.query(NotaEntrada).filter(
            NotaEntrada.chave_nf == auditoria.chave_nf,
            NotaEntrada.empresa_id == auditoria.empresa_id
        ).first()

        # Buscar itens do SPED com join no produto
        itens_sped = []
        if nota_sped:
            itens_sped = db.session.query(
                ItemNotaEntrada,
                db.func.coalesce(
                    db.func.substring(Produto.descricao, 1, 15),
                    db.func.substring(ItemNotaEntrada.descricao_complementar, 1, 15),
                    'Sem descrição'
                ).label('produto_descricao')
            ).outerjoin(
                NotaFiscalItem, ItemNotaEntrada.produto_entrada_id == NotaFiscalItem.id
            ).outerjoin(
                Produto, NotaFiscalItem.produto_id == Produto.id
            ).filter(
                ItemNotaEntrada.nota_entrada_id == nota_sped.id
            ).all()

        # Montar resposta
        return jsonify({
            "success": True,
            "auditoria": auditoria.to_dict(),
            "xml": {
                "dados_gerais": xml_data.to_dict() if xml_data else None,
                "itens": [
                    {
                        **item.NotaFiscalItem.to_dict(),
                        'descricao_produto': item.produto_descricao
                    }
                    for item in itens_xml
                ]
            },
            "sped": {
                "dados_gerais": nota_sped.to_dict() if nota_sped else None,
                "itens": [
                    {
                        **item.ItemNotaEntrada.to_dict(),
                        'descricao': item.produto_descricao or item.ItemNotaEntrada.descricao_complementar or 'Produto não identificado'
                    }
                    for item in itens_sped
                ]
            },
            "empresa": {
                "id": empresa.id,
                "nome": empresa.razao_social,
                "cnpj": empresa.cnpj
            }
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Erro ao obter detalhes comparativos: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/aprovar-escrituracao', methods=['POST'])
@jwt_required()
def aprovar_escrituracao():
    """
    Aprova auditoria de escrituração (individual ou em massa)
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        data = request.get_json()
        auditoria_ids = data.get('auditoria_ids', [])
        justificativa = data.get('justificativa', '')

        if not auditoria_ids:
            return jsonify({"message": "IDs das auditorias são obrigatórios"}), 400

        aprovacoes_realizadas = []

        for auditoria_id in auditoria_ids:
            auditoria = db.session.get(AuditoriaEntrada, auditoria_id)
            if not auditoria:
                continue

            # Verificar permissão
            empresa = db.session.get(Empresa, auditoria.empresa_id)
            if not empresa or empresa.escritorio_id != usuario.escritorio_id:
                continue

            # Verificar se há divergência e se justificativa é obrigatória
            # Para auditoria de escrituração, usar valores totais da nota
            if auditoria.xml_valor_total_nota is not None and auditoria.sped_valor_total_nota is not None:
                xml_valor = float(auditoria.xml_valor_total_nota)
                sped_valor = float(auditoria.sped_valor_total_nota)
            else:
                # Fallback para auditoria completa
                xml_valor = float(auditoria.xml_valor_total or 0)
                sped_valor = float(auditoria.sped_valor_item or 0)

            divergencia = abs(xml_valor - sped_valor)

            if divergencia > 0.01 and not justificativa:
                return jsonify({
                    "success": False,
                    "message": f"Justificativa é obrigatória para notas com divergência (NF {auditoria.numero_nf})"
                }), 400

            # Aprovar escrituração atualizando o status para "conforme"
            auditoria.status_escrituracao = 'conforme'
            auditoria.justificativa_escrituracao = justificativa
            auditoria.data_aprovacao_escrituracao = datetime.now()
            auditoria.usuario_aprovacao_escrituracao = usuario_id

            aprovacoes_realizadas.append({
                'auditoria_id': auditoria.id,
                'numero_nf': auditoria.numero_nf,
                'chave_nf': auditoria.chave_nf
            })

        db.session.commit()

        return jsonify({
            "success": True,
            "message": f"{len(aprovacoes_realizadas)} auditorias aprovadas com sucesso",
            "aprovacoes": aprovacoes_realizadas
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"Erro ao aprovar escrituração: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/ml/aprovar-padrao', methods=['POST'])
@jwt_required()
def aprovar_padrao_ml():
    """
    Aprova um padrão de auditoria para aprendizado de máquina
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        data = request.get_json()
        auditoria_id = data.get('auditoria_id')

        if not auditoria_id:
            return jsonify({"message": "ID da auditoria é obrigatório"}), 400

        # Verificar permissão
        auditoria = db.session.get(AuditoriaEntrada, auditoria_id)
        if not auditoria:
            return jsonify({"message": "Auditoria não encontrada"}), 404

        empresa = db.session.get(Empresa, auditoria.empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        # Registrar padrão no ML
        ml_service = MachineLearningService(auditoria.empresa_id)
        resultado = ml_service.registrar_padrao_aprovado(auditoria_id, usuario_id)

        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Erro ao aprovar padrão: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/ml/sugestoes', methods=['GET'])
@jwt_required()
def obter_sugestoes_ml():
    """
    Obtém sugestões de configuração baseadas em ML
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        empresa_id = request.args.get('empresa_id', type=int)
        cliente_id = request.args.get('cliente_id', type=int)
        produto_id = request.args.get('produto_id', type=int)
        cfop = request.args.get('cfop')
        ncm = request.args.get('ncm')

        if not all([empresa_id, cliente_id, produto_id, cfop, ncm]):
            return jsonify({"message": "Todos os parâmetros são obrigatórios"}), 400

        # Verificar permissão
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        # Obter sugestões
        ml_service = MachineLearningService(empresa_id)
        resultado = ml_service.sugerir_configuracao(cliente_id, produto_id, cfop, ncm)

        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Erro ao obter sugestões: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/ml/anomalias', methods=['GET'])
@jwt_required()
def analisar_anomalias_ml():
    """
    Analisa anomalias baseado no histórico de ML
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        empresa_id = request.args.get('empresa_id', type=int)
        mes = request.args.get('mes', type=int)
        ano = request.args.get('ano', type=int)

        if not all([empresa_id, mes, ano]):
            return jsonify({"message": "Empresa, mês e ano são obrigatórios"}), 400

        # Verificar permissão
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        # Analisar anomalias
        ml_service = MachineLearningService(empresa_id)
        resultado = ml_service.analisar_anomalias(mes, ano)

        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Erro ao analisar anomalias: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/ml/estatisticas', methods=['GET'])
@jwt_required()
def obter_estatisticas_ml():
    """
    Obtém estatísticas do sistema de ML
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        empresa_id = request.args.get('empresa_id', type=int)

        if not empresa_id:
            return jsonify({"message": "ID da empresa é obrigatório"}), 400

        # Verificar permissão
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        # Obter estatísticas
        ml_service = MachineLearningService(empresa_id)
        resultado = ml_service.obter_estatisticas_ml()

        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Erro ao obter estatísticas: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/xml-detalhes/<int:xml_id>', methods=['GET'])
@jwt_required()
def obter_detalhes_xml(xml_id):
    """
    Obtém detalhes completos de um XML importado (produtos, tributos, etc.)
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar XML
        xml = db.session.get(ImportacaoXML, xml_id)
        if not xml:
            return jsonify({"message": "XML não encontrado"}), 404

        # Verificar permissão
        empresa = db.session.get(Empresa, xml.empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        # Buscar produtos/itens da nota fiscal com joins
        from models import Produto, Cliente

        itens = db.session.query(NotaFiscalItem)\
            .join(Produto, NotaFiscalItem.produto_id == Produto.id)\
            .join(Cliente, NotaFiscalItem.cliente_id == Cliente.id)\
            .filter(
                NotaFiscalItem.empresa_id == xml.empresa_id,
                NotaFiscalItem.chave_nf == xml.chave_nf
            ).all()

        # Buscar tributos
        tributos = db.session.query(Tributo).filter(
            Tributo.empresa_id == xml.empresa_id,
            Tributo.chave_nf == xml.chave_nf
        ).all()

        # Organizar dados por produto
        produtos_detalhes = []
        for item in itens:
            # Buscar tributo correspondente
            tributo = next((t for t in tributos if t.produto_id == item.produto_id), None)

            produto_info = {
                'item': item.to_dict(),
                'tributo': tributo.to_dict() if tributo else None,
                'produto_nome': item.produto.descricao if item.produto else 'Produto não encontrado',
                'cliente_nome': item.cliente.razao_social if item.cliente else 'Cliente não encontrado'
            }
            produtos_detalhes.append(produto_info)

        # Calcular totais
        valor_total_nota = sum(float(item.valor_total or 0) for item in itens)
        total_icms = sum(float(t.icms_valor or 0) for t in tributos)
        total_icms_st = sum(float(t.icms_st_valor or 0) for t in tributos)
        total_ipi = sum(float(t.ipi_valor or 0) for t in tributos)
        total_pis = sum(float(t.pis_valor or 0) for t in tributos)
        total_cofins = sum(float(t.cofins_valor or 0) for t in tributos)

        return jsonify({
            "success": True,
            "xml": xml.to_dict(),
            "empresa": {
                "id": empresa.id,
                "razao_social": empresa.razao_social,
                "cnpj": empresa.cnpj
            },
            "produtos": produtos_detalhes,
            "totais": {
                "valor_total_nota": valor_total_nota,
                "total_icms": total_icms,
                "total_icms_st": total_icms_st,
                "total_ipi": total_ipi,
                "total_pis": total_pis,
                "total_cofins": total_cofins,
                "total_tributos": total_icms + total_icms_st + total_ipi + total_pis + total_cofins
            },
            "estatisticas": {
                "total_produtos": len(produtos_detalhes),
                "produtos_com_tributos": len([p for p in produtos_detalhes if p['tributo']]),
                "produtos_sem_tributos": len([p for p in produtos_detalhes if not p['tributo']])
            }
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Erro ao obter detalhes do XML: {str(e)}"
        }), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/xmls/<int:xml_id>', methods=['DELETE'])
@jwt_required()
def excluir_xml_individual(xml_id):
    """
    Exclui um XML importado individualmente
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar o XML
        xml = db.session.get(ImportacaoXML, xml_id)
        if not xml:
            return jsonify({"message": "XML não encontrado"}), 404

        # Verificar permissão
        empresa = db.session.get(Empresa, xml.empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        # Excluir registros relacionados primeiro
        # Excluir tributos relacionados
        tributos = db.session.query(Tributo).filter(
            Tributo.empresa_id == xml.empresa_id,
            Tributo.chave_nf == xml.chave_nf
        ).all()

        for tributo in tributos:
            db.session.delete(tributo)

        # Excluir produtos relacionados através dos tributos
        # O modelo Produto não tem chave_nf, então vamos excluir através dos tributos
        # que referenciam os produtos relacionados a este XML
        produtos_relacionados = db.session.query(Produto).join(Tributo).filter(
            Tributo.empresa_id == xml.empresa_id,
            Tributo.chave_nf == xml.chave_nf
        ).all()

        for produto in produtos_relacionados:
            # Verificar se o produto não é usado por outros tributos
            outros_tributos = db.session.query(Tributo).filter(
                Tributo.produto_id == produto.id,
                Tributo.chave_nf != xml.chave_nf
            ).first()

            if not outros_tributos:
                db.session.delete(produto)

        # Excluir clientes relacionados se não tiverem outras referências
        if xml.cnpj_emitente:
            cliente = db.session.query(Cliente).filter(
                Cliente.empresa_id == xml.empresa_id,
                Cliente.cnpj == xml.cnpj_emitente
            ).first()

            if cliente:
                # Verificar se há outros XMLs ou tributos usando este cliente
                outros_tributos = db.session.query(Tributo).filter(
                    Tributo.cliente_id == cliente.id
                ).first()

                if not outros_tributos:
                    db.session.delete(cliente)

        # Excluir o XML
        db.session.delete(xml)
        db.session.commit()

        return jsonify({
            "success": True,
            "message": "XML excluído com sucesso"
        })

    except Exception as e:
        db.session.rollback()
        print(f"Erro ao excluir XML: {str(e)}")
        return jsonify({"message": "Erro interno do servidor"}), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/notas-faltantes/<int:nota_id>', methods=['DELETE'])
@jwt_required()
def excluir_nota_faltante_individual(nota_id):
    """
    Exclui uma nota faltante individualmente
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar a nota faltante
        nota = db.session.get(NotasFaltantes, nota_id)
        if not nota:
            return jsonify({"message": "Nota faltante não encontrada"}), 404

        # Verificar permissão
        empresa = db.session.get(Empresa, nota.empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        # Excluir a nota faltante
        db.session.delete(nota)
        db.session.commit()

        return jsonify({
            "success": True,
            "message": "Nota faltante excluída com sucesso"
        })

    except Exception as e:
        db.session.rollback()
        print(f"Erro ao excluir nota faltante: {str(e)}")
        return jsonify({"message": "Erro interno do servidor"}), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/alterar-data-nota-faltante', methods=['POST'])
@jwt_required()
def alterar_data_nota_faltante():
    """
    Altera a data de uma nota faltante individual
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        data = request.get_json()
        nota_id = data.get('nota_id')
        nova_data_emissao = data.get('nova_data_emissao')
        nova_data_entrada = data.get('nova_data_entrada')
        observacoes = data.get('observacoes', '')

        if not nota_id:
            return jsonify({"message": "ID da nota é obrigatório"}), 400

        # Buscar a nota faltante
        nota = db.session.get(NotasFaltantes, nota_id)
        if not nota:
            return jsonify({"message": "Nota faltante não encontrada"}), 404

        # Verificar permissão
        empresa = db.session.get(Empresa, nota.empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        # Converter datas do formato brasileiro para datetime
        from datetime import datetime

        if nova_data_emissao:
            try:
                dia, mes, ano = nova_data_emissao.split('/')
                nota.data_emissao = datetime(int(ano), int(mes), int(dia))
            except ValueError:
                return jsonify({"message": "Formato de data de emissão inválido. Use DD/MM/AAAA"}), 400

        if nova_data_entrada:
            try:
                dia, mes, ano = nova_data_entrada.split('/')
                nota.data_entrada = datetime(int(ano), int(mes), int(dia))
            except ValueError:
                return jsonify({"message": "Formato de data de entrada inválido. Use DD/MM/AAAA"}), 400

        # Atualizar observações
        if observacoes:
            nota.observacoes = f"{nota.observacoes or ''}\n[{datetime.now().strftime('%d/%m/%Y %H:%M')}] {observacoes}".strip()

        db.session.commit()

        return jsonify({
            "success": True,
            "message": "Data alterada com sucesso"
        })

    except Exception as e:
        db.session.rollback()
        print(f"Erro ao alterar data da nota faltante: {str(e)}")
        return jsonify({"message": "Erro interno do servidor"}), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/alterar-data-massa', methods=['POST'])
@jwt_required()
def alterar_data_massa():
    """
    Altera datas de múltiplas notas faltantes
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        data = request.get_json()
        notas_ids = data.get('notas_ids', [])
        nova_data_emissao = data.get('nova_data_emissao')
        nova_data_entrada = data.get('nova_data_entrada')
        observacoes = data.get('observacoes', '')

        if not notas_ids:
            return jsonify({"message": "Nenhuma nota selecionada"}), 400

        if not nova_data_emissao and not nova_data_entrada:
            return jsonify({"message": "Pelo menos uma data deve ser informada"}), 400

        # Buscar notas faltantes
        notas = db.session.query(NotasFaltantes).filter(
            NotasFaltantes.id.in_(notas_ids)
        ).all()

        if not notas:
            return jsonify({"message": "Nenhuma nota encontrada"}), 404

        # Verificar permissões
        for nota in notas:
            empresa = db.session.get(Empresa, nota.empresa_id)
            if not empresa or empresa.escritorio_id != usuario.escritorio_id:
                return jsonify({"message": f"Acesso negado para nota {nota.id}"}), 403

        # Converter datas do formato brasileiro para datetime
        from datetime import datetime

        data_emissao_obj = None
        data_entrada_obj = None

        if nova_data_emissao:
            try:
                dia, mes, ano = nova_data_emissao.split('/')
                data_emissao_obj = datetime(int(ano), int(mes), int(dia))
            except ValueError:
                return jsonify({"message": "Formato de data de emissão inválido. Use DD/MM/AAAA"}), 400

        if nova_data_entrada:
            try:
                dia, mes, ano = nova_data_entrada.split('/')
                data_entrada_obj = datetime(int(ano), int(mes), int(dia))
            except ValueError:
                return jsonify({"message": "Formato de data de entrada inválido. Use DD/MM/AAAA"}), 400

        # Atualizar notas
        total_alteradas = 0
        for nota in notas:
            if data_emissao_obj:
                nota.data_emissao = data_emissao_obj

            if data_entrada_obj:
                nota.data_entrada = data_entrada_obj

            # Atualizar observações
            if observacoes:
                nota.observacoes = f"{nota.observacoes or ''}\n[{datetime.now().strftime('%d/%m/%Y %H:%M')}] Alteração em massa: {observacoes}".strip()

            total_alteradas += 1

        db.session.commit()

        return jsonify({
            "success": True,
            "message": f"{total_alteradas} notas alteradas com sucesso",
            "total_alteradas": total_alteradas
        })

    except Exception as e:
        db.session.rollback()
        print(f"Erro ao alterar datas em massa: {str(e)}")
        return jsonify({"message": "Erro interno do servidor"}), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/marcar-nota-encontrada/<int:nota_id>', methods=['POST'])
@jwt_required()
def marcar_nota_encontrada(nota_id):
    """
    Marca uma nota faltante como encontrada
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar a nota faltante
        nota_faltante = db.session.get(NotasFaltantes, nota_id)
        if not nota_faltante:
            return jsonify({"message": "Nota faltante não encontrada"}), 404

        # Verificar permissão
        empresa = db.session.get(Empresa, nota_faltante.empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        # Usar o serviço para marcar como encontrada
        service = NotasFaltantesService(nota_faltante.empresa_id, usuario.escritorio_id)
        resultado = service.marcar_nota_como_encontrada(nota_id)

        if resultado['success']:
            return jsonify(resultado)
        else:
            return jsonify(resultado), 500

    except Exception as e:
        return jsonify({"message": f"Erro ao marcar nota como encontrada: {str(e)}"}), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/excluir-nota-completa/<int:nota_id>', methods=['DELETE'])
@jwt_required()
def excluir_nota_faltante_completa(nota_id):
    """
    Exclui uma nota faltante completamente do sistema
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar a nota faltante
        nota_faltante = db.session.get(NotasFaltantes, nota_id)
        if not nota_faltante:
            return jsonify({"message": "Nota faltante não encontrada"}), 404

        # Verificar permissão
        empresa = db.session.get(Empresa, nota_faltante.empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        # Usar o serviço para excluir completamente
        service = NotasFaltantesService(nota_faltante.empresa_id, usuario.escritorio_id)
        resultado = service.excluir_nota_faltante_completa(nota_id)

        if resultado['success']:
            return jsonify(resultado)
        else:
            return jsonify(resultado), 500

    except Exception as e:
        return jsonify({"message": f"Erro ao excluir nota: {str(e)}"}), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/alterar-data-nota-real', methods=['POST'])
@jwt_required()
def alterar_data_nota_real():
    """
    Altera a data de entrada de notas reais (XML/SPED), movendo entre meses
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        data = request.get_json()
        chaves_nf = data.get('chaves_nf', [])
        nova_data_entrada = data.get('nova_data_entrada')
        motivo = data.get('motivo', 'Alteração de data de entrada')

        if not chaves_nf:
            return jsonify({"message": "Nenhuma nota selecionada"}), 400

        if not nova_data_entrada:
            return jsonify({"message": "Nova data de entrada é obrigatória"}), 400

        # Converter data do formato brasileiro para datetime
        try:
            dia, mes, ano = nova_data_entrada.split('/')
            data_entrada_obj = datetime(int(ano), int(mes), int(dia)).date()
        except ValueError:
            return jsonify({"message": "Formato de data inválido. Use DD/MM/AAAA"}), 400

        total_alteradas = 0

        # Alterar XMLs
        xmls = db.session.query(ImportacaoXML).filter(
            ImportacaoXML.chave_nf.in_(chaves_nf)
        ).all()

        for xml in xmls:
            # Verificar permissão
            empresa = db.session.get(Empresa, xml.empresa_id)
            if not empresa or empresa.escritorio_id != usuario.escritorio_id:
                continue

            # Criar histórico da alteração
            historico = HistoricoAlteracaoXML(
                importacao_xml_id=xml.id,
                usuario_id=usuario_id,
                data_anterior=xml.data_entrada,
                data_nova=data_entrada_obj,
                motivo=motivo
            )
            db.session.add(historico)

            # Alterar data
            xml.data_entrada = data_entrada_obj
            total_alteradas += 1

        # Alterar notas SPED
        from models import NotaEntrada
        notas_sped = db.session.query(NotaEntrada).filter(
            NotaEntrada.chave_nf.in_(chaves_nf)
        ).all()

        for nota in notas_sped:
            # Verificar permissão
            empresa = db.session.get(Empresa, nota.empresa_id)
            if not empresa or empresa.escritorio_id != usuario.escritorio_id:
                continue

            # Alterar data
            nota.data_entrada_saida = data_entrada_obj
            total_alteradas += 1

        db.session.commit()

        return jsonify({
            "success": True,
            "message": f"{total_alteradas} notas alteradas com sucesso",
            "total_alteradas": total_alteradas
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({"message": f"Erro ao alterar datas: {str(e)}"}), 500

@auditoria_entrada_bp.route('/api/auditoria-entrada/excluir-nota-real', methods=['POST'])
@jwt_required()
def excluir_nota_real():
    """Exclui notas reais (XML, SPED e registros relacionados) a partir da chave NF"""
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        data = request.get_json()
        chaves_nf = data.get('chaves_nf', [])
        motivo = data.get('motivo', 'Exclusão manual de nota')

        if not chaves_nf:
            return jsonify({"message": "Nenhuma chave informada"}), 400

        total_excluidas = 0

        for chave in chaves_nf:
            nota_processada = False

            xml = db.session.query(ImportacaoXML).filter(
                ImportacaoXML.chave_nf == chave
            ).first()
            if xml:
                empresa = db.session.get(Empresa, xml.empresa_id)
                if empresa and empresa.escritorio_id == usuario.escritorio_id:
                    xml.status_validacao = 'cancelado'
                    xml.observacoes_validacao = motivo
                    nota_processada = True

            nota_sped = db.session.query(NotaEntrada).filter(
                NotaEntrada.chave_nf == chave
            ).first()
            if nota_sped:
                empresa = db.session.get(Empresa, nota_sped.empresa_id)
                if empresa and empresa.escritorio_id == usuario.escritorio_id:
                    itens_sped = db.session.query(ItemNotaEntrada).filter(
                        ItemNotaEntrada.nota_entrada_id == nota_sped.id
                    ).all()
                    for item in itens_sped:
                        db.session.delete(item)
                    db.session.delete(nota_sped)
                    nota_processada = True

            itens_nf = db.session.query(NotaFiscalItem).filter(
                NotaFiscalItem.chave_nf == chave
            ).all()
            for item in itens_nf:
                empresa = db.session.get(Empresa, item.empresa_id)
                if empresa and empresa.escritorio_id == usuario.escritorio_id:
                    db.session.delete(item)

            nota_faltante = db.session.query(NotasFaltantes).filter(
                NotasFaltantes.chave_nf == chave
            ).first()
            if nota_faltante:
                empresa = db.session.get(Empresa, nota_faltante.empresa_id)
                if empresa and empresa.escritorio_id == usuario.escritorio_id:
                    db.session.delete(nota_faltante)
                    nota_processada = True

            if nota_processada:
                total_excluidas += 1

        db.session.commit()

        return jsonify({
            "success": True,
            "message": f"{total_excluidas} notas excluídas com sucesso",
            "total_excluidas": total_excluidas
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({"message": f"Erro ao excluir notas: {str(e)}"}), 500