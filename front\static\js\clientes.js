/**
 * clientes.js - Auditoria Fiscal
 * Funções para gerenciar a página de clientes
 */

// Controle da scrollbar dupla na tabela de clientes
let clientesScrollbar = null;

// Função para inicializar a página de clientes
function initClientesPage() {
  // Verificar se estamos na página de clientes
  if (
    window.location.pathname === '/fiscal/clientes' ||
    window.location.pathname.endsWith('/fiscal/clientes/')
  ) {
    setupClientesPage();
  } else {
  }
}

// Adicionar manipulador para quando o DOM estiver totalmente carregado
if (document.readyState === 'loading') {
  // O DOM ainda não foi completamente carregado
  document.addEventListener('DOMContentLoaded', initClientesPage);
} else {
  // O DOM já foi carregado
  initClientesPage();
}

// Adicionar um manipulador para o evento de mudança de rota (caso esteja usando um SPA)
window.addEventListener('popstate', function () {
  initClientesPage();
});

// Adicionar um manipulador de eventos global para depuração
document.addEventListener(
  'click',
  function (event) {
    // Verificar se o clique foi em um link de paginação
    if (event.target.closest('.pagination .page-link')) {
    }

    // Verificar se o clique foi no seletor de itens por página
    if (
      event.target.id === 'per-page-select' ||
      event.target.closest('#per-page-select')
    ) {
    }
  },
  true,
); // Usando capture: true para pegar eventos na fase de captura

/**
 * Configura a página de clientes
 */
function setupClientesPage() {
  // Carregar o conteúdo da página
  loadClientesContent();

  // Configurar evento para o botão de novo cliente
  document.addEventListener('click', function (event) {
    if (
      event.target.id === 'new-cliente-btn' ||
      (event.target.parentElement &&
        event.target.parentElement.id === 'new-cliente-btn')
    ) {
      event.preventDefault();
      event.stopPropagation();
      createNewCliente();
    }
  });
}

/**
 * Carrega o conteúdo da página de clientes
 */
function loadClientesContent() {
  const pageContent = document.getElementById('page-content');
  if (!pageContent) return;

  // Verificar se a seção já existe
  let clientesSection = document.getElementById('page-clientes');
  if (clientesSection) {
    clientesSection.classList.add('active');
    loadClientesData();
    return;
  }

  // Criar a seção de clientes
  clientesSection = document.createElement('div');
  clientesSection.id = 'page-clientes';
  clientesSection.className = 'page-section active';

  // Conteúdo HTML da página
  clientesSection.innerHTML = `
    <div class="section-header">
      <h2><i class="fas fa-users"></i> Participantes</h2>
    </div>

    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h5 class="card-title">Lista de Participantes</h5>
          <div class="d-flex">
            <button id="new-cliente-btn" class="btn btn-sm btn-primary">
              <i class="fas fa-plus"></i> Novo Participante
            </button>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-md-2">
            <div class="custom-dropdown">
              <select id="uf-filter" class="form-select form-select-sm">
                <option value="">Todos os Estados</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="custom-dropdown">
              <select id="municipio-filter" class="form-select form-select-sm">
                <option value="">Todos os Municípios</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="custom-dropdown">
              <select id="atividade-filter" class="form-select form-select-sm">
                <option value="">Todas as Atividades</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="custom-dropdown">
              <select id="destinacao-filter" class="form-select form-select-sm">
                <option value="">Todas as Destinações</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="custom-dropdown">
              <select id="cnae-filter" class="form-select form-select-sm">
                <option value="">Todos os CNAEs</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="d-flex">
              <button id="apply-filters-btn" class="btn btn-sm btn-secondary me-2">
                <i class="fas fa-filter"></i> Filtrar
              </button>
              <button id="clear-filters-btn" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-times"></i> Limpar
              </button>
            </div>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-12">
            <div class="d-flex">
              <button id="select-all-btn" class="btn btn-sm btn-outline-primary me-2">
                <i class="fas fa-check-square"></i> Selecionar Todos
              </button>
              <button id="bulk-edit-btn" class="btn btn-sm btn-outline-success me-2" disabled>
                <i class="fas fa-edit"></i> Edição em Massa
              </button>
            </div>
          </div>
        </div>

        <div id="clientes-content">
          <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Carregando...</span>
                        </div>
                      </div>
    </div>
  `;

  // Adicionar a seção ao conteúdo da página
  pageContent.appendChild(clientesSection);

  // Inicializar dropdowns customizados nos filtros
  if (window.initAllCustomDropdowns) {
    window.initAllCustomDropdowns();
  }
  // Configurar filtros
  setupClientesFilters();

  // Carregar dados de clientes
  loadClientesData();
}

/**
 * Configura os filtros da página de clientes
 */
function setupClientesFilters() {
  // Carregar opções para os filtros
  loadFilterOptions();

  // Configurar botão de aplicar filtros
  const applyFiltersBtn = document.getElementById('apply-filters-btn');
  if (applyFiltersBtn) {
    applyFiltersBtn.addEventListener('click', function () {
      loadClientesData();
    });
  }

  // Configurar botão de limpar filtros
  const clearFiltersBtn = document.getElementById('clear-filters-btn');
  if (clearFiltersBtn) {
    clearFiltersBtn.addEventListener('click', function () {
      // Limpar todos os filtros
      document.getElementById('uf-filter').value = '';
      document.getElementById('municipio-filter').value = '';
      document.getElementById('atividade-filter').value = '';
      document.getElementById('destinacao-filter').value = '';
      document.getElementById('cnae-filter').value = '';

      // Recarregar dados
      loadClientesData();
    });
  }

  // Configurar botão de selecionar todos
  const selectAllBtn = document.getElementById('select-all-btn');
  if (selectAllBtn) {
    selectAllBtn.addEventListener('click', function () {
      toggleSelectAllClientes();
    });
  }

  // Configurar botão de edição em massa
  const bulkEditBtn = document.getElementById('bulk-edit-btn');
  if (bulkEditBtn) {
    bulkEditBtn.addEventListener('click', function () {
      openBulkEditModal();
    });
  }

  // Atualizar municípios quando o estado for alterado
  const ufFilter = document.getElementById('uf-filter');
  if (ufFilter) {
    ufFilter.addEventListener('change', function () {
      updateMunicipioFilter(this.value);
    });
  }
}

/**
 * Carrega as opções para os filtros
 */
function loadFilterOptions() {
  // Se não houver empresa selecionada, usar valores padrão
  if (!selectedCompany) {
    loadDefaultFilterOptions();
    return;
  }

  // Obter dados para os filtros
  fetch(`/fiscal/api/clientes/filter-options?empresa_id=${selectedCompany}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => {
      return response.json();
    })
    .then((data) => {
      if (data.options) {
        // Preencher filtro de UF
        const ufFilter = document.getElementById('uf-filter');
        if (ufFilter && data.options.ufs && data.options.ufs.length > 0) {
          data.options.ufs.forEach((uf) => {
            const option = document.createElement('option');
            option.value = uf;
            option.textContent = uf;
            ufFilter.appendChild(option);
          });
        } else {
          if (ufFilter) loadDefaultUFs(ufFilter);
        }

        // Preencher filtro de atividade
        const atividadeFilter = document.getElementById('atividade-filter');
        if (
          atividadeFilter &&
          data.options.atividades &&
          data.options.atividades.length > 0
        ) {
          data.options.atividades.forEach((atividade) => {
            if (atividade) {
              const option = document.createElement('option');
              option.value = atividade;
              option.textContent = atividade;
              atividadeFilter.appendChild(option);
            }
          });
        } else {
          if (atividadeFilter) loadDefaultAtividades(atividadeFilter);
        }

        // Preencher filtro de destinação
        const destinacaoFilter = document.getElementById('destinacao-filter');
        if (
          destinacaoFilter &&
          data.options.destinacoes &&
          data.options.destinacoes.length > 0
        ) {
          data.options.destinacoes.forEach((destinacao) => {
            if (destinacao) {
              const option = document.createElement('option');
              option.value = destinacao;
              option.textContent = destinacao;
              destinacaoFilter.appendChild(option);
            }
          });
        } else {
          if (destinacaoFilter) loadDefaultDestinacoes(destinacaoFilter);
        }

        // Preencher filtro de CNAE
        const cnaeFilter = document.getElementById('cnae-filter');
        if (cnaeFilter && data.options.cnaes && data.options.cnaes.length > 0) {
          data.options.cnaes.forEach((cnae) => {
            if (cnae) {
              const option = document.createElement('option');
              option.value = cnae;
              option.textContent = cnae;
              cnaeFilter.appendChild(option);
            }
          });
        } else {
        }
      } else {
        loadDefaultFilterOptions();
      }
    })
    .catch((error) => {
      loadDefaultFilterOptions();
    });
}

/**
 * Carrega opções padrão para todos os filtros
 */
function loadDefaultFilterOptions() {
  // Carregar UFs padrão
  const ufFilter = document.getElementById('uf-filter');
  if (ufFilter) {
    loadDefaultUFs(ufFilter);
  }

  // Carregar atividades padrão
  const atividadeFilter = document.getElementById('atividade-filter');
  if (atividadeFilter) {
    loadDefaultAtividades(atividadeFilter);
  }

  // Carregar destinações padrão
  const destinacaoFilter = document.getElementById('destinacao-filter');
  if (destinacaoFilter) {
    loadDefaultDestinacoes(destinacaoFilter);
  }
}

/**
 * Carrega UFs padrão para o filtro
 */
function loadDefaultUFs(ufFilter) {
  const ufs = [
    'AC',
    'AL',
    'AP',
    'AM',
    'BA',
    'CE',
    'DF',
    'ES',
    'GO',
    'MA',
    'MT',
    'MS',
    'MG',
    'PA',
    'PB',
    'PR',
    'PE',
    'PI',
    'RJ',
    'RN',
    'RS',
    'RO',
    'RR',
    'SC',
    'SP',
    'SE',
    'TO',
  ];

  ufs.forEach((uf) => {
    const option = document.createElement('option');
    option.value = uf;
    option.textContent = uf;
    ufFilter.appendChild(option);
  });
}

/**
 * Carrega atividades padrão para o filtro
 */
function loadDefaultAtividades(atividadeFilter) {
  const atividades = [
    'Serviço',
    'Não Contribuinte',
    'Indústria ou Equiparado',
    'Comércio Varejista',
    'Comércio Atacadista',
    'Distribuidor',
    'Produtor Rural',
    'Consumidor Final',
    'Órgão Público',
  ];

  atividades.forEach((atividade) => {
    const option = document.createElement('option');
    option.value = atividade;
    option.textContent = atividade;
    atividadeFilter.appendChild(option);
  });
}

/**
 * Carrega destinações padrão para o filtro
 */
function loadDefaultDestinacoes(destinacaoFilter) {
  const destinacoes = [
    'Revenda',
    'Industrialização',
    'Uso e Consumo',
    'Ativo Imobilizado',
  ];

  destinacoes.forEach((destinacao) => {
    const option = document.createElement('option');
    option.value = destinacao;
    option.textContent = destinacao;
    destinacaoFilter.appendChild(option);
  });
}

/**
 * Atualiza o filtro de municípios com base no estado selecionado
 */
function updateMunicipioFilter(uf) {
  if (!selectedCompany) {
    return;
  }

  const municipioFilter = document.getElementById('municipio-filter');
  if (!municipioFilter) {
    return;
  }

  // Limpar opções atuais
  municipioFilter.innerHTML = '<option value="">Todos os Municípios</option>';

  if (!uf) {
    console.log('No UF selected, clearing municipality filter');
    return;
  }

  console.log(
    `Fetching municipalities for UF: ${uf} and company: ${selectedCompany}`,
  );

  // Obter municípios para o estado selecionado
  fetch(`/fiscal/api/clientes/municipios?empresa_id=${selectedCompany}&uf=${uf}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => {
      console.log('Municipality API response status:', response.status);
      return response.json();
    })
    .then((data) => {
      console.log('Municipality data received:', data);

      if (data.municipios && data.municipios.length > 0) {
        console.log(
          `Found ${data.municipios.length} municipalities for UF ${uf}`,
        );

        data.municipios.forEach((municipio) => {
          if (municipio) {
            const option = document.createElement('option');
            option.value = municipio;
            option.textContent = municipio;
            municipioFilter.appendChild(option);
            console.log(`Added municipality option: ${municipio}`);
          }
        });
      } else {
        console.warn(`No municipalities found for UF ${uf}`);
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar municípios:', error);
    });
}

/**
 * Seleciona ou desmarca todos os clientes
 */
function toggleSelectAllClientes() {
  const checkboxes = document.querySelectorAll('.cliente-checkbox');
  const selectAllBtn = document.getElementById('select-all-btn');

  // Verificar se todos estão selecionados
  const allSelected = Array.from(checkboxes).every((cb) => cb.checked);

  // Inverter a seleção
  checkboxes.forEach((checkbox) => {
    checkbox.checked = !allSelected;
  });

  // Atualizar texto do botão
  if (allSelected) {
    selectAllBtn.innerHTML =
      '<i class="fas fa-check-square"></i> Selecionar Todos';
  } else {
    selectAllBtn.innerHTML = '<i class="fas fa-square"></i> Desmarcar Todos';
  }

  // Atualizar estado do botão de edição em massa
  updateBulkEditButtonState();
}

/**
 * Atualiza o estado do botão de edição em massa
 */
function updateBulkEditButtonState() {
  const bulkEditBtn = document.getElementById('bulk-edit-btn');
  const selectedCount = document.querySelectorAll(
    '.cliente-checkbox:checked',
  ).length;

  if (selectedCount > 0) {
    bulkEditBtn.disabled = false;
    bulkEditBtn.innerHTML = `<i class="fas fa-edit"></i> Editar ${selectedCount} Cliente(s)`;
  } else {
    bulkEditBtn.disabled = true;
    bulkEditBtn.innerHTML = '<i class="fas fa-edit"></i> Edição em Massa';
  }
}

/**
 * Carrega os dados de clientes com suporte a paginação
 * @param {number} page - Número da página a ser carregada (padrão: 1)
 * @param {number} perPage - Número de itens por página (padrão: 100)
 */
function loadClientesData(page = 1, perPage = 100) {
  console.log('=== INICIANDO loadClientesData ===');
  console.log('Parâmetros recebidos - page:', page, 'perPage:', perPage);

  // Limpar scrollbars existentes
  cleanupDualScrollbarsClientes();

  const clientesContent = document.getElementById('clientes-content');
  if (!clientesContent) {
    console.error('Elemento clientes-content não encontrado');
    return;
  }

  console.log(`Carregando página ${page} com ${perPage} itens por página`);

  // Mostrar indicador de carregamento
  clientesContent.innerHTML = `
    <div class="text-center py-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Carregando...</span>
      </div>
      <p class="mt-2">Carregando dados, por favor aguarde...</p>
    </div>
  `;

  // Parâmetros de filtro
  const params = new URLSearchParams();

  // Adicionar empresa selecionada se existir
  if (selectedCompany) {
    params.append('empresa_id', selectedCompany);
    console.log('Adicionando empresa_id aos parâmetros:', selectedCompany);
  } else {
    console.warn('Nenhuma empresa selecionada');
  }

  // Adicionar parâmetros de paginação
  params.append('page', page);
  params.append('per_page', perPage);

  console.log(
    'Parâmetros de paginação definidos - page:',
    page,
    'per_page:',
    perPage,
  );

  // Adicionar filtros
  const filters = {
    uf: 'uf-filter',
    municipio: 'municipio-filter',
    atividade: 'atividade-filter',
    destinacao: 'destinacao-filter',
    cnae: 'cnae-filter',
  };

  // Adicionar filtros aos parâmetros
  Object.entries(filters).forEach(([param, elementId]) => {
    const element = document.getElementById(elementId);
    if (element && element.value) {
      params.append(param, element.value);
    }
  });

  console.log('Parâmetros da requisição:', params.toString());

  // Obter clientes da API
  const apiUrl = `/fiscal/api/clientes?${params.toString()}`;
  console.log('Fazendo requisição para a API:', apiUrl);

  fetch(apiUrl, {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`);
      }
      return response.json();
    })
    .then((data) => {
      console.log('Dados recebidos da API:', data);

      if (data.clientes && data.clientes.length > 0) {
        console.log(`Recebidos ${data.clientes.length} clientes`);
        // Renderizar tabela de clientes
        renderClientesTable(clientesContent, data.clientes, data.pagination);
      } else {
        console.log('Nenhum cliente encontrado com os filtros atuais');
        // Mostrar mensagem de nenhum cliente
        clientesContent.innerHTML = `
          <div class="alert alert-info">
            Nenhum cliente encontrado com os filtros atuais.
          </div>
        `;
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar clientes:', error);
      clientesContent.innerHTML = `
        <div class="alert alert-danger">
          Erro ao carregar clientes: ${error.message}
          <div class="mt-2">Por favor, tente novamente ou entre em contato com o suporte.</div>
        </div>
      `;
    });
}

/**
 * Renderiza a tabela de clientes com suporte a paginação
 * @param {HTMLElement} container - Container onde a tabela será renderizada
 * @param {Array} clientes - Lista de clientes
 * @param {Object} pagination - Informações de paginação
 */
function renderClientesTable(container, clientes, pagination) {
  console.log('Renderizando tabela de clientes...');
  console.log('Número de clientes:', clientes.length);
  console.log('Informações de paginação:', pagination);

  if (!container) {
    console.error('Container não fornecido para renderização da tabela');
    return;
  }
  // Criar controles de paginação se houver informações de paginação
  let paginationControls = '';
  if (pagination) {
    paginationControls = `
      <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="d-flex align-items-center">
          <span class="text-muted me-3">Mostrando ${pagination.from} a ${
      pagination.to
    } de ${pagination.total} registros</span>
          <select id="per-page-select" class="form-select form-select-sm" style="width: auto;">
            <option value="50" ${
              pagination.per_page == 50 ? 'selected' : ''
            }>50 por página</option>
            <option value="100" ${
              pagination.per_page == 100 ? 'selected' : ''
            }>100 por página</option>
            <option value="200" ${
              pagination.per_page == 200 ? 'selected' : ''
            }>200 por página</option>
            <option value="500" ${
              pagination.per_page == 500 ? 'selected' : ''
            }>500 por página</option>
          </select>
        </div>
        <nav aria-label="Navegação de páginas">
          <ul class="pagination mb-0">
            <li class="page-item ${!pagination.has_prev ? 'disabled' : ''}">
              <a class="page-link" href="#" data-page="1" aria-label="Primeira">
                <span aria-hidden="true">&laquo;&laquo;</span>
              </a>
            </li>
            <li class="page-item ${!pagination.has_prev ? 'disabled' : ''}">
              <a class="page-link" href="#" data-page="${
                pagination.prev_page || 1
              }" aria-label="Anterior">
                <span aria-hidden="true">&laquo;</span>
              </a>
            </li>
            ${getPaginationLinks(pagination.current_page, pagination.last_page)}
            <li class="page-item ${!pagination.has_next ? 'disabled' : ''}">
              <a class="page-link" href="#" data-page="${
                pagination.next_page || pagination.last_page
              }" aria-label="Próxima">
                <span aria-hidden="true">&raquo;</span>
              </a>
            </li>
            <li class="page-item ${!pagination.has_next ? 'disabled' : ''}">
              <a class="page-link" href="#" data-page="${
                pagination.last_page
              }" aria-label="Última">
                <span aria-hidden="true">&raquo;&raquo;</span>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    `;
  }

  // Criar HTML da tabela com scrollbar superior
  let html = `
    <div class="table-container-with-dual-scroll">
      <div class="table-top-scrollbar" id="clientes-top-scrollbar">
        <div class="table-top-scrollbar-content" id="clientes-top-scrollbar-content"></div>
      </div>
      <div class="table-responsive" id="clientes-table-responsive">
        <table id="clientes-table" class="table table-striped table-hover">
        <thead>
          <tr>
            <th>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="select-all-checkbox">
              </div>
            </th>
            <th>CNPJ / CPF</th>
            <th>Razão Social</th>
            <th>Estado</th>
            <th>Município</th>
            <th>Atividade</th>
            <th>Destinação</th>
            <th>CNAE</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Adicionar linhas da tabela
  clientes.forEach((cliente) => {
    // Formatar CNPJ: 53040685000166 -> 53.040.685/0001-66
    const cnpjFormatado = cliente.cnpj
      ? cliente.cnpj.replace(
          /^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/,
          '$1.$2.$3/$4-$5',
        )
      : cliente.cnpj;

    html += `
      <tr>
        <td>
          <div class="form-check">
            <input class="form-check-input cliente-checkbox" type="checkbox" data-id="${
              cliente.id
            }">
          </div>
        </td>
        <td>${cnpjFormatado || '-'}</td>
        <td>${cliente.razao_social || '-'}</td>
        <td>${cliente.uf || '-'}</td>
        <td>${cliente.municipio || '-'}</td>
        <td>${cliente.atividade || '-'}</td>
        <td>${cliente.destinacao || '-'}</td>
        <td>${cliente.cnae || '-'}</td>
        <td>
          <div class="btn-group">
            <button class="btn btn-sm btn-primary edit-cliente-btn" data-id="${
              cliente.id
            }">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-danger delete-cliente-btn" data-id="${
              cliente.id
            }" data-nome="${cliente.razao_social}">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </td>
      </tr>
    `;
  });

  html += `
        </tbody>
      </table>
      </div>
    </div>
    ${paginationControls}
  `;

  // Atualizar o conteúdo
  console.log('Atualizando conteúdo do container...');
  container.innerHTML = html;
  console.log('Conteúdo do container atualizado');

  // Verificar se os elementos de paginação estão presentes
  const paginationLinks = document.querySelectorAll('.pagination .page-link');
  console.log(
    `Encontrados ${paginationLinks.length} links de paginação no DOM`,
  );

  const perPageSelect = document.getElementById('per-page-select');
  console.log(
    'Seletor de itens por página encontrado:',
    perPageSelect !== null,
  );

  // Inicializar DataTable com paginação desativada (usamos nossa própria paginação no servidor)
  try {
    new DataTable('#clientes-table', {
      language: {
        url: '/fiscal/static/js/vendor/datatables/pt-BR.json',
      },
      responsive: true,
      paging: false,
      lengthChange: false,
      info: false,
    });
    setupDualScrollbarsClientes();
  } catch (error) {
    console.error('Erro ao inicializar DataTable:', error);
  }

  // Configurar botões de ação
  setupClienteButtons();

  // Configurar eventos para os links de paginação e seletor de itens por página
  if (pagination) {
    console.log('Configurando eventos de paginação...');

    // Usar event delegation para os links de paginação
    document.body.addEventListener('click', handlePaginationClick, true);

    // Usar event delegation para o seletor de itens por página
    document.body.addEventListener('change', handlePerPageChange, true);

    // Forçar rolagem para o topo da tabela
    const tableContainer = container.closest('.table-responsive');
    if (tableContainer) {
      tableContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }
}

/**
 * Gera os links de paginação
 * @param {number} currentPage - Página atual
 * @param {number} lastPage - Última página
 * @returns {string} HTML com os links de paginação
 */
function getPaginationLinks(currentPage, lastPage) {
  let links = '';
  const maxLinks = 5; // Número máximo de links a serem exibidos

  // Determinar o intervalo de páginas a serem exibidas
  let startPage = Math.max(1, currentPage - Math.floor(maxLinks / 2));
  let endPage = Math.min(lastPage, startPage + maxLinks - 1);

  // Ajustar o intervalo se necessário
  if (endPage - startPage + 1 < maxLinks && startPage > 1) {
    startPage = Math.max(1, endPage - maxLinks + 1);
  }

  // Gerar os links
  for (let i = startPage; i <= endPage; i++) {
    links += `
      <li class="page-item ${i === currentPage ? 'active' : ''}">
        <a class="page-link" href="#" data-page="${i}">${i}</a>
      </li>
    `;
  }

  return links;
}

/**
 * Manipulador de clique para os links de paginação usando event delegation
 */
function handlePaginationClick(event) {
  // Verificar se o clique foi em um link de paginação
  const paginationLink = event.target.closest('.page-link');
  if (!paginationLink) return;

  // Prevenir comportamento padrão
  event.preventDefault();
  event.stopPropagation();

  // Preciso apagar todos os consoles logs da página.

  // Obter o número da página
  const page = parseInt(paginationLink.getAttribute('data-page'));

  if (!isNaN(page) && page > 0) {
    // Obter o valor atual de itens por página
    const perPageSelect = document.getElementById('per-page-select');
    const perPage = perPageSelect ? parseInt(perPageSelect.value) : 100;

    // Carregar a página selecionada
    loadClientesData(page, perPage);

    // Rolar para o topo da tabela
    const tableContainer = document.querySelector('.table-responsive');
    if (tableContainer) {
      tableContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  } else {
    console.warn(
      'Número de página inválido:',
      paginationLink.getAttribute('data-page'),
    );
  }
}

/**
 * Configura eventos para os links de paginação
 * @deprecated Esta função foi substituída por handlePaginationClick com event delegation
 */
function setupPaginationEvents() {
  console.warn(
    'setupPaginationEvents está obsoleta. Usando event delegation em handlePaginationClick.',
  );
}

/**
 * Configura o seletor de itens por página
 * @deprecated Esta função foi substituída por handlePerPageChange com event delegation
 */
function setupPerPageSelect() {
  console.warn(
    'setupPerPageSelect está obsoleta. Usando event delegation em handlePerPageChange.',
  );
}

/**
 * Manipula a mudança no seletor de itens por página
 * @param {Event} event - O evento de mudança
 */
function handlePerPageChange(event) {
  // Verificar se o evento foi disparado pelo seletor de itens por página
  const perPageSelect = event.target.matches('#per-page-select')
    ? event.target
    : null;
  if (!perPageSelect) return;

  try {
    const perPage = parseInt(perPageSelect.value);
    if (isNaN(perPage) || perPage <= 0) {
      return;
    }

    // Prevenir comportamento padrão
    event.preventDefault();
    event.stopPropagation();

    // Atualizar o valor selecionado no seletor
    const options = perPageSelect.options;
    for (let i = 0; i < options.length; i++) {
      options[i].selected = parseInt(options[i].value) === perPage;
    }

    // Ao mudar a quantidade de itens por página, voltar para a primeira página
    loadClientesData(1, perPage);

    // Rolar para o topo da tabela
    const tableContainer = document.querySelector('.table-responsive');
    if (tableContainer) {
      tableContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  } catch (error) {}
}

/**
 * Configura os botões de ação da tabela de clientes
 */
function setupClienteButtons() {
  // Botão de editar cliente
  document.querySelectorAll('.edit-cliente-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const clienteId = this.getAttribute('data-id');
      editCliente(clienteId);
    });
  });

  // Botão de excluir cliente
  document.querySelectorAll('.delete-cliente-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const clienteId = this.getAttribute('data-id');
      const clienteNome = this.getAttribute('data-nome');
      deleteCliente(clienteId, clienteNome);
    });
  });

  // Checkbox de selecionar todos
  const selectAllCheckbox = document.getElementById('select-all-checkbox');
  if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener('change', function () {
      const checkboxes = document.querySelectorAll('.cliente-checkbox');
      checkboxes.forEach((checkbox) => {
        checkbox.checked = this.checked;
      });

      // Atualizar estado do botão de edição em massa
      updateBulkEditButtonState();
    });
  }

  // Checkboxes individuais
  document.querySelectorAll('.cliente-checkbox').forEach((checkbox) => {
    checkbox.addEventListener('change', function () {
      // Verificar se todos estão selecionados
      const allCheckboxes = document.querySelectorAll('.cliente-checkbox');
      const allChecked = Array.from(allCheckboxes).every((cb) => cb.checked);

      // Atualizar checkbox de selecionar todos
      const selectAllCheckbox = document.getElementById('select-all-checkbox');
      if (selectAllCheckbox) {
        selectAllCheckbox.checked = allChecked;
      }

      // Atualizar estado do botão de edição em massa
      updateBulkEditButtonState();
    });
  });
}

/**
 * Exclui um cliente
 * @param {number} clienteId - ID do cliente
 * @param {string} clienteNome - Nome do cliente para exibir na confirmação
 */
function deleteCliente(clienteId, clienteNome) {
  // Confirmar exclusão
  if (
    !confirm(
      `Tem certeza que deseja excluir o cliente "${clienteNome}"? Esta ação não pode ser desfeita.`,
    )
  ) {
    return;
  }

  // Enviar requisição para excluir o cliente
  fetch(`/fiscal/api/clientes/${clienteId}`, {
    method: 'DELETE',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.message) {
        alert(data.message);
        // Recarregar os dados de clientes
        loadClientesData();
      } else {
        alert('Erro ao excluir cliente');
      }
    })
    .catch((error) => {
      alert('Erro ao excluir cliente');
    });
}

/**
 * Edita um cliente
 */
function editCliente(clienteId) {
  // Obter detalhes do cliente da API
  fetch(`/fiscal/api/clientes/${clienteId}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.cliente) {
        // Criar modal para editar o cliente
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'edit-cliente-modal';
        modal.tabIndex = '-1';
        modal.setAttribute('aria-labelledby', 'edit-cliente-modal-label');
        modal.setAttribute('aria-hidden', 'true');

        const cliente = data.cliente;

        modal.innerHTML = `
          <div class="modal-dialog modal-lg">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="edit-cliente-modal-label">Editar Participante</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
              </div>
              <div class="modal-body">
                <form id="edit-cliente-form">
                  <ul class="nav nav-tabs" id="editClienteTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                      <button class="nav-link active" id="edit-info-tab" data-bs-toggle="tab" data-bs-target="#edit-info-content" type="button" role="tab" aria-controls="edit-info-content" aria-selected="true">
                        Informações Gerais
                      </button>
                    </li>
                    <li class="nav-item" role="presentation">
                      <button class="nav-link" id="edit-fiscal-tab" data-bs-toggle="tab" data-bs-target="#edit-fiscal-content" type="button" role="tab" aria-controls="edit-fiscal-content" aria-selected="false">
                        Informações Fiscais
                      </button>
                    </li>
                    <li class="nav-item" role="presentation">
                      <button class="nav-link" id="edit-endereco-tab" data-bs-toggle="tab" data-bs-target="#edit-endereco-content" type="button" role="tab" aria-controls="edit-endereco-content" aria-selected="false">
                        Endereço
                      </button>
                    </li>
                  </ul>

                  <div class="tab-content mt-3" id="editClienteTabsContent">
                    <!-- Tab Informações Gerais -->
                    <div class="tab-pane fade show active" id="edit-info-content" role="tabpanel" aria-labelledby="edit-info-tab">
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="cnpj" class="form-label">CNPJ / CPF *</label>
                          <input type="text" class="form-control" id="cnpj" name="cnpj" value="${
                            cliente.cnpj
                          }" required>
                        </div>
                        <div class="col-md-6">
                          <label for="razao_social" class="form-label">Razão Social *</label>
                          <input type="text" class="form-control" id="razao_social" name="razao_social" value="${
                            cliente.razao_social
                          }" required>
                        </div>
                      </div>
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="inscricao_estadual" class="form-label">Inscrição Estadual</label>
                          <input type="text" class="form-control" id="inscricao_estadual" name="inscricao_estadual" value="${
                            cliente.inscricao_estadual || ''
                          }">
                        </div>
                      </div>
                    </div>

                    <!-- Tab Informações Fiscais -->
                    <div class="tab-pane fade" id="edit-fiscal-content" role="tabpanel" aria-labelledby="edit-fiscal-tab">
                    <div class="row mb-3">
                        <div class="col-md-12">
                          <label for="natureza_juridica" class="form-label">Natureza Jurídica</label>
                          <input type="text" class="form-control" id="natureza_juridica" name="natureza_juridica" value="${
                            cliente.natureza_juridica || ''
                          }">
                        </div>
                      </div>
                      <div class="row mb-3">
                        <div class="col-md-2">
                          <label for="cnae" class="form-label">CNAE</label>
                          <input type="text" class="form-control" id="cnae" name="cnae" value="${
                            cliente.cnae || ''
                          }">
                        </div>
                        <div class="col-md-10">
                          <label for="descricao" class="form-label">Descrição</label>
                          <input type="text" class="form-control" id="descricao" name="descricao" value="${
                            truncateWords(cliente.descricao || '', 15)
                          }">
                        </div>
                      </div>
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="atividade" class="form-label">Atividade</label>
                          <select class="form-select" id="atividade" name="atividade">
                            <option value="">Selecione...</option>
                            <option value="Indústria" ${
                              cliente.atividade === 'Indústria ou Equiparado'
                                ? 'selected'
                                : ''
                            }>Indústria ou Equiparado</option>
                            <option value="Comércio Varejista" ${
                              cliente.atividade === 'Comércio Varejista'
                                ? 'selected'
                                : ''
                            }>Comércio Varejista</option>
                            <option value="Comércio Atacadista" ${
                              cliente.atividade === 'Comércio Atacadista'
                                ? 'selected'
                                : ''
                            }>Comércio Atacadista</option>
                            <option value="Distribuidor" ${
                              cliente.atividade === 'Distribuidor'
                                ? 'selected'
                                : ''
                            }>Distribuidor</option>
                            <option value="Produtor Rural" ${
                              cliente.atividade === 'Produtor Rural'
                                ? 'selected'
                                : ''
                            }>Produtor Rural</option>
                            <option value="Consumidor Final" ${
                              cliente.atividade === 'Consumidor Final'
                                ? 'selected'
                                : ''
                            }>Consumidor Final</option>
                            <option value="Órgão Público" ${
                              cliente.atividade === 'Órgão Público'
                                ? 'selected'
                                : ''
                            }>Órgão Público</option>
                            <option value="Serviço" ${
                              cliente.atividade === 'Serviço' ? 'selected' : ''
                            }>Serviço</option>
                            <option value="Não Contribuinte" ${
                              cliente.atividade === 'Não Contribuinte'
                                ? 'selected'
                                : ''
                            }>Não Contribuinte</option>
                          </select>
                        </div>
                        <div class="col-md-6">
                          <label for="destinacao" class="form-label">Destinação</label>
                          <select class="form-select" id="destinacao" name="destinacao">
                            <option value="">Selecione...</option>
                            <option value="Revenda" ${
                              cliente.destinacao === 'Revenda' ? 'selected' : ''
                            }>Revenda</option>
                            <option value="Industrialização" ${
                              cliente.destinacao === 'Industrialização'
                                ? 'selected'
                                : ''
                            }>Industrialização</option>
                            <option value="Uso e Consumo" ${
                              cliente.destinacao === 'Uso e Consumo'
                                ? 'selected'
                                : ''
                            }>Uso e Consumo</option>
                            <option value="Ativo Imobilizado" ${
                              cliente.destinacao === 'Ativo Imobilizado'
                                ? 'selected'
                                : ''
                            }>Ativo Imobilizado</option>
                          </select>
                        </div>
                      </div>                     
                      </div>
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="simples_nacional" name="simples_nacional" ${
                              cliente.simples_nacional ? 'checked' : ''
                            }>
                            <label class="form-check-label" for="simples_nacional">
                              Simples Nacional
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Tab Endereço -->
                    <div class="tab-pane fade" id="edit-endereco-content" role="tabpanel" aria-labelledby="edit-endereco-tab">
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="uf" class="form-label">Estado</label>
                          <input type="text" class="form-control" id="uf" name="uf" value="${
                            cliente.uf || ''
                          }">
                        </div>
                        <div class="col-md-6">
                          <label for="municipio" class="form-label">Município</label>
                          <input type="text" class="form-control" id="municipio" name="municipio" value="${
                            cliente.municipio || ''
                          }">
                        </div>
                      </div>
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="logradouro" class="form-label">Logradouro</label>
                          <input type="text" class="form-control" id="logradouro" name="logradouro" value="${
                            cliente.logradouro || ''
                          }">
                        </div>
                        <div class="col-md-6">
                          <label for="numero" class="form-label">Número</label>
                          <input type="text" class="form-control" id="numero" name="numero" value="${
                            cliente.numero || ''
                          }">
                        </div>
                      </div>
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="bairro" class="form-label">Bairro</label>
                          <input type="text" class="form-control" id="bairro" name="bairro" value="${
                            cliente.bairro || ''
                          }">
                        </div>
                        <div class="col-md-6">
                          <label for="cep" class="form-label">CEP</label>
                          <input type="text" class="form-control" id="cep" name="cep" value="${
                            cliente.cep || ''
                          }">
                        </div>
                      </div>
                    </div>
                  </div>
                                <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="update-cliente-btn">Salvar</button>
              </div>
                </form>
              </div>
            </div>
          </div>
        `;

        // Adicionar o modal ao body
        document.body.appendChild(modal);

        // Inicializar o modal
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();

        // Configurar evento para remover o modal quando for fechado
        modal.addEventListener('hidden.bs.modal', function () {
          document.body.removeChild(modal);
        });

        // Configurar botão de salvar
        const updateBtn = modal.querySelector('#update-cliente-btn');
        if (updateBtn) {
          updateBtn.addEventListener('click', function () {
            // Validar o formulário
            const form = modal.querySelector('#edit-cliente-form');
            if (!form.checkValidity()) {
              form.reportValidity();
              return;
            }

            // Obter os dados do formulário
            const formData = {
              cnpj: modal.querySelector('#cnpj').value,
              razao_social: modal.querySelector('#razao_social').value,
              inscricao_estadual: modal.querySelector('#inscricao_estadual')
                .value,
              atividade: modal.querySelector('#atividade').value,
              destinacao: modal.querySelector('#destinacao').value,
              natureza_juridica:
                modal.querySelector('#natureza_juridica').value,
              cnae: modal.querySelector('#cnae').value,
              descricao: modal.querySelector('#descricao').value,
              simples_nacional:
                modal.querySelector('#simples_nacional').checked,
              uf: modal.querySelector('#uf').value,
              municipio: modal.querySelector('#municipio').value,
              logradouro: modal.querySelector('#logradouro').value,
              numero: modal.querySelector('#numero').value,
              bairro: modal.querySelector('#bairro').value,
              cep: modal.querySelector('#cep').value,
            };

            // Enviar os dados para a API
            fetch(`/fiscal/api/clientes/${clienteId}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${localStorage.getItem('token')}`,
              },
              body: JSON.stringify(formData),
            })
              .then((response) => response.json())
              .then((data) => {
                if (data.message) {
                  alert(data.message);
                  // Fechar o modal
                  modalInstance.hide();
                  // Recarregar os dados de clientes
                  loadClientesData();
                } else {
                  alert('Erro ao atualizar cliente');
                }
              })
              .catch((error) => {
                alert('Erro ao atualizar cliente');
              });
          });
        }
      } else {
        alert('Erro ao carregar detalhes do cliente');
      }
    })
    .catch((error) => {
      alert('Erro ao carregar detalhes do cliente');
    });
}

/**
 * Abre o modal de edição em massa
 */
function openBulkEditModal() {
  // Obter IDs dos clientes selecionados
  const selectedIds = Array.from(
    document.querySelectorAll('.cliente-checkbox:checked'),
  ).map((checkbox) => checkbox.getAttribute('data-id'));

  if (selectedIds.length === 0) {
    alert('Selecione pelo menos um cliente para edição em massa');
    return;
  }

  // Criar modal para edição em massa
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.id = 'bulk-edit-modal';
  modal.tabIndex = '-1';
  modal.setAttribute('aria-labelledby', 'bulk-edit-modal-label');
  modal.setAttribute('aria-hidden', 'true');

  modal.innerHTML = `
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="bulk-edit-modal-label">Edição em Massa - ${selectedIds.length} Cliente(s)</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
        </div>
        <div class="modal-body">
          <form id="bulk-edit-form">
            <div class="mb-3">
              <label for="bulk-atividade" class="form-label">Atividade</label>
              <select class="form-select" id="bulk-atividade" name="atividade">
                <option value="">Não alterar</option>
                <option value="Indústria">Indústria ou Equiparado</option>
                <option value="Comércio Varejista">Comércio Varejista</option>
                <option value="Comércio Atacadista">Comércio Atacadista</option>
                <option value="Distribuidor">Distribuidor</option>
                <option value="Produtor Rural">Produtor Rural</option>
                <option value="Consumidor Final">Consumidor Final</option>
                <option value="Órgão Público">Órgão Público</option>
                <option value="Serviço">Serviço</option>
                <option value="Não Contribuinte">Não Contribuinte</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="bulk-destinacao" class="form-label">Destinação</label>
              <select class="form-select" id="bulk-destinacao" name="destinacao">
                <option value="">Não alterar</option>
                <option value="Revenda">Revenda</option>
                <option value="Industrialização">Industrialização</option>
                <option value="Uso e Consumo">Uso e Consumo</option>
                <option value="Ativo Imobilizado">Ativo Imobilizado</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="button" class="btn btn-primary" id="apply-bulk-edit-btn">Aplicar</button>
        </div>
      </div>
    </div>
  `;

  // Adicionar o modal ao body
  document.body.appendChild(modal);

  // Inicializar o modal
  const modalInstance = new bootstrap.Modal(modal);
  modalInstance.show();

  // Configurar evento para remover o modal quando for fechado
  modal.addEventListener('hidden.bs.modal', function () {
    document.body.removeChild(modal);
  });

  // Configurar botão de aplicar
  const applyBtn = modal.querySelector('#apply-bulk-edit-btn');
  if (applyBtn) {
    applyBtn.addEventListener('click', function () {
      // Obter os dados do formulário
      const atividade = modal.querySelector('#bulk-atividade').value;
      const destinacao = modal.querySelector('#bulk-destinacao').value;

      // Verificar se pelo menos um campo foi preenchido
      if (!atividade && !destinacao) {
        alert('Selecione pelo menos um campo para atualizar');
        return;
      }

      // Preparar dados para envio
      const updateData = {
        cliente_ids: selectedIds,
      };

      if (atividade) {
        updateData.atividade = atividade;
      }

      if (destinacao) {
        updateData.destinacao = destinacao;
      }

      // Enviar os dados para a API
      fetch('/fiscal/api/clientes/bulk-update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(updateData),
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.message) {
            alert(data.message);
            // Fechar o modal
            modalInstance.hide();
            // Recarregar os dados de clientes
            loadClientesData();
          } else {
            alert('Erro ao atualizar clientes');
          }
        })
        .catch((error) => {
          alert('Erro ao atualizar clientes');
        });
    });
  }
}

/**
 * Cria um modal para exibir os detalhes de um cliente
 */
function createClienteModal(cliente) {
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.id = 'cliente-modal';
  modal.tabIndex = '-1';
  modal.setAttribute('aria-labelledby', 'cliente-modal-label');
  modal.setAttribute('aria-hidden', 'true');

  modal.innerHTML = `
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="cliente-modal-label">Detalhes do Cliente</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="clienteTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="info-tab" data-bs-toggle="tab" data-bs-target="#info-content" type="button" role="tab" aria-controls="info-content" aria-selected="true">
                Informações Gerais
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="fiscal-tab" data-bs-toggle="tab" data-bs-target="#fiscal-content" type="button" role="tab" aria-controls="fiscal-content" aria-selected="false">
                Informações Fiscais
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="endereco-tab" data-bs-toggle="tab" data-bs-target="#endereco-content" type="button" role="tab" aria-controls="endereco-content" aria-selected="false">
                Endereço
              </button>
            </li>
          </ul>

          <div class="tab-content mt-3" id="clienteTabsContent">
            <!-- Tab Informações Gerais -->
            <div class="tab-pane fade show active" id="info-content" role="tabpanel" aria-labelledby="info-tab">
              <div class="row">
                <div class="col-md-6">
                  <p><strong>ID:</strong> ${cliente.id}</p>
                  <p><strong>CNPJ / CPF:</strong> ${cliente.cnpj}</p>
                  <p><strong>Razão Social:</strong> ${cliente.razao_social}</p>
                  <p><strong>Inscrição Estadual:</strong> ${
                    cliente.inscricao_estadual || '-'
                  }</p>
                  <p><strong>Status:</strong> ${
                    cliente.status === 'producao' ? 'Produção' : 'Novo'
                  }</p>
                </div>
                <div class="col-md-6">
                  <p><strong>Data Cadastro:</strong> ${
                    cliente.data_cadastro
                      ? new Date(cliente.data_cadastro).toLocaleDateString(
                          'pt-BR',
                        )
                      : '-'
                  }</p>
                </div>
              </div>
            </div>

            <!-- Tab Informações Fiscais -->
            <div class="tab-pane fade" id="fiscal-content" role="tabpanel" aria-labelledby="fiscal-tab">
              <div class="row">
                <div class="col-md-6">
                  <p><strong>CNAE:</strong> ${cliente.cnae || '-'}</p>
                  <p><strong>Atividade:</strong> ${cliente.atividade || '-'}</p>
                  <p><strong>Destinação:</strong> ${
                    cliente.destinacao || '-'
                  }</p>
                </div>
                <div class="col-md-6">
                  <p><strong>Simples Nacional:</strong> ${
                    cliente.simples_nacional ? 'Sim' : 'Não'
                  }</p>
                  <p><strong>Indicador IE Destinatário:</strong> ${
                    cliente.ind_ie_dest || '-'
                  }</p>
                  <p><strong>Indicador Consumidor Final:</strong> ${
                    cliente.ind_final || '-'
                  }</p>
                </div>
              </div>
            </div>

            <!-- Tab Endereço -->
            <div class="tab-pane fade" id="endereco-content" role="tabpanel" aria-labelledby="endereco-tab">
              <div class="row">
                <div class="col-md-6">
                  <p><strong>Logradouro:</strong> ${
                    cliente.logradouro || '-'
                  }</p>
                  <p><strong>Número:</strong> ${cliente.numero || '-'}</p>
                  <p><strong>Bairro:</strong> ${cliente.bairro || '-'}</p>
                </div>
                <div class="col-md-6">
                  <p><strong>Município:</strong> ${cliente.municipio || '-'}</p>
                  <p><strong>UF:</strong> ${cliente.uf || '-'}</p>
                  <p><strong>CEP:</strong> ${cliente.cep || '-'}</p>
                  <p><strong>País:</strong> ${cliente.pais || '-'}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
          ${
            cliente.status !== 'producao'
              ? `<button type="button" class="btn btn-success set-producao-modal-btn" data-id="${cliente.id}">
                  <i class="fas fa-check"></i> Definir como Produção
                </button>`
              : ''
          }
        </div>
      </div>
    </div>
  `;

  // Configurar botão de definir como produção
  setTimeout(() => {
    const setProducaoBtn = modal.querySelector('.set-producao-modal-btn');
    if (setProducaoBtn) {
      setProducaoBtn.addEventListener('click', function () {
        const clienteId = this.getAttribute('data-id');
        setClienteProducao(clienteId);

        // Fechar o modal
        const modalInstance = bootstrap.Modal.getInstance(modal);
        modalInstance.hide();
      });
    }
  }, 100);

  return modal;
}

/**
 * Define um cliente como produção
 */
function setClienteProducao(clienteId) {
  if (!confirm('Deseja realmente definir este cliente como produção?')) {
    return;
  }

  // Atualizar o status do cliente na API
  fetch(`/fiscal/api/clientes/${clienteId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    body: JSON.stringify({
      status: 'producao',
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.message) {
        alert(data.message);
        // Recarregar os dados de clientes
        loadClientesData();
      } else {
        alert('Erro ao atualizar status do cliente');
      }
    })
    .catch((error) => {
      alert('Erro ao atualizar status do cliente');
    });
}

/**
 * Cria um novo cliente
 */
function createNewCliente() {
  // Criar modal para o formulário de novo cliente
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.id = 'new-cliente-modal';
  modal.tabIndex = '-1';
  modal.setAttribute('aria-labelledby', 'new-cliente-modal-label');
  modal.setAttribute('aria-hidden', 'true');

  // Obter as opções para os selects
  const atividadeOptions = [
    'Indústria ou Equiparado',
    'Comércio Varejista',
    'Comércio Atacadista',
    'Distribuidor',
    'Produtor Rural',
    'Consumidor Final',
    'Não Contribuinte',
    'Órgão Público',
    'Serviços',
  ];

  const destinacaoOptions = [
    'Industrialização',
    'Revenda',
    'Ativo Imobilizado',
    'Uso e Consumo',
  ];

  // Criar as opções para os selects
  let atividadeOptionsHtml = '';
  atividadeOptions.forEach((option) => {
    atividadeOptionsHtml += `<option value="${option}">${option}</option>`;
  });

  let destinacaoOptionsHtml = '';
  destinacaoOptions.forEach((option) => {
    destinacaoOptionsHtml += `<option value="${option}">${option}</option>`;
  });

  modal.innerHTML = `
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="new-cliente-modal-label">Novo Participante</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
        </div>
        <div class="modal-body">
          <form id="new-cliente-form">
            <ul class="nav nav-tabs" id="newClienteTabs" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="nav-link active" id="new-info-tab" data-bs-toggle="tab" data-bs-target="#new-info-content" type="button" role="tab" aria-controls="new-info-content" aria-selected="true">
                  Informações Gerais
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="new-fiscal-tab" data-bs-toggle="tab" data-bs-target="#new-fiscal-content" type="button" role="tab" aria-controls="new-fiscal-content" aria-selected="false">
                  Informações Fiscais
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="new-endereco-tab" data-bs-toggle="tab" data-bs-target="#new-endereco-content" type="button" role="tab" aria-controls="new-endereco-content" aria-selected="false">
                  Endereço
                </button>
              </li>
            </ul>

            <div class="tab-content mt-3" id="newClienteTabsContent">
              <!-- Tab Informações Gerais -->
              <div class="tab-pane fade show active" id="new-info-content" role="tabpanel" aria-labelledby="new-info-tab">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="cnpj" class="form-label">CNPJ / CPF *</label>
                    <input type="text" class="form-control" id="cnpj" name="cnpj" required>
                  </div>
                  <div class="col-md-6">
                    <label for="razao_social" class="form-label">Razão Social *</label>
                    <input type="text" class="form-control" id="razao_social" name="razao_social" required>
                  </div>
                </div>
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="inscricao_estadual" class="form-label">Inscrição Estadual</label>
                    <input type="text" class="form-control" id="inscricao_estadual" name="inscricao_estadual">
                  </div>
                </div>
              </div>

              <!-- Tab Informações Fiscais -->
              <div class="tab-pane fade" id="new-fiscal-content" role="tabpanel" aria-labelledby="new-fiscal-tab">
                <div class="row mb-1">
                  <div class="col-md-12">
                    <label for="natureza_juridica" class="form-label">Natureza Jurídica</label>
                    <input type="text" class="form-control" id="natureza_juridica" name="natureza_juridica">
                  </div>
                </div>
                <div class="row mb-1">
                  <div class="col-md-2">
                    <label for="cnae" class="form-label">CNAE</label>
                    <input type="text" class="form-control" id="cnae" name="cnae">
                  </div>
                  <div class="col-md-10">
                    <label for="descricao" class="form-label">Descrição</label>
                    <input type="text" class="form-control" id="descricao" name="descricao">
                  </div>
                </div>
                <div class="row mb-1">
                  <div class="col-md-6">
                    <label for="atividade" class="form-label">Atividade</label>
                    <select class="form-select" id="atividade" name="atividade">
                      <option value="">Selecione...</option>
                      ${atividadeOptionsHtml}
                    </select>
                  </div>
                  <div class="col-md-6">
                    <label for="destinacao" class="form-label">Destinação</label>
                    <select class="form-select" id="destinacao" name="destinacao">
                      <option value="">Selecione...</option>
                      ${destinacaoOptionsHtml}
                    </select>
                  </div>
                </div>
                <div class="row mb-2">
                  <div class="col-md-6">
                    <div class="form-check mt-4">
                      <input class="form-check-input" type="checkbox" id="simples_nacional" name="simples_nacional">
                      <label class="form-check-label" for="simples_nacional">
                        Simples Nacional
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Tab Endereço -->
              <div class="tab-pane fade" id="new-endereco-content" role="tabpanel" aria-labelledby="new-endereco-tab">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="logradouro" class="form-label">Logradouro</label>
                    <input type="text" class="form-control" id="logradouro" name="logradouro">
                  </div>
                  <div class="col-md-6">
                    <label for="numero" class="form-label">Número</label>
                    <input type="text" class="form-control" id="numero" name="numero">
                  </div>
                </div>
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="bairro" class="form-label">Bairro</label>
                    <input type="text" class="form-control" id="bairro" name="bairro">
                  </div>
                  <div class="col-md-6">
                    <label for="municipio" class="form-label">Município</label>
                    <input type="text" class="form-control" id="municipio" name="municipio">
                  </div>
                </div>
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="uf" class="form-label">UF</label>
                    <input type="text" class="form-control" id="uf" name="uf" maxlength="2">
                  </div>
                  <div class="col-md-6">
                    <label for="cep" class="form-label">CEP</label>
                    <input type="text" class="form-control" id="cep" name="cep">
                  </div>
                </div>
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="pais" class="form-label">País</label>
                    <input type="text" class="form-control" id="pais" name="pais" value="BRASIL">
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="button" class="btn btn-primary" id="save-cliente-btn">Salvar</button>
        </div>
      </div>
    </div>
  `;

  // Adicionar o modal ao body
  document.body.appendChild(modal);

  // Inicializar o modal
  const modalInstance = new bootstrap.Modal(modal);
  modalInstance.show();

  // Configurar evento para remover o modal quando for fechado
  modal.addEventListener('hidden.bs.modal', function () {
    document.body.removeChild(modal);
  });

  // Configurar botão de salvar
  const saveBtn = modal.querySelector('#save-cliente-btn');
  if (saveBtn) {
    saveBtn.addEventListener('click', function () {
      // Validar o formulário
      const form = modal.querySelector('#new-cliente-form');
      if (!form.checkValidity()) {
        form.reportValidity();
        return;
      }

      // Obter os dados do formulário
      const formData = {
        cnpj: modal.querySelector('#cnpj').value,
        razao_social: modal.querySelector('#razao_social').value,
        inscricao_estadual: modal.querySelector('#inscricao_estadual').value,
        cnae: modal.querySelector('#cnae').value,
        descricao: modal.querySelector('#descricao').value,
        atividade: modal.querySelector('#atividade').value,
        destinacao: modal.querySelector('#destinacao').value,
        natureza_juridica: modal.querySelector('#natureza_juridica').value,
        simples_nacional: modal.querySelector('#simples_nacional').checked,
        logradouro: modal.querySelector('#logradouro').value,
        numero: modal.querySelector('#numero').value,
        bairro: modal.querySelector('#bairro').value,
        municipio: modal.querySelector('#municipio').value,
        uf: modal.querySelector('#uf').value,
        cep: modal.querySelector('#cep').value,
        pais: modal.querySelector('#pais').value,
        empresa_id: selectedCompany,
        status: 'novo',
      };

      // Enviar os dados para a API
      fetch('/fiscal/api/clientes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(formData),
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.message) {
            alert(data.message);
            // Fechar o modal
            modalInstance.hide();
            // Recarregar os dados de clientes
            loadClientesData();
          } else {
            alert('Erro ao criar cliente');
          }
        })
        .catch((error) => {
          alert('Erro ao criar cliente');
        });
    });
  }
}

/**
 * Configura as scrollbars superior e inferior da tabela de clientes
 */
function setupDualScrollbarsClientes() {
  const topScrollbar = document.getElementById('clientes-top-scrollbar');
  const topScrollbarContent = document.getElementById('clientes-top-scrollbar-content');
  const tableResponsive = document.getElementById('clientes-table-responsive');
  const table = document.getElementById('clientes-table');

  if (!topScrollbar || !topScrollbarContent || !tableResponsive || !table) {
    console.warn('Elementos de scrollbar não encontrados para clientes');
    return;
  }

  function updateTopScrollbarWidth() {
    const tableWidth = table.scrollWidth;
    const containerWidth = tableResponsive.clientWidth;
    if (tableWidth > containerWidth) {
      topScrollbar.style.display = 'block';
      topScrollbarContent.style.width = `${tableWidth}px`;
    } else {
      topScrollbar.style.display = 'none';
    }
  }

  function syncTopScrollbar() {
    if (topScrollbar.scrollLeft !== tableResponsive.scrollLeft) {
      topScrollbar.scrollLeft = tableResponsive.scrollLeft;
    }
  }

  function syncTableScroll() {
    if (tableResponsive.scrollLeft !== topScrollbar.scrollLeft) {
      tableResponsive.scrollLeft = topScrollbar.scrollLeft;
    }
  }

  tableResponsive.addEventListener('scroll', syncTopScrollbar);
  topScrollbar.addEventListener('scroll', syncTableScroll);

  updateTopScrollbarWidth();

  const resizeObserver = new ResizeObserver(() => {
    updateTopScrollbarWidth();
  });
  resizeObserver.observe(table);
  resizeObserver.observe(tableResponsive);

  clientesScrollbar = {
    resizeObserver,
    topScrollbar,
    tableResponsive,
    syncTopScrollbar,
    syncTableScroll,
  };
}

/**
 * Limita a quantidade de palavras exibidas
 * @param {string} text Texto a truncar
 * @param {number} limit Número máximo de palavras
 * @returns {string} Texto truncado
 */
function truncateWords(text, limit) {
  if (!text) return '';
  const words = text.split(/\s+/);
  if (words.length <= limit) {
    return text;
  }
  return words.slice(0, limit).join(' ') + '...';
}

/**
 * Remove configurações de scrollbars duplas da tabela de clientes
 */
function cleanupDualScrollbarsClientes() {
  if (clientesScrollbar) {
    const { resizeObserver, topScrollbar, tableResponsive, syncTopScrollbar, syncTableScroll } = clientesScrollbar;
    if (tableResponsive && syncTopScrollbar) {
      tableResponsive.removeEventListener('scroll', syncTopScrollbar);
    }
    if (topScrollbar && syncTableScroll) {
      topScrollbar.removeEventListener('scroll', syncTableScroll);
    }
    if (resizeObserver) {
      resizeObserver.disconnect();
    }
    clientesScrollbar = null;
  }
}
