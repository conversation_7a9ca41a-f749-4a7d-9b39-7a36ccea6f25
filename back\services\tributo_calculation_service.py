from models import db, Tributo, Produto, CenarioICMS, CenarioICMSST, CenarioIPI, CenarioPIS, CenarioCOFINS, CenarioDIFAL
from decimal import Decimal

class TributoCalculationService:
    """
    Serviço para cálculo de tributos com base nos cenários em produção
    """

    def __init__(self, empresa_id):
        """
        Inicializa o serviço de cálculo de tributos

        Args:
            empresa_id (int): ID da empresa
        """
        self.empresa_id = empresa_id

    def calculate_tributos(self, tributo_ids=None, produto_ids=None):
        """
        Calcula os valores de tributos com base nos cenários em produção

        Args:
            tributo_ids (list): Lista de IDs de tributos para calcular (opcional)
            produto_ids (list): Lista de IDs de produtos para calcular (opcional)

        Returns:
            dict: Resultado do cálculo
        """
        # Construir query base
        query = Tributo.query.filter_by(empresa_id=self.empresa_id)

        # Aplicar filtros
        if tributo_ids:
            query = query.filter(Tributo.id.in_(tributo_ids))
        if produto_ids:
            query = query.filter(Tributo.produto_id.in_(produto_ids))

        # Executar a query
        tributos = query.all()

        # Resultados
        results = {
            'total': len(tributos),
            'calculados': 0,
            'nao_calculados': 0,
            'por_tipo': {
                'icms': {'calculados': 0, 'nao_calculados': 0},
                'icms_st': {'calculados': 0, 'nao_calculados': 0},
                'ipi': {'calculados': 0, 'nao_calculados': 0},
                'pis': {'calculados': 0, 'nao_calculados': 0},
                'cofins': {'calculados': 0, 'nao_calculados': 0},
                'difal': {'calculados': 0, 'nao_calculados': 0}
            }
        }

        # Processar cada tributo
        for tributo in tributos:
            # Calcular para cada tipo de tributo
            tipos_tributo = ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal']
            all_calculated = True

            for tipo in tipos_tributo:
                # Verificar se o tributo tem valores para este tipo
                if self._has_tributo_values(tributo, tipo):
                    # Calcular valores com base no cenário em produção
                    calculated = self._calculate_tributo_values(tributo, tipo)

                    if calculated:
                        results['por_tipo'][tipo]['calculados'] += 1
                    else:
                        results['por_tipo'][tipo]['nao_calculados'] += 1
                        all_calculated = False

            # Atualizar contadores
            if all_calculated:
                results['calculados'] += 1
                # Atualizar status do produto para 'conforme'
                produto = db.session.get(Produto, tributo.produto_id)
                if produto:
                    produto.status = 'conforme'
            else:
                results['nao_calculados'] += 1

        # Salvar alterações
        db.session.commit()

        return results

    def _has_tributo_values(self, tributo, tipo_tributo):
        """
        Verifica se o tributo tem valores para um tipo específico

        Args:
            tributo (Tributo): Objeto do tributo
            tipo_tributo (str): Tipo de tributo

        Returns:
            bool: True se o tributo tem valores para o tipo, False caso contrário
        """
        if tipo_tributo == 'icms':
            return tributo.icms_valor is not None
        elif tipo_tributo == 'icms_st':
            return tributo.icms_st_valor is not None
        elif tipo_tributo == 'ipi':
            return tributo.ipi_valor is not None
        elif tipo_tributo == 'pis':
            return tributo.pis_valor is not None
        elif tipo_tributo == 'cofins':
            return tributo.cofins_valor is not None
        elif tipo_tributo == 'difal':
            return tributo.difal_v_icms_uf_dest is not None
        return False

    def _calculate_tributo_values(self, tributo, tipo_tributo):
        """
        Calcula os valores de um tributo com base no cenário em produção

        Args:
            tributo (Tributo): Objeto do tributo
            tipo_tributo (str): Tipo de tributo

        Returns:
            bool: True se o cálculo foi realizado, False caso contrário
        """
        # Obter o modelo correspondente
        CenarioModel = {
            'icms': CenarioICMS,
            'icms_st': CenarioICMSST,
            'ipi': CenarioIPI,
            'pis': CenarioPIS,
            'cofins': CenarioCOFINS,
            'difal': CenarioDIFAL
        }[tipo_tributo]

        # Buscar cenário em produção ativo
        cenario = CenarioModel.query.filter_by(
            empresa_id=tributo.empresa_id,
            cliente_id=tributo.cliente_id,
            produto_id=tributo.produto_id,
            status='producao',
            ativo=True
        ).first()

        # Se não encontrar cenário ativo, verificar se existe algum em produção
        if not cenario:
            cenario = CenarioModel.query.filter_by(
                empresa_id=tributo.empresa_id,
                cliente_id=tributo.cliente_id,
                produto_id=tributo.produto_id,
                status='producao'
            ).first()

        # Se não encontrar nenhum cenário em produção, não calcular
        if not cenario:
            return False

        # Calcular valores com base no cenário
        if tipo_tributo == 'icms':
            return self._calculate_icms(tributo, cenario)
        elif tipo_tributo == 'icms_st':
            return self._calculate_icms_st(tributo, cenario)
        elif tipo_tributo == 'ipi':
            return self._calculate_ipi(tributo, cenario)
        elif tipo_tributo == 'pis':
            return self._calculate_pis(tributo, cenario)
        elif tipo_tributo == 'cofins':
            return self._calculate_cofins(tributo, cenario)
        elif tipo_tributo == 'difal':
            return self._calculate_difal(tributo, cenario)

        return False

    def _calculate_icms(self, tributo, cenario):
        """Calcula ICMS e atualiza as colunas cenario_icms_* na tabela tributo"""
        try:
            # Usar o método estático para garantir consistência
            valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
            valor_ipi = Decimal(str(tributo.cenario_ipi_valor)) if tributo.cenario_ipi_valor else None
            valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
            valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None

            # Verificar se cliente é de uso e consumo ou ativo imobilizado
            cliente_uso_consumo_ativo = False
            if tributo.cliente and hasattr(tributo.cliente, 'destinacao'):
                cliente_uso_consumo_ativo = tributo.cliente.destinacao in ['uso_consumo', 'ativo_imobilizado']

            base_calculo, valor_icms = self.calcular_icms(
                valor_total,
                cenario,
                valor_ipi,
                cliente_uso_consumo_ativo,
                valor_frete,
                valor_desconto
            )

            # Atualizar valores no tributo
            tributo.cenario_icms_id = cenario.id
            tributo.cenario_icms_vbc = float(base_calculo)
            tributo.cenario_icms_valor = float(valor_icms)

            return True

        except Exception as e:
            return False

    def _calculate_icms_st(self, tributo, cenario):
        """Calcula ICMS-ST e atualiza as colunas cenario_icms_st_* na tabela tributo"""
        try:
            # Usar o método estático para garantir consistência
            valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
            valor_ipi = Decimal(str(tributo.cenario_ipi_valor)) if tributo.cenario_ipi_valor else None
            valor_icms = Decimal(str(tributo.cenario_icms_valor)) if tributo.cenario_icms_valor else None
            valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
            valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None

            # Verificar se cliente é de uso e consumo ou ativo imobilizado
            cliente_uso_consumo_ativo = False
            if tributo.cliente and hasattr(tributo.cliente, 'destinacao'):
                cliente_uso_consumo_ativo = tributo.cliente.destinacao in ['uso_consumo', 'ativo_imobilizado']

            base_calculo, valor_icms_st = self.calcular_icms_st(
                valor_total,
                cenario,
                valor_ipi,
                valor_icms,
                cliente_uso_consumo_ativo,
                valor_frete,
                valor_desconto
            )

            # Atualizar valores no tributo
            tributo.cenario_icms_st_id = cenario.id
            tributo.cenario_icms_st_vbc = float(base_calculo)
            tributo.cenario_icms_st_valor = float(valor_icms_st)

            return True

        except Exception as e:
            return False

    def _calculate_ipi(self, tributo, cenario):
        """Calcula IPI e atualiza as colunas cenario_ipi_* na tabela tributo"""
        try:
            # Usar o método estático para garantir consistência
            valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
            valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
            valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None

            base_calculo, valor_ipi = self.calcular_ipi(
                valor_total,
                cenario,
                valor_frete,
                valor_desconto
            )

            # Atualizar valores no tributo
            tributo.cenario_ipi_id = cenario.id
            tributo.cenario_ipi_vbc = float(base_calculo)
            tributo.cenario_ipi_valor = float(valor_ipi)

            return True

        except Exception as e:
            return False

    def _calculate_pis(self, tributo, cenario):
        """Calcula PIS e atualiza as colunas cenario_pis_* na tabela tributo"""
        try:
            # Usar o método estático para garantir consistência
            valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
            valor_icms = Decimal(str(tributo.cenario_icms_valor)) if tributo.cenario_icms_valor else None
            valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
            valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None

            base_calculo, valor_pis = self.calcular_pis(
                valor_total,
                cenario,
                valor_icms,
                valor_frete,
                valor_desconto
            )

            # Atualizar valores no tributo
            tributo.cenario_pis_id = cenario.id
            tributo.cenario_pis_vbc = float(base_calculo)
            tributo.cenario_pis_valor = float(valor_pis)

            return True

        except Exception as e:
            return False

    def _calculate_cofins(self, tributo, cenario):
        """Calcula COFINS e atualiza as colunas cenario_cofins_* na tabela tributo"""
        try:
            # Usar o método estático para garantir consistência
            valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
            valor_icms = Decimal(str(tributo.cenario_icms_valor)) if tributo.cenario_icms_valor else None
            valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
            valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None

            base_calculo, valor_cofins = self.calcular_cofins(
                valor_total,
                cenario,
                valor_icms,
                valor_frete,
                valor_desconto
            )

            # Atualizar valores no tributo
            tributo.cenario_cofins_id = cenario.id
            tributo.cenario_cofins_vbc = float(base_calculo)
            tributo.cenario_cofins_valor = float(valor_cofins)

            return True

        except Exception as e:
            return False

    def _calculate_difal(self, tributo, cenario):
        """Calcula DIFAL e atualiza as colunas cenario_difal_* na tabela tributo"""
        try:
            # Usar o método estático para garantir consistência
            valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')

            valor_difal = self.calcular_difal(
                valor_total,
                cenario
            )

            # Atualizar valores no tributo
            tributo.cenario_difal_id = cenario.id
            tributo.cenario_difal_valor = float(valor_difal)

            return True

        except Exception as e:
            return False

    # Métodos estáticos para cálculo de tributos (utilizados pelo serviço de auditoria)

    @staticmethod
    def calcular_ipi(valor_total, cenario_ipi, valor_frete=None, valor_desconto=None):
        """
        Calcula o IPI para um tributo

        Args:
            valor_total (Decimal): Valor total da mercadoria
            cenario_ipi (CenarioIPI): Cenário de IPI
            valor_frete (Decimal, optional): Valor do frete
            valor_desconto (Decimal, optional): Valor do desconto

        Returns:
            tuple: (base_calculo, valor_ipi)
        """
        # Verificar se o CST é 50 (tributado)
        if cenario_ipi.cst != '50':
            return Decimal('0.00'), Decimal('0.00')

        # Calcular base de cálculo (valor da mercadoria)
        base_calculo = Decimal(str(valor_total)) if valor_total else Decimal('0.00')

        # Subtrair desconto da base de cálculo se configurado no cenário
        if valor_desconto and hasattr(cenario_ipi, 'incluir_desconto') and cenario_ipi.incluir_desconto:
            base_calculo -= Decimal(str(valor_desconto))

        # Incluir frete na base de cálculo se configurado no cenário
        if valor_frete and hasattr(cenario_ipi, 'incluir_frete') and cenario_ipi.incluir_frete:
            base_calculo += Decimal(str(valor_frete))

        # Calcular valor do IPI
        aliquota = Decimal(str(cenario_ipi.aliquota)) if cenario_ipi.aliquota else Decimal('0.00')
        valor_ipi = (base_calculo * aliquota) / Decimal('100.00')


        return base_calculo, valor_ipi

    @staticmethod
    def calcular_icms(valor_total, cenario_icms, valor_ipi=None, cliente_uso_consumo_ativo=False, valor_frete=None, valor_desconto=None):
        """
        Calcula o ICMS para um tributo

        Args:
            valor_total (Decimal): Valor total da mercadoria
            cenario_icms (CenarioICMS): Cenário de ICMS
            valor_ipi (Decimal, optional): Valor do IPI
            cliente_uso_consumo_ativo (bool, optional): Indica se o cliente é de uso e consumo ou ativo imobilizado
            valor_frete (Decimal, optional): Valor do frete
            valor_desconto (Decimal, optional): Valor do desconto

        Returns:
            tuple: (base_calculo, valor_icms)
        """
        # Verificar se o CST é 00, 10, 20 ou 70 (tributado)
        csts_tributados = ['00', '10', '20', '70']
        if cenario_icms.cst not in csts_tributados:
            return Decimal('0.00'), Decimal('0.00')

        # Calcular base de cálculo
        base_calculo = Decimal(str(valor_total)) if valor_total else Decimal('0.00')

        # Subtrair desconto da base de cálculo se configurado no cenário
        if valor_desconto and hasattr(cenario_icms, 'incluir_desconto') and cenario_icms.incluir_desconto:
            base_calculo -= Decimal(str(valor_desconto))

        # Incluir frete na base de cálculo se configurado no cenário
        if valor_frete and hasattr(cenario_icms, 'incluir_frete') and cenario_icms.incluir_frete:
            base_calculo += Decimal(str(valor_frete))

        # Adicionar valor do IPI à base de cálculo se for cliente de uso e consumo ou ativo imobilizado
        if cliente_uso_consumo_ativo and valor_ipi:
            base_calculo += Decimal(str(valor_ipi))

        # Aplicar redução da base de cálculo, se houver
        if cenario_icms.p_red_bc and Decimal(str(cenario_icms.p_red_bc)) > Decimal('0.00'):
            valor_reducao = base_calculo * (Decimal(str(cenario_icms.p_red_bc)) / Decimal('100.00'))
            base_calculo -= valor_reducao

        # Calcular valor do ICMS
        aliquota = Decimal(str(cenario_icms.aliquota)) if cenario_icms.aliquota else Decimal('0.00')
        valor_icms = (base_calculo * aliquota) / Decimal('100.00')


        return base_calculo, valor_icms

    @staticmethod
    def calcular_icms_st(valor_total, cenario_icms_st, valor_ipi=None, valor_icms=None, cliente_uso_consumo_ativo=False, valor_frete=None, valor_desconto=None):
        """
        Calcula o ICMS-ST para um tributo

        Args:
            valor_total (Decimal): Valor total da mercadoria
            cenario_icms_st (CenarioICMSST): Cenário de ICMS-ST
            valor_ipi (Decimal, optional): Valor do IPI
            valor_icms (Decimal, optional): Valor do ICMS
            cliente_uso_consumo_ativo (bool, optional): Indica se o cliente é de uso e consumo ou ativo imobilizado
            valor_frete (Decimal, optional): Valor do frete
            valor_desconto (Decimal, optional): Valor do desconto

        Returns:
            tuple: (base_calculo, valor_icms_st)
        """
        # Verificar se o CST é 10 ou 70 (tributado com ST)
        csts_st = ['10', '70']
        if cenario_icms_st.cst not in csts_st:
            return Decimal('0.00'), Decimal('0.00')

        # Calcular base de cálculo
        base_calculo = Decimal(str(valor_total)) if valor_total else Decimal('0.00')

        # Subtrair desconto da base de cálculo se configurado no cenário
        if valor_desconto and hasattr(cenario_icms_st, 'incluir_desconto') and cenario_icms_st.incluir_desconto:
            base_calculo -= Decimal(str(valor_desconto))

        # Incluir frete na base de cálculo se configurado no cenário
        if valor_frete and hasattr(cenario_icms_st, 'incluir_frete') and cenario_icms_st.incluir_frete:
            base_calculo += Decimal(str(valor_frete))

        # Adicionar valor do IPI à base de cálculo se for cliente de uso e consumo ou ativo imobilizado
        if cliente_uso_consumo_ativo and valor_ipi:
            base_calculo += Decimal(str(valor_ipi))

        # Aplicar MVA
        if cenario_icms_st.icms_st_p_mva and Decimal(str(cenario_icms_st.icms_st_p_mva)) > Decimal('0.00'):
            base_calculo = base_calculo * (Decimal('1.00') + Decimal(str(cenario_icms_st.icms_st_p_mva)) / Decimal('100.00'))

        # Aplicar redução da base de cálculo, se houver e se o CST não for 10
        if cenario_icms_st.cst != '10' and cenario_icms_st.p_red_bc and Decimal(str(cenario_icms_st.p_red_bc)) > Decimal('0.00'):
            valor_reducao = base_calculo * (Decimal(str(cenario_icms_st.p_red_bc)) / Decimal('100.00'))
            base_calculo -= valor_reducao

        # Calcular valor do ICMS-ST
        aliquota = Decimal(str(cenario_icms_st.icms_st_aliquota)) if cenario_icms_st.icms_st_aliquota else Decimal('0.00')
        valor_icms_st_total = (base_calculo * aliquota) / Decimal('100.00')

        # Subtrair o valor do ICMS próprio
        valor_icms_st = valor_icms_st_total - Decimal(str(valor_icms)) if valor_icms else valor_icms_st_total


        return base_calculo, valor_icms_st

    @staticmethod
    def calcular_pis(valor_total, cenario_pis, valor_icms=None, valor_frete=None, valor_desconto=None):
        """
        Calcula o PIS para um tributo

        Args:
            valor_total (Decimal): Valor total da mercadoria
            cenario_pis (CenarioPIS): Cenário de PIS
            valor_icms (Decimal, optional): Valor do ICMS
            valor_frete (Decimal, optional): Valor do frete
            valor_desconto (Decimal, optional): Valor do desconto

        Returns:
            tuple: (base_calculo, valor_pis)
        """
        # Verificar se o CST é 1 ou 2 (tributado)
        csts_tributados = ['01', '02', '1', '2']
        if cenario_pis.cst not in csts_tributados:
            return Decimal('0.00'), Decimal('0.00')

        # Se o CST for 4, a alíquota deve ser 0
        if cenario_pis.cst in ['04', '4']:
            return Decimal('0.00'), Decimal('0.00')

        # Calcular base de cálculo
        base_calculo = Decimal(str(valor_total)) if valor_total else Decimal('0.00')

        # Subtrair desconto da base de cálculo se configurado no cenário
        if valor_desconto and hasattr(cenario_pis, 'incluir_desconto') and cenario_pis.incluir_desconto:
            base_calculo -= Decimal(str(valor_desconto))

        # Incluir frete na base de cálculo se configurado no cenário
        if valor_frete and hasattr(cenario_pis, 'incluir_frete') and cenario_pis.incluir_frete:
            base_calculo += Decimal(str(valor_frete))

        # Subtrair o valor do ICMS da base de cálculo
        if valor_icms:
            base_calculo -= Decimal(str(valor_icms))

        # Aplicar redução da base de cálculo, se houver
        if cenario_pis.p_red_bc and Decimal(str(cenario_pis.p_red_bc)) > Decimal('0.00'):
            base_calculo = base_calculo * (Decimal('1.00') - Decimal(str(cenario_pis.p_red_bc)) / Decimal('100.00'))

        # Calcular valor do PIS
        aliquota = Decimal(str(cenario_pis.aliquota)) if cenario_pis.aliquota else Decimal('0.00')
        valor_pis = (base_calculo * aliquota) / Decimal('100.00')


        return base_calculo, valor_pis

    @staticmethod
    def calcular_cofins(valor_total, cenario_cofins, valor_icms=None, valor_frete=None, valor_desconto=None):
        """
        Calcula o COFINS para um tributo

        Args:
            valor_total (Decimal): Valor total da mercadoria
            cenario_cofins (CenarioCOFINS): Cenário de COFINS
            valor_icms (Decimal, optional): Valor do ICMS
            valor_frete (Decimal, optional): Valor do frete
            valor_desconto (Decimal, optional): Valor do desconto

        Returns:
            tuple: (base_calculo, valor_cofins)
        """
        # Verificar se o CST é 1 ou 2 (tributado)
        csts_tributados = ['01', '02', '1', '2']
        if cenario_cofins.cst not in csts_tributados:
            return Decimal('0.00'), Decimal('0.00')

        # Se o CST for 4, a alíquota deve ser 0
        if cenario_cofins.cst in ['04', '4']:
            return Decimal('0.00'), Decimal('0.00')

        # Calcular base de cálculo
        base_calculo = Decimal(str(valor_total)) if valor_total else Decimal('0.00')

        # Subtrair desconto da base de cálculo se configurado no cenário
        if valor_desconto and hasattr(cenario_cofins, 'incluir_desconto') and cenario_cofins.incluir_desconto:
            base_calculo -= Decimal(str(valor_desconto))

        # Incluir frete na base de cálculo se configurado no cenário
        if valor_frete and hasattr(cenario_cofins, 'incluir_frete') and cenario_cofins.incluir_frete:
            base_calculo += Decimal(str(valor_frete))

        # Subtrair o valor do ICMS da base de cálculo
        if valor_icms:
            base_calculo -= Decimal(str(valor_icms))

        # Aplicar redução da base de cálculo, se houver
        if cenario_cofins.p_red_bc and Decimal(str(cenario_cofins.p_red_bc)) > Decimal('0.00'):
            base_calculo = base_calculo * (Decimal('1.00') - Decimal(str(cenario_cofins.p_red_bc)) / Decimal('100.00'))

        # Calcular valor do COFINS
        aliquota = Decimal(str(cenario_cofins.aliquota)) if cenario_cofins.aliquota else Decimal('0.00')
        valor_cofins = (base_calculo * aliquota) / Decimal('100.00')


        return base_calculo, valor_cofins

    @staticmethod
    def calcular_difal(valor_total, cenario_difal):
        """
        Calcula o DIFAL para um tributo

        Args:
            valor_total (Decimal): Valor total da mercadoria
            cenario_difal (CenarioDIFAL): Cenário de DIFAL

        Returns:
            Decimal: valor_difal
        """
        # Verificar se os campos necessários estão preenchidos
        if (not cenario_difal.p_icms_uf_dest or
            not cenario_difal.p_icms_inter or
            not valor_total):
            return Decimal('0.00')

        # Calcular base de cálculo
        base_calculo = Decimal(str(valor_total)) if valor_total else Decimal('0.00')

        # Aplicar redução da base de cálculo, se houver
        if cenario_difal.p_red_bc and Decimal(str(cenario_difal.p_red_bc)) > Decimal('0.00'):
            base_calculo = base_calculo * (Decimal('1.00') - Decimal(str(cenario_difal.p_red_bc)) / Decimal('100.00'))

        # Calcular diferença entre alíquotas
        aliquota_dest = Decimal(str(cenario_difal.p_icms_uf_dest))
        aliquota_inter = Decimal(str(cenario_difal.p_icms_inter))
        diferenca = aliquota_dest - aliquota_inter

        # Aplicar percentual de partilha, se houver
        if cenario_difal.p_icms_inter_part:
            diferenca = diferenca * Decimal(str(cenario_difal.p_icms_inter_part)) / Decimal('100.00')

        # Calcular valor do DIFAL
        valor_difal = (base_calculo * diferenca) / Decimal('100.00')

        # Calcular FCP, se houver
        if cenario_difal.p_fcp_uf_dest and Decimal(str(cenario_difal.p_fcp_uf_dest)) > Decimal('0.00'):
            fcp = (base_calculo * Decimal(str(cenario_difal.p_fcp_uf_dest))) / Decimal('100.00')
            valor_difal += fcp


        return valor_difal
