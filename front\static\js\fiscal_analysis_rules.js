/**
 * Regras de Análise Fiscal - Auditoria Comparativa
 * Sistema configurável para validação de dados fiscais
 */

// Configuração dos cards de análise
const ANALYSIS_CARDS_CONFIG = {
    cfop: {
        title: 'Análise CFOP',
        description: 'Inconsistências CFOP',
        icon: 'fas fa-code',
        color: 'danger',
        tributos: ['icms'] // Apenas ICMS
    },
    cfop_cst: {
        title: 'Análise CFOP x CST',
        description: 'CFOP vs CST divergentes',
        icon: 'fas fa-balance-scale',
        color: 'danger',
        tributos: ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'pis_cofins'] // Todos os tributos
    },
    product_type: {
        title: 'Tipo de Produto',
        description: 'CFOP vs Tipo inadequado',
        icon: 'fas fa-boxes',
        color: 'danger',
        tributos: ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'pis_cofins'] // Todos os tributos
    },
    origin: {
        title: 'Análise Origem',
        description: 'Origem SPED vs XML',
        icon: 'fas fa-map-marker-alt',
        color: 'danger',
        tributos: ['icms'] // Apenas ICMS
    },
    aliquota: {
        title: 'Análise Alíquota',
        description: 'Alíquota SPED vs XML',
        icon: 'fas fa-percentage',
        color: 'danger',
        tributos: ['icms', 'icms_st', 'ipi', 'pis', 'cofins'] // Todos os tributos
    },
    ipi: {
        title: 'Análise IPI',
        description: 'Validações específicas de IPI',
        icon: 'fas fa-cube',
        color: 'danger',
        tributos: ['ipi'] // Apenas IPI
    },
    pis_cofins: {
        title: 'Regras PIS/COFINS',
        description: 'Validações específicas de PIS/COFINS',
        icon: 'fas fa-receipt',
        color: 'danger',
        tributos: ['pis', 'cofins', 'pis_cofins']
    }
};

// Mapeamento de CFOPs de entrada para saída
const CFOP_ENTRADA_SAIDA_MAP = {
    // Industrialização
    '1901': ['5901'], // Entrada para industrialização por encomenda
    '1902': ['5902'], // Retorno de mercadoria remetida para industrialização por encomenda
    '1903': ['5903'], // Entrada de mercadoria remetida para industrialização e não aplicada
    '1904': ['5904'], // Retorno de remessa para venda fora do estabelecimento
    '1905': ['5905'], // Entrada de mercadoria para depósito fechado/armazém geral
    '1906': ['5906'], // Retorno de mercadoria de depósito fechado ou armazém geral
    '1907': ['5907'], // Retorno simbólico de mercadoria de depósito fechado ou armazém geral
    '1908': ['5908'], // Entrada de bem por contrato de comodato
    '1909': ['5909'], // Retorno de bem de comodato
    '1910': ['5910'], // Entrada de bonificação, doação ou brinde
    '1911': ['5911'], // Entrada de amostra grátis
    '1912': ['5912'], // Entrada de bem para demonstração
    '1913': ['5913'], // Retorno de bem de demonstração
    '1914': ['5914'], // Retorno de bem de exposição ou feira
    '1915': ['5915'], // Entrada para conserto ou reparo
    '1916': ['5916'], // Retorno de bem remetido para conserto ou reparo
    '1917': ['5917'], // Entrada de mercadoria em consignação
    '1918': ['5918'], // Devolução de mercadoria em consignação
    '1919': ['5919'], // Devolução simbólica de mercadoria em consignação
    '1920': ['5920'], // Entrada de vasilhame ou sacaria
    '1921': ['5921'], // Retorno de vasilhame ou sacaria
    '1922': ['5922', '5123'], // Simples faturamento por compra para recebimento futuro
    '1923': ['5923'], // Entrada do vendedor remetente, em venda à ordem
    '1924': ['5924'], // Entrada para industrialização por conta e ordem
    '1925': ['5925'], // Retorno de industrialização por conta e ordem
    '1926': ['5926'], // Reclassificação de mercadoria (formação/desagregação de kit)
    '1949': ['5949', '5929'], // Outra entrada de mercadoria ou prestação de serviço
    
    // Compras com ST
    '1401': ['5401', '5405'], // Compra de mercadoria com ST (tipo Matéria Prima)
    '1407': ['5401', '5405', '5929'], // Compra de mercadoria com ST (tipo uso e consumo)
    '1406': ['5401', '5405'], // Compra de mercadoria com ST (Tipo Ativo Imobilizado)
    '1403': ['5401', '5403','5405'], // Compra de mercadoria com ST (Tipo Revenda)
    '1411': ['5411','5410',], // Compra de mercadoria com ST (Tipo Revenda)
    
    // Compras para revenda/uso
    '1556': ['5101', '5102', '5103', '5106', '5929'], // Compra de material de uso e consumo (tipo uso e consumo)
    '1551': ['5101', '5102', '5103', '5106', '5929'], // Compra de material de uso e consumo (tipo Ativo Imobilizado)
    '1101': ['5101', '5102', '5103', '5106', '5929'], // Compra de material de uso e consumo (MATERIA PRIMA)
    '1102': ['5101', '5102', '5103', '5106', '5929'], // Compra de material de uso e consumo (tipo REVENDA)
    '1122': ['5122', '5123',], // Compra de material de uso e consumo (Materia Prima)
    '1124': ['5124',], // Compra de material de uso e consumo (Produto em processo
    '1125': ['5125',], // Compra de material de uso e consumo (Produto em processo
    '1117': ['5117','5116'], // Compra de material de uso e consumo (Mercadoria para Revenda)
    '1252': ['5252',], // Compra de material de uso e consumo (Outros)
    '1410': ['5410', '5411'], // Compra de material de uso e consumo (Produto Acabado)
    '1201': ['5201', '5202', '5556'], // Compra de material de uso e consumo (Produto Acabado)
    '1202': ['5201', '5202', '5556'], // Compra de material de uso e consumo (Mecadoria para Revenda)
    '1653': ['5653', '5656', '5929', '5655'], // Compra de material de uso e consumo (Uso consumo, materia prima)
    '1554': ['5555'], // Compra de material de uso e consumo (ativo imobilizado, uso e consumo)!
    '1555': ['5554'], // Compra de material de uso e consumo (ativo imobilizado, uso e consumo)!

};

// Regras de validação CFOP
const CFOP_VALIDATION_RULES = {
    // Regra 1: CFOPs SPED problemáticos com CFOP XML 1.9**
    invalidCfopCombinations: [
        {
            spedCfops: ['1101', '1102', '1401', '1407', '1556'],
            xmlPattern: /^1\.9\d{2}$/, //excessao 5929
            description: 'CFOP SPED não pode ter CFOP XML 1.9**'
        }
    ],

    // Regra 2: CFOP 1101 e 1102 com CFOP 5.40*
    invalidCfopSaidaCombinations: [
        {
            spedCfops: ['1101', '1102'],
            xmlPattern: /^5\.40/,
            description: 'CFOP 1101/1102 não pode ter CFOP XML 5.40*'
        }
    ],

    // Regra 3: Verificar se CFOP SPED tem correspondência válida com CFOP XML
    // Se CFOP SPED não tem correspondência válida, é inconsistente
    checkValidCorrespondence: true
};

// Regras de tipo de produto
const PRODUCT_TYPE_RULES = {
    // Tipos de produto
    types: {
        '01': 'Mercadoria para revenda',
        '02': 'Matéria-prima',
        '03': 'Embalagem',
        '04': 'Uso e consumo'
    },
    
    // CSTs problemáticos
    problematicCsts: ['10', '60', '70'],
    
    // Regras específicas
    rules: [
        {
            cfop: '1101',
            allowedCsts: [], // Não pode ter CST 10, 60, 70
            productTypes: ['02', '03'], // Matéria-prima/embalagem
            rule: 'not_allowed',
            description: 'CFOP 1101 não pode ter CST 10, 60, 70 para matéria prima/embalagem'
        },
        {
            cfop: '1401',
            allowedCsts: ['10', '60', '70'], // Só pode ter CST 10, 60, 70
            productTypes: ['02', '03'], // Matéria-prima/embalagem
            rule: 'only_allowed',
            description: 'CFOP 1401 só pode ter CST 10, 60, 70 para matéria prima/embalagem'
        },
        {
            cfop: '1556',
            allowedCsts: [], // Não pode ter CST 10, 60, 70
            productTypes: ['04'], // Uso e consumo
            rule: 'not_allowed',
            description: 'CFOP 1556 não pode ter CST 10, 60, 70 para uso e consumo'
        },
        {
            cfop: '1407',
            allowedCsts: ['10', '60', '70'], // Só pode ter CST 10, 60, 70
            productTypes: ['04'], // Uso e consumo
            rule: 'only_allowed',
            description: 'CFOP 1407 só pode ter CST 10, 60, 70 para uso e consumo'
        },
        {
            cfop: '1102',
            allowedCsts: [], // Não pode ter CST 10, 60, 70
            productTypes: ['01'], // Revenda
            rule: 'not_allowed',
            description: 'CFOP 1102 não pode ter CST 10, 60, 70 para revenda'
        },
        {
            cfop: '1403',
            allowedCsts: ['10', '60', '70'], // Só pode ter CST 10, 60, 70
            productTypes: ['01'], // Revenda
            rule: 'only_allowed',
            description: 'CFOP 1403 só pode ter CST 10, 60, 70 para revenda'
        }
    ]
};

// Regras específicas de CST por tipo de produto (apenas ICMS)
const ICMS_CST_RULES = [
    {
        productTypes: ['04'], // Uso e consumo
        xmlCsts: ['00', '20'],
        spedCst: '90',
        description: 'Uso e consumo: CST XML 00 ou 20 deve ter CST SPED 90'
    },
    {
        productTypes: ['02', '04'], // Matéria-prima ou uso e consumo
        xmlCsts: ['61'],
        spedCst: '02',
        description: 'CST XML 61 pode ter CST SPED 02 para matéria-prima ou uso e consumo'
    }
];

// Regras de origem
const ORIGIN_VALIDATION_RULES = {
    // Exceções permitidas
    exceptions: [
        {
            spedOrigin: '2',
            xmlOrigin: '1',
            description: 'Origem 2 no SPED pode ter origem 1 na XML'
        }
    ],

    // Regra geral: origens devem ser iguais
    generalRule: 'origins_must_match'
};

// Regras de alíquota
const ALIQUOTA_VALIDATION_RULES = {
    // Tipos de produto que devem ter alíquota igual
    equalAliquotaTypes: ['01', '02', '03'], // Revenda, Matéria-prima, Embalagem

    // Tipos de produto que devem ter alíquota SPED = 0
    zeroAliquotaTypes: ['04'], // Uso e consumo

    // Tolerância para comparação de alíquotas (diferença mínima considerada)
    tolerance: 0.01,

    rules: [
        {
            productTypes: ['01', '02', '03'], // Revenda, Matéria-prima, Embalagem
            rule: 'aliquotas_must_match',
            description: 'Alíquota SPED deve ser igual à alíquota XML para produtos de revenda, matéria-prima e embalagem'
        },
        {
            productTypes: ['04'], // Uso e consumo
            rule: 'sped_aliquota_must_be_zero',
            description: 'Alíquota SPED deve ser 0 para produtos de uso e consumo'
        }
    ]
};

// Regras de alíquota específicas por CFOP
const CFOP_ALIQUOTA_RULES = [
    {
        cfop: '1401',
        validSpedAliquotas: [18, 12],
        xmlMustBeZero: true,
        description: 'CFOP 1401: alíquota SPED deve ser 18% ou 12% e XML igual a 0%'
    }
];

// Regras específicas de IPI
const IPI_RULES = [
    {
        description: 'Alíquota ou valor IPI divergente do XML',
        analysis: 'aliquota',
        check: auditoria => {
            const xmlValor = parseFloat(auditoria.xml_data?.ipi_valor || auditoria.xml_ipi_valor);
            if (isNaN(xmlValor)) return false;
            const spedValor = parseFloat(auditoria.tributos?.ipi?.valor || 0);
            const xmlAliq = parseFloat(auditoria.xml_data?.ipi_aliquota || auditoria.xml_ipi_aliquota);
            const spedAliq = parseFloat(auditoria.tributos?.ipi?.aliquota || 0);
            return Math.abs(spedValor - xmlValor) > 0.01 || Math.abs(spedAliq - xmlAliq) > 0.01;
        }
    },
    {
        description: 'CST IPI deve ser 00 para matéria-prima ou embalagem com valor de imposto',
        analysis: 'ipi',
        check: a => {
            const xmlValor = parseFloat(a.xml_data?.ipi_valor || a.xml_ipi_valor);
            if (isNaN(xmlValor)) return false;
            return ['01', '02'].includes(a.tipo_produto) && a.tributos?.ipi?.cst !== '00';
        }
    },
    {
        description: 'CST IPI deve ser 49 para revenda, uso e consumo ou ativo imobilizado com valor de imposto',
        analysis: 'ipi',
        check: a => {
            const xmlValor = parseFloat(a.xml_data?.ipi_valor || a.xml_ipi_valor);
            if (isNaN(xmlValor)) return false;
            return ['00', '07', '08'].includes(a.tipo_produto) && a.tributos?.ipi?.cst !== '49';
        }
    },
    {
        description: 'CST IPI deve ser 00 ou 49 para tipo outros com valor de imposto',
        analysis: 'ipi',
        check: a => {
            const xmlValor = parseFloat(a.xml_data?.ipi_valor || a.xml_ipi_valor);
            if (isNaN(xmlValor)) return false;
            return ['99', '10'].includes(a.tipo_produto) && !['00', '49'].includes(a.tributos?.ipi?.cst);
        }
    },
    {
        description: 'CFOP 1201/1202/1410/1411 exige CST IPI 00 quando há valor de imposto',
        analysis: 'cfop_cst',
        check: a => {
            const xmlValor = parseFloat(a.xml_data?.ipi_valor || a.xml_ipi_valor);
            if (isNaN(xmlValor)) return false;
            return ['1201', '1202', '1410', '1411'].includes(a.sped_cfop) && a.tributos?.ipi?.cst !== '00';
        }
    },
    {
        description: 'CST XML 55 exige CST SPED 05',
        check: a => {
            const cstXml = a.xml_data?.ipi_cst || a.xml_ipi_cst;
            return cstXml === '55' && ['01', '02', '04'].includes(a.tipo_produto) && !a.tributos?.ipi?.cst === '05';
        }
    },
    {
        description: 'CST XML 51 exige CST SPED 01',
        analysis: 'cfop_cst',
        check: a => {
            const cstXml = a.xml_data?.ipi_cst || a.xml_ipi_cst;
            return cstXml === '51' && ['01', '02', '04'].includes(a.tipo_produto) && !a.tributos?.ipi?.cst === '01';
        }
    },
    {
        description: 'CST XML 50 exige CST SPED 00',
        analysis: 'cfop_cst',
        check: a => {
            const cstXml = a.xml_data?.ipi_cst || a.xml_ipi_cst;
            return cstXml === '50' && ['01', '02', '04'].includes(a.tipo_produto) && !a.tributos?.ipi?.cst === '00';
        }
    },
    {
        description: 'Uso e consumo ou ativo imobilizado deve ter CST IPI 49',
        analysis: 'ipi',
        check: a => ['07', '08'].includes(a.tipo_produto) && a.tributos?.ipi?.cst !== '49'
    },
    {
        description: 'NCM com IPI NT exige CST 03',
        analysis: 'ipi',
        check: a => {
            const cst = a.tributos?.ipi?.cst;
            if (!a.is_ncm_nt) return false;
            return ['01', '02', '04'].includes(a.tipo_produto) && cst !== '03';
        }
    },
    {
        description: 'Tipo outros sem valor de imposto deve usar CST 49',
        analysis: 'ipi',
        check: a => {
            const xmlValor = parseFloat(a.xml_data?.ipi_valor || a.xml_ipi_valor);
            if (!isNaN(xmlValor)) return false;
            return ['99', '10'].includes(a.tipo_produto) && a.tributos?.ipi?.cst !== '49';
        }
    },
    {
        description: 'CFOP 1556: Para XML de entrada (grupo 1xx) ou devolução (5.929), o CST SPED deve ser 90 e o CST/CSOSN XML não pode ser de tributação.',
        analysis: 'cfop_cst',
        check: a => {
            const spedCfop = a.sped_cfop;
            const xmlCfop = a.xml_cfop;

            if (spedCfop !== '1556') return false;

            // Checa se o CFOP XML é do grupo 1xx (ex: 1.101, 2.102) ou 5.929
            const xmlCfopPattern = /^[1-7]\.1\d{2}$/;
            if (!xmlCfopPattern.test(xmlCfop) && xmlCfop !== '5.929') return false;

            const spedCst = a.tributos?.icms?.cst;
            const xmlCst = a.xml_cst || a.xml_data?.cst;
            const xmlCsosn = a.xml_csosn || a.xml_data?.csosn;

            const invalidXmlCst = ['10', '60', '70'].includes(xmlCst);
            const invalidXmlCsosn = xmlCsosn === '15';
            const incorrectSpedCst = spedCst !== '90';

            return invalidXmlCst || invalidXmlCsosn || incorrectSpedCst;
        }
    }
];

// Mapeamento simples das regras de PIS/COFINS enviadas pelo backend
const PIS_COFINS_RULES_MAP = {
    LP_CFOP_98: 'CFOP iniciado em 1.9, 1201, 1202, 1410 ou 1411 deve usar CST 98 (Lucro Presumido)',
    LP_5123_1922_70: 'CFOP XML 5123 e CFOP SPED 1922 deve usar CST 70 (Lucro Presumido)',
    LP_CST_70: 'CFOP diferente de 1.9 deve usar CST 70 (Lucro Presumido)',
    LR_CST_50_56: 'Tipo de produto obriga CST 50 ou 56 (Lucro Real)',
    LR_EPI_50_56: 'Uso consumo EPI ou manutenção deve usar CST 50 ou 56 (Lucro Real)',
    LR_USO_CONSUMO_70: 'Uso consumo sem EPI/Manut. deve usar CST 70 (Lucro Real)',
    LR_CST_70: 'Uso consumo ou ativo imobilizado deve usar CST 70 (Lucro Real)',
    LR_ALIQ_50_56: 'CST 50/56 exige alíquotas 1,65% e 7,60% (Lucro Real)',
    LR_DEVOLUCAO_ALIQ: 'CFOP de devolução com CST 50/56 exige alíquotas permitidas (Lucro Real)',
    LR_DEVOLUCAO_98: 'CFOP de devolução com finNFe=4 e ind_oper/ind_emit=0 deve usar CST 98 e alíquota 0 (Lucro Real)',
    LR_CFOP_98: 'CFOP iniciado em 1.9 deve usar CST 98 (Lucro Real)',
    LR_5123_1922: 'CFOP 5123 x 1922 deve usar CST 50/56 e alíquota 1,65% e 7,60% (Lucro Real)',
    LR_1252_ALIQ: 'CFOP 1252 deve usar CST 50/56 e alíquota 1,65% e 7,60% (Lucro Real)'
};

function getPisCofinsViolations(auditoria) {
    const codes = auditoria.regras_pis_cofins || [];
    return codes.map(c => PIS_COFINS_RULES_MAP[c] || c);
}

function hasPisCofinsViolation(auditoria) {
    return (auditoria.regras_pis_cofins || []).length > 0;
}

function isNcmNT(ncm) {
    return false; // Mantido para compatibilidade
}

/**
 * Verifica inconsistências de CFOP
 */
function isCfopInconsistent(spedCfop, xmlCfop, tipoItem = '') {
    if (!spedCfop || !xmlCfop) return false;

    // Regra especial: CFOP XML 5405 para matéria-prima ou embalagem
    if (xmlCfop === '5405' && ['02', '03'].includes(tipoItem)) {
        return spedCfop !== '1401';
    }

    // Verificar combinações inválidas com padrão 1.9**
    for (const rule of CFOP_VALIDATION_RULES.invalidCfopCombinations) {
        if (rule.spedCfops.includes(spedCfop) && rule.xmlPattern.test(xmlCfop)) {
            return true;
        }
    }

    // Verificar combinações inválidas com padrão 5.40*
    for (const rule of CFOP_VALIDATION_RULES.invalidCfopSaidaCombinations) {
        if (rule.spedCfops.includes(spedCfop) && rule.xmlPattern.test(xmlCfop)) {
            return true;
        }
    }

    // Verificar se CFOP SPED tem correspondência válida com CFOP XML
    if (CFOP_VALIDATION_RULES.checkValidCorrespondence) {
        const validXmlCfops = CFOP_ENTRADA_SAIDA_MAP[spedCfop];
        if (validXmlCfops && !validXmlCfops.includes(xmlCfop)) {
            return true; // CFOP XML não está na lista de correspondências válidas
        }
    }

    return false;
}

/**
 * Verifica inconsistências de CFOP x CST
 */
function isCfopCstInconsistent(auditoria) {
    const { sped_cfop, xml_cfop, tributos, xml_cst, xml_data, tipo_produto } = auditoria;
    const spedCst = tributos?.icms?.cst;
    const xmlCstValue = xml_cst || xml_data?.cst;

    if (!sped_cfop || !spedCst || !xmlCstValue) return false;

    // Regra especial: CFOP XML 5405 para matéria-prima ou embalagem
    if (xml_cfop === '5405' && ['02', '03'].includes(tipo_produto)) {
        if (sped_cfop !== '1401') {
            return false; // Erro de CFOP, não de CST
        }
        return !['00', '20'].includes(spedCst);
    }

    // Aplicar regras especiais de CST (ICMS)
    const specialIcmsRule = ICMS_CST_RULES.find(rule =>
        rule.productTypes.includes(tipo_produto) && rule.xmlCsts.includes(xmlCstValue)
    );
    if (specialIcmsRule) {
        const allowed = Array.isArray(specialIcmsRule.spedCst) ? specialIcmsRule.spedCst : [specialIcmsRule.spedCst];
        return !allowed.includes(spedCst);
    }

    // Verifica regras complexas de IPI e a nova regra de CFOP/CSOSN
    if (getIpiRuleViolationByAnalysis('cfop_cst', auditoria)) {
        return true;
    }

    return false;
}

/**
 * Verifica inconsistências de tipo de produto
 */
function isProductTypeInconsistent(spedCfop, spedCst, tipoItem) {
    if (!spedCfop || !spedCst || !tipoItem) return false;

    // Buscar regra aplicável
    const applicableRule = PRODUCT_TYPE_RULES.rules.find(rule => 
        rule.cfop === spedCfop && rule.productTypes.includes(tipoItem)
    );

    if (!applicableRule) return false;

    const isProblematicCst = PRODUCT_TYPE_RULES.problematicCsts.includes(spedCst);

    if (applicableRule.rule === 'not_allowed') {
        // Não pode ter CST problemático
        return isProblematicCst;
    } else if (applicableRule.rule === 'only_allowed') {
        // Só pode ter CST problemático
        return !isProblematicCst;
    }

    return false;
}

/**
 * Verifica inconsistências de origem
 */
function isOriginInconsistent(spedOrigem, xmlOrigem) {
    if (!spedOrigem || !xmlOrigem) return false;

    // Verificar exceções
    for (const exception of ORIGIN_VALIDATION_RULES.exceptions) {
        if (spedOrigem === exception.spedOrigin && xmlOrigem === exception.xmlOrigin) {
            return false;
        }
    }

    // Regra geral: origens devem ser iguais
    return spedOrigem !== xmlOrigem;
}

/**
 * Verifica inconsistências de alíquota
 */
function isAliquotaInconsistent(spedAliquota, xmlAliquota, tipoItem, spedCfop = '') {
    if (!tipoItem) return false;

    // Converter para números
    const spedAliq = parseFloat(spedAliquota) || 0;
    const xmlAliq = parseFloat(xmlAliquota) || 0;

    // Regras específicas por CFOP
    const cfopRule = CFOP_ALIQUOTA_RULES.find(r => r.cfop === spedCfop);
    if (cfopRule) {
        const spedOk = cfopRule.validSpedAliquotas.map(a => parseFloat(a)).includes(spedAliq);
        const xmlOk = cfopRule.xmlMustBeZero ? xmlAliq <= ALIQUOTA_VALIDATION_RULES.tolerance : true;
        return !(spedOk && xmlOk);
    }

    // Buscar regra aplicável
    const applicableRule = ALIQUOTA_VALIDATION_RULES.rules.find(rule =>
        rule.productTypes.includes(tipoItem)
    );

    if (!applicableRule) return false;

    if (applicableRule.rule === 'aliquotas_must_match') {
        // Alíquotas devem ser iguais (com tolerância)
        return Math.abs(spedAliq - xmlAliq) > ALIQUOTA_VALIDATION_RULES.tolerance;
    } else if (applicableRule.rule === 'sped_aliquota_must_be_zero') {
        // Alíquota SPED deve ser zero
        return spedAliq > ALIQUOTA_VALIDATION_RULES.tolerance;
    }

    return false;
}

/**
 * Retorna descrição da regra de IPI violada ou string vazia
 */
function getIpiRuleViolation(auditoria) {
    return getIpiRuleViolationByAnalysis('ipi', auditoria);
}

function getIpiRuleViolationByAnalysis(analysisType, auditoria) {
    for (const rule of IPI_RULES) {
        if (rule.analysis === analysisType && rule.check(auditoria)) {
            return rule.description;
        }
    }
    return '';
}

function isIpiInconsistent(auditoria) {
    return getIpiRuleViolationByAnalysis('ipi', auditoria) !== '';
}


/**
 * Obtém descrição da regra violada
 */
function getRuleViolationDescription(analysisType, auditoria) {
    const spedCfop = auditoria.sped_cfop;
    const xmlCfop = auditoria.xml_cfop;
    const spedCst = auditoria.tributos?.icms?.cst;
    const xmlCst = auditoria.xml_cst || auditoria.xml_data?.cst;

    // Tipo de produto utilizado em diversas validações
    const tipoItem = auditoria.tipo_produto;
    const spedOrigem = auditoria.sped_origem;
    const xmlOrigem = auditoria.xml_origem || auditoria.xml_data?.origem;

    switch (analysisType) {
        case 'cfop':
            // Verificar qual regra foi violada
            for (const rule of CFOP_VALIDATION_RULES.invalidCfopCombinations) {
                if (rule.spedCfops.includes(spedCfop) && rule.xmlPattern.test(xmlCfop)) {
                    return rule.description;
                }
            }
            for (const rule of CFOP_VALIDATION_RULES.invalidCfopSaidaCombinations) {
                if (rule.spedCfops.includes(spedCfop) && rule.xmlPattern.test(xmlCfop)) {
                    return rule.description;
                }
            }

            if (xmlCfop === '5405' && ['02', '03'].includes(auditoria.tipo_produto) && spedCfop !== '1401') {
                return 'CFOP SPED deve ser 1401 para CFOP NOTA 5405 de matéria-prima ou embalagem'
            }
            
            // Verificar correspondência válida
            const validXmlCfops = CFOP_ENTRADA_SAIDA_MAP[spedCfop];
            if (validXmlCfops && !validXmlCfops.includes(xmlCfop)) {
                return `CFOP SPED (${spedCfop}) incompatível com CFOP XML (${xmlCfop}). CFOPs válidos: ${validXmlCfops.join(', ')}`;
            }
            break;
            
        case 'cfop_cst':
            const cstRule = ICMS_CST_RULES.find(r =>
                r.productTypes.includes(tipoItem) && r.xmlCsts.includes(xmlCst)
            );
            if (cstRule) {
                return cstRule.description;
            }
            if (xmlCfop === '5405' && ['02', '03'].includes(auditoria.tipo_produto) && spedCfop === '1401' && !['00', '20'].includes(spedCst)) {
                return 'CST SPED deve ser 00 ou 20 para CFOP SPED 1401 com CFOP XML 5405 de matéria-prima ou embalagem';
            }
            const ipiCfopCst = getIpiRuleViolationByAnalysis('cfop_cst', auditoria);
            if (ipiCfopCst) return ipiCfopCst;
            return `Regra de CST não encontrada para esta combinação`;
            
        case 'product_type':
            const rule = PRODUCT_TYPE_RULES.rules.find(r => 
                r.cfop === spedCfop && r.productTypes.includes(tipoItem)
            );
            return rule ? rule.description : 'Inconsistência de tipo de produto';
            
        case 'origin':
            return `Origem SPED (${spedOrigem}) incompatível com origem XML (${xmlOrigem})`;

        case 'aliquota':
            // Obter alíquotas baseado no contexto atual
            let spedAliq = 0, xmlAliq = 0;

            // Tentar obter do tributo ICMS primeiro (mais comum)
            if (auditoria.tributos?.icms?.aliquota !== undefined) {
                spedAliq = parseFloat(auditoria.tributos.icms.aliquota) || 0;
                xmlAliq = parseFloat(auditoria.xml_data?.icms_aliquota || auditoria.xml_icms_aliquota) || 0;
            } else if (auditoria.tributos?.pis?.aliquota !== undefined) {
                spedAliq = parseFloat(auditoria.tributos.pis.aliquota) || 0;
                xmlAliq = parseFloat(auditoria.xml_data?.pis_aliquota || auditoria.xml_pis_aliquota) || 0;
            } else if (auditoria.tributos?.cofins?.aliquota !== undefined) {
                spedAliq = parseFloat(auditoria.tributos.cofins.aliquota) || 0;
                xmlAliq = parseFloat(auditoria.xml_data?.cofins_aliquota || auditoria.xml_cofins_aliquota) || 0;
            }

            const cfopAliqRule = CFOP_ALIQUOTA_RULES.find(r => r.cfop === spedCfop);
            if (cfopAliqRule) {
                return cfopAliqRule.description;
            }
            if (ALIQUOTA_VALIDATION_RULES.zeroAliquotaTypes.includes(tipoItem)) {
                return `Alíquota SPED (${spedAliq.toFixed(2)}%) deve ser 0% para uso e consumo`;
            }
            const ipiAliq = getIpiRuleViolationByAnalysis('aliquota', auditoria);
            if (ipiAliq) return ipiAliq;
            return `Alíquota SPED (${spedAliq.toFixed(2)}%) diferente da alíquota XML (${xmlAliq.toFixed(2)}%)`;
        case 'ipi':
            return getIpiRuleViolation(auditoria);
    }

    return 'Inconsistência detectada';
}

// Exportar para uso global
if (typeof window !== 'undefined') {
    window.FiscalAnalysisRules = {
        ANALYSIS_CARDS_CONFIG,
        CFOP_ENTRADA_SAIDA_MAP,
        CFOP_VALIDATION_RULES,
        PRODUCT_TYPE_RULES,
        ICMS_CST_RULES,
        ORIGIN_VALIDATION_RULES,
        ALIQUOTA_VALIDATION_RULES,
        CFOP_ALIQUOTA_RULES,
        IPI_RULES,
        PIS_COFINS_RULES_MAP,
        isCfopInconsistent,
        isCfopCstInconsistent,
        isProductTypeInconsistent,
        isOriginInconsistent,
        isAliquotaInconsistent,
        isIpiInconsistent,
        hasPisCofinsViolation,
        getPisCofinsViolations,
        getIpiRuleViolation,
        getIpiRuleViolationByAnalysis,
        getRuleViolationDescription
    };
}
