/**
 * CSS específico para Auditoria Comparativa
 * Estilos isolados para não afetar outros componentes
 */

/* === PROGRESS STATS LAYOUT === */
.auditoria-progress-stats {
    margin-top: 1rem;
}

.auditoria-stats-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 1rem;
}

.auditoria-stat-item {
    flex: 1;
    min-width: 120px;
    text-align: center;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.auditoria-stat-item:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.auditoria-stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.auditoria-stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin: 0;
}

/* Cores específicas para cada tipo de estatística */
.auditoria-stat-item.stat-notas .auditoria-stat-value {
    color: #0d6efd;
}

.auditoria-stat-item.stat-matches .auditoria-stat-value {
    color: #198754;
}

.auditoria-stat-item.stat-tempo .auditoria-stat-value {
    color: #0dcaf0;
}

.auditoria-stat-item.stat-atual .auditoria-stat-value {
    color: #fd7e14;
}

/* === RESPONSIVIDADE === */
@media (max-width: 768px) {
    .auditoria-stats-container {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }
    
    .auditoria-stat-item {
        width: 100%;
        max-width: 280px;
        min-width: unset;
    }
}

@media (max-width: 576px) {
    .auditoria-stat-item {
        padding: 0.5rem;
        max-width: 100%;
    }
    
    .auditoria-stat-value {
        font-size: 1.1rem;
    }
    
    .auditoria-stat-label {
        font-size: 0.8rem;
    }
}

/* === PROGRESS BAR CUSTOMIZATIONS === */
.auditoria-progress-container {
    margin-bottom: 1.5rem;
}

.auditoria-progress-container .card-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    color: white;
    border-bottom: none;
}

.auditoria-progress-container .card-header h6 {
    margin: 0;
    font-weight: 600;
}

.auditoria-progress-container .progress {
    height: 24px;
    background-color: #e9ecef;
    border-radius: 12px;
    overflow: hidden;
}

.auditoria-progress-details {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* === ANIMATIONS === */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
    100% {
        opacity: 1;
    }
}

.auditoria-stat-item.updating {
    animation: pulse 1s infinite;
}

/* === UTILITY CLASSES === */
.auditoria-text-primary {
    color: #0d6efd !important;
}

.auditoria-text-success {
    color: #198754 !important;
}

.auditoria-text-info {
    color: #0dcaf0 !important;
}

.auditoria-text-warning {
    color: #fd7e14 !important;
}

/* === MATCHING MANUAL STYLES === */

/* Estilos para Matching Manual */
.xml-candidate-item {
    cursor: grab;
    transition: all 0.3s ease;
    position: relative;
}

.xml-candidate-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.xml-candidate-item:active {
    cursor: grabbing;
}

.sped-item-container {
    min-height: 200px;
    transition: all 0.3s ease;
}

.sped-item-container.drag-over {
    background-color: #e3f2fd !important;
    border: 2px dashed #2196f3 !important;
}

.xml-items-dropped {
    min-height: 100px;
}

.border-dashed {
    border: 2px dashed #dee2e6 !important;
    border-radius: 8px;
}

.xml-selected-item {
    animation: slideInDown 0.3s ease;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.xml-candidate-item[style*="opacity: 0.5"] {
    filter: grayscale(50%);
}

/* Badge para itens selecionados */
.xml-candidate-item .badge.bg-success {
    position: absolute;
    top: 5px;
    right: 5px;
    z-index: 10;
}

/* Melhorar aparência dos cards no modal */
#modal-matching-manual .card-header {
    font-weight: 600;
}

#modal-matching-manual .card-body {
    padding: 1rem;
}

/* Estilo para área de drop ativa */
.sped-item-container[ondragover] {
    border: 2px dashed #28a745;
    background-color: #f8fff9;
}

/* Responsividade para modal de matching */
@media (max-width: 768px) {
    #modal-matching-manual .modal-dialog {
        margin: 0.5rem;
    }

    #modal-matching-manual .card-body {
        max-height: 300px;
    }
}

/* Cor de fundo mais suave para itens já pareados */
.bg-warning-light {
    background-color: #fff3cd !important;
    border-color: #ffeaa7 !important;
}
