/**
 * Escritorios.js - Auditoria Fiscal
 * Funções para gerenciar a página de escritórios
 */

// Variáveis globais
let escritoriosTable = null;

document.addEventListener('DOMContentLoaded', function () {
  // Inicializar a página de escritórios
  initEscritoriosPage();
});

/**
 * Inicializa a página de escritórios
 */
function initEscritoriosPage() {
  // Configurar o botão de adicionar escritório
  setupAddEscritorioButton();

  // Carregar dados de escritórios
  loadEscritoriosData();
}

/**
 * Carrega dados de escritórios
 */
function loadEscritoriosData() {
  // Mostrar indicador de carregamento
  const contentArea = document.querySelector('.content-area');
  if (contentArea) {
    contentArea.innerHTML =
      '<div class="text-center my-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">Carregando escritórios...</p></div>';
  }

  // Fazer requisição para obter os escritórios
  fetch('/fiscal/api/escritorios', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => {
      return response.json();
    })
    .then((data) => {
      renderEscritoriosTable(data.escritorios || []);
    })
    .catch((error) => {
      if (contentArea) {
        contentArea.innerHTML =
          '<div class="alert alert-danger">Erro ao carregar escritórios. Tente novamente mais tarde.</div>';
      }
    });
}

/**
 * Renderiza a tabela de escritórios
 * @param {Array} escritorios - Lista de escritórios
 */
function renderEscritoriosTable(escritorios) {
  const contentArea = document.querySelector('.content-area');
  if (!contentArea) {
    return;
  }

  // Criar estrutura da tabela
  let html = `
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h3>Escritórios Cadastrados</h3>
    </div>
  `;

  if (escritorios.length === 0) {
    html += '<div class="alert alert-info">Nenhum escritório cadastrado.</div>';
  } else {
    html += `
      <div class="table-responsive">
        <table id="escritorios-table" class="table table-striped table-hover">
          <thead>
            <tr>
              <th>Nome</th>
              <th>CNPJ</th>
              <th>Endereço</th>
              <th>Ações</th>
            </tr>
          </thead>
          <tbody>
    `;

    escritorios.forEach((escritorio) => {
      html += `
        <tr>
          <td>${escritorio.nome}</td>
          <td>${escritorio.cnpj}</td>
          <td>${escritorio.endereco || '-'}</td>
          <td>
            <button class="btn btn-sm btn-info view-escritorio" data-id="${
              escritorio.id
            }">
              <i class="fas fa-eye"></i>
            </button>
            <button class="btn btn-sm btn-primary edit-escritorio" data-id="${
              escritorio.id
            }">
              <i class="fas fa-edit"></i>
            </button>
          </td>
        </tr>
      `;
    });

    html += `
          </tbody>
        </table>
      </div>
    `;
  }

  // Atualizar o conteúdo
  contentArea.innerHTML = html;

  // Inicializar DataTable
  if (escritorios.length > 0) {
    try {
      const ptBR = {
        sEmptyTable: 'Nenhum registro encontrado',
        sInfo: 'Mostrando de _START_ até _END_ de _TOTAL_ registros',
        sInfoEmpty: 'Mostrando 0 até 0 de 0 registros',
        sInfoFiltered: '(Filtrados de _MAX_ registros)',
        sInfoPostFix: '',
        sInfoThousands: '.',
        sLengthMenu: '_MENU_ resultados por página',
        sLoadingRecords: 'Carregando...',
        sProcessing: 'Processando...',
        sZeroRecords: 'Nenhum registro encontrado',
        sSearch: 'Pesquisar',
        oPaginate: {
          sNext: 'Próximo',
          sPrevious: 'Anterior',
          sFirst: 'Primeiro',
          sLast: 'Último',
        },
        oAria: {
          sSortAscending: ': Ordenar colunas de forma ascendente',
          sSortDescending: ': Ordenar colunas de forma descendente',
        },
      };

      escritoriosTable = $('#escritorios-table').DataTable({
        language: ptBR,
        order: [[0, 'asc']],
        pageLength: 10,
        responsive: true,
      });
    } catch (error) {
    }
  }

  // Configurar botão de adicionar escritório
  setupAddEscritorioButton();
}

/**
 * Configura o botão de adicionar escritório
 */
function setupAddEscritorioButton() {
  const addButton = document.getElementById('btn-add-escritorio');
  if (!addButton) {
    return;
  }

  // Adicionar event listener diretamente (o botão já existe no HTML)
  addButton.addEventListener('click', function () {
    openEscritorioModal();
  });
}

/**
 * Abre o modal para cadastro de escritório
 * @param {Object} escritorio - Dados do escritório para edição (opcional)
 */
function openEscritorioModal(escritorio = null) {
  // Obter referências ao modal
  const modal = document.getElementById('formModal');
  const modalTitle = document.getElementById('modalTitle');
  const modalBody = document.getElementById('modalBody');

  // Definir título do modal
  modalTitle.textContent = escritorio ? 'Editar Escritório' : 'Novo Escritório';

  // Criar conteúdo do modal com tabs
  modalBody.innerHTML = `
    <ul class="nav nav-tabs" id="escritorioTabs" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="escritorio-tab" data-bs-toggle="tab" data-bs-target="#escritorio-content" type="button" role="tab" aria-controls="escritorio-content" aria-selected="true">
          Dados do Escritório
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="admin-tab" data-bs-toggle="tab" data-bs-target="#admin-content" type="button" role="tab" aria-controls="admin-content" aria-selected="false">
          Usuário Administrador
        </button>
      </li>
    </ul>

    <div class="tab-content mt-3" id="escritorioTabsContent">
      <!-- Tab Dados do Escritório -->
      <div class="tab-pane fade show active" id="escritorio-content" role="tabpanel" aria-labelledby="escritorio-tab">
        <form id="escritorio-form" class="needs-validation" novalidate>
          <div class="mb-3">
            <label for="nome" class="form-label">Nome do Escritório *</label>
            <input type="text" class="form-control" id="nome" name="nome" required value="${
              escritorio ? escritorio.nome : ''
            }">
            <div class="invalid-feedback">
              Por favor, informe o nome do escritório.
            </div>
          </div>

          <div class="mb-3">
            <label for="cnpj" class="form-label">CNPJ *</label>
            <input type="text" class="form-control" id="cnpj" name="cnpj" required value="${
              escritorio ? escritorio.cnpj : ''
            }">
            <div class="invalid-feedback">
              Por favor, informe o CNPJ do escritório.
            </div>
          </div>

          <div class="mb-3">
            <label for="endereco" class="form-label">Endereço</label>
            <textarea class="form-control" id="endereco" name="endereco" rows="3">${
              escritorio && escritorio.endereco ? escritorio.endereco : ''
            }</textarea>
            <div class="form-text">Opcional: Informe o endereço completo do escritório.</div>
          </div>

          <div class="d-flex justify-content-between">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
            <button type="button" class="btn btn-primary" id="btn-next-tab">Próximo</button>
          </div>
        </form>
      </div>

      <!-- Tab Usuário Administrador -->
      <div class="tab-pane fade" id="admin-content" role="tabpanel" aria-labelledby="admin-tab">
        <form id="admin-form" class="needs-validation" novalidate>
          <div class="mb-3">
            <label for="admin-nome" class="form-label">Nome do Usuário *</label>
            <input type="text" class="form-control" id="admin-nome" name="admin-nome" required>
            <div class="invalid-feedback">
              Por favor, informe o nome do usuário administrador.
            </div>
          </div>

          <div class="mb-3">
            <label for="admin-email" class="form-label">E-mail *</label>
            <input type="email" class="form-control" id="admin-email" name="admin-email" required>
            <div class="invalid-feedback">
              Por favor, informe um e-mail válido.
            </div>
          </div>

          <div class="mb-3">
            <label for="admin-senha" class="form-label">Senha *</label>
            <div class="input-group">
              <input type="password" class="form-control" id="admin-senha" name="admin-senha" required>
              <button class="btn btn-outline-secondary toggle-password" type="button">
                <i class="fas fa-eye"></i>
              </button>
            </div>
            <div class="invalid-feedback">
              Por favor, informe uma senha.
            </div>
          </div>

          <div class="d-flex justify-content-between">
            <button type="button" class="btn btn-secondary" id="btn-prev-tab">Voltar</button>
            <button type="submit" class="btn btn-success" id="btn-save-escritorio">Salvar</button>
          </div>
        </form>
      </div>
    </div>

    <div id="form-message" class="mt-3"></div>
  `;

  // Inicializar o modal
  const modalInstance = new bootstrap.Modal(modal);
  modalInstance.show();

  // Configurar navegação entre tabs
  setupTabNavigation();

  // Configurar toggle de senha
  setupPasswordToggle();

  // Configurar envio do formulário
  setupEscritorioFormSubmit(modalInstance, escritorio);
}

/**
 * Configura a navegação entre as tabs do modal
 */
function setupTabNavigation() {
  const nextTabBtn = document.getElementById('btn-next-tab');
  const prevTabBtn = document.getElementById('btn-prev-tab');

  if (nextTabBtn) {
    nextTabBtn.addEventListener('click', function () {
      // Validar formulário da primeira tab
      const escritorioForm = document.getElementById('escritorio-form');
      if (!escritorioForm.checkValidity()) {
        escritorioForm.classList.add('was-validated');
        return;
      }

      // Mudar para a segunda tab
      const adminTab = document.getElementById('admin-tab');
      const tabInstance = new bootstrap.Tab(adminTab);
      tabInstance.show();
    });
  }

  if (prevTabBtn) {
    prevTabBtn.addEventListener('click', function () {
      // Voltar para a primeira tab
      const escritorioTab = document.getElementById('escritorio-tab');
      const tabInstance = new bootstrap.Tab(escritorioTab);
      tabInstance.show();
    });
  }
}

/**
 * Configura o toggle de senha
 */
function setupPasswordToggle() {
  const toggleButtons = document.querySelectorAll('.toggle-password');

  toggleButtons.forEach((button) => {
    button.addEventListener('click', function () {
      const input = this.previousElementSibling;
      const icon = this.querySelector('i');

      if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
      } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
      }
    });
  });
}

/**
 * Configura o envio do formulário de escritório
 * @param {Object} modalInstance - Instância do modal Bootstrap
 * @param {Object} escritorio - Dados do escritório para edição (opcional)
 */
function setupEscritorioFormSubmit(modalInstance, escritorio = null) {
  const saveButton = document.getElementById('btn-save-escritorio');
  if (!saveButton) return;

  saveButton.addEventListener('click', function (e) {
    e.preventDefault();

    // Validar formulários
    const escritorioForm = document.getElementById('escritorio-form');
    const adminForm = document.getElementById('admin-form');

    if (!escritorioForm.checkValidity() || !adminForm.checkValidity()) {
      escritorioForm.classList.add('was-validated');
      adminForm.classList.add('was-validated');
      return;
    }

    // Mostrar indicador de carregamento
    const formMessage = document.getElementById('form-message');
    formMessage.innerHTML =
      '<div class="alert alert-info">Salvando dados...</div>';
    saveButton.disabled = true;

    // Obter dados do formulário
    const escritorioData = {
      nome: document.getElementById('nome').value,
      cnpj: document.getElementById('cnpj').value,
      endereco: document.getElementById('endereco').value,
    };

    // Criar o escritório
    createEscritorio(escritorioData)
      .then((response) => {
        if (response.id) {
          // Escritório criado com sucesso, criar o usuário administrador
          const userData = {
            nome: document.getElementById('admin-nome').value,
            email: document.getElementById('admin-email').value,
            senha: document.getElementById('admin-senha').value,
            escritorio_id: response.id,
            tipo_usuario: 'escritorio',
          };

          return createEscritorioAdmin(userData);
        } else {
          throw new Error(response.message || 'Erro ao criar escritório');
        }
      })
      .then((response) => {
        // Exibir mensagem de sucesso
        formMessage.innerHTML =
          '<div class="alert alert-success">Escritório e usuário administrador criados com sucesso!</div>';

        // Fechar o modal após 2 segundos
        setTimeout(() => {
          modalInstance.hide();
          // Recarregar a tabela de escritórios
          loadEscritoriosData();
        }, 2000);
      })
      .catch((error) => {
        formMessage.innerHTML = `<div class="alert alert-danger">${
          error.message || 'Erro ao salvar dados. Tente novamente.'
        }</div>`;
        saveButton.disabled = false;
      });
  });
}

/**
 * Cria um novo escritório
 * @param {Object} data - Dados do escritório
 * @returns {Promise} - Promise com a resposta da API
 */
function createEscritorio(data) {
  return fetch('/fiscal/api/escritorios', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    body: JSON.stringify(data),
  }).then((response) => response.json());
}

/**
 * Cria um usuário administrador para o escritório
 * @param {Object} data - Dados do usuário
 * @returns {Promise} - Promise com a resposta da API
 */
function createEscritorioAdmin(data) {
  return fetch('/fiscal/api/usuarios', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    body: JSON.stringify(data),
  }).then((response) => response.json());
}
