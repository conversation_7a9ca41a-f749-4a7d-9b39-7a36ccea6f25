# Implementação de Melhorias SPED - Resumo

## Alterações Implementadas

### 1. **Banco de Dados**

#### Tabela `cliente_entrada` - Novos campos:

- `cnae` VARCHAR(10) - CNAE principal do fornecedor
- `atividade` VARCHAR(100) - Atividade mapeada baseada no CNAE
- `destinacao` VARCHAR(100) - Destinação mapeada baseada no CNAE
- `simples_nacional` BOOLEAN - Status do Simples Nacional
- `natureza_juridica` VARCHAR(255) - Descrição da natureza jurídica

#### Tabela `item_nota_entrada` - Novos campos:

- `p_red_icms` DECIMAL(5,4) - Percentual de redução de ICMS
- `p_red_icms_st` DECIMAL(5,4) - Percentual de redução de ICMS-ST
- `p_mva_icms_st` DECIMAL(5,4) - Percentual de MVA de ICMS-ST
- `p_red_ipi` DECIMAL(5,4) - Percentual de redução de IPI
- `p_red_cofins` DECIMAL(5,4) - Percentual de redução de COFINS
- `valor_icms_cenario` DECIMAL(15,2) - Valor ICMS calculado pelo cenário
- `valor_icms_st_cenario` DECIMAL(15,2) - Valor ICMS-ST calculado pelo cenário
- `valor_ipi_cenario` DECIMAL(15,2) - Valor IPI calculado pelo cenário
- `valor_pis_cenario` DECIMAL(15,2) - Valor PIS calculado pelo cenário
- `valor_cofins_cenario` DECIMAL(15,2) - Valor COFINS calculado pelo cenário

### 2. **Backend - Modelos**

#### `ClienteEntrada` (back/models/cliente_entrada.py):

- Adicionados novos campos da API CNPJ
- Atualizado método `to_dict()` para incluir novos campos

#### `ItemNotaEntrada` (back/models/item_nota_entrada.py):

- Adicionados campos de percentuais de redução
- Adicionados campos para valores calculados dos tributos
- Atualizado método `to_dict()` para incluir novos campos

### 3. **Backend - Utilitários**

#### `sped_tipo_mapper.py` (back/utils/sped_tipo_mapper.py):

- Novo utilitário para mapear tipos de produto SPED
- Dicionário completo com códigos 00-99 e suas descrições
- Funções para obter descrição e listar todos os tipos

### 4. **Backend - Serviços**

#### `SPEDImportService` (back/services/sped_import_service.py):

- **Integração com API CNPJ**: Busca dados de fornecedores automaticamente
- **Mapeamento de atividade**: Implementa mesma lógica do XML para "Produtor Rural"
- **Criação automática de cenários**: Cria cenários para todos os tributos após importação
- **Logging melhorado**: Mais informações sobre o processamento

### 5. **Frontend - JavaScript**

#### `importacao.js` (front/static/js/importacao.js):

- **Correção WebSocket**: Corrigidos IDs de elementos para importação individual
- **Indicadores de progresso**: Corrigidos para funcionar corretamente
- **Elementos corretos**: Uso dos IDs corretos (`import-xml-result` vs `import-result`)

### 6. **Scripts de Atualização**

#### `update_sped_tables.sql` (db/update_sped_tables.sql):

- Script SQL para aplicar alterações no banco existente
- Adiciona novos campos com `IF NOT EXISTS` para segurança
- Inclui comentários explicativos nos campos

## Funcionalidades Implementadas

### 1. **Dicionário SPED de Tipos de Produto**

```
00 = Mercadoria para Revenda
01 = Matéria Prima
02 = Embalagem
03 = Produto em Processo
04 = Produto Acabado
05 = Subproduto
06 = Produto Intermediário
07 = Material de uso e consumo
08 = Ativo Imobilizado
09 = Serviços
10 = Outros Insumos
99 = Outras
```

### 2. **Integração API CNPJ para Fornecedores SPED**

- Busca automática de dados para fornecedores com CNPJ
- Extração de CNAE, atividade, destinação e status Simples Nacional
- Prioridade para "Produtor Rural" baseado na natureza jurídica
- Mesma lógica implementada na importação XML

### 3. **Criação Automática de Cenários**

- Cenários criados automaticamente após importação SPED
- Suporte para todos os tipos de tributos (ICMS, ICMS-ST, IPI, PIS, COFINS)
- Mapeamento de dados do SPED para estrutura de cenários
- Logging detalhado do processo

### 4. **Correção WebSocket**

- Corrigidos problemas com indicadores de progresso
- Importação individual agora mostra corretamente o ícone "Processando XML"
- Importação em lote mantém funcionalidade WebSocket

## Como Aplicar as Alterações

### 1. **Banco de Dados**

```sql
-- Execute o script de atualização
\i db/update_sped_tables.sql
```

### 2. **Aplicação**

- Reiniciar o servidor backend para carregar novos modelos
- Limpar cache do navegador para carregar JavaScript atualizado

## Próximos Passos

1. **Testar importação SPED** com arquivo real
2. **Verificar criação de cenários** automáticos
3. **Validar integração API CNPJ** para fornecedores
4. **Testar WebSocket** em importações individuais e em lote
5. **Implementar cálculos** de percentuais de redução conforme necessário

## Correções Implementadas

### **Problema de Transação SQL Resolvido**

- Adicionado controle de rollback adequado
- Separação da criação de cenários em transação independente
- Melhor tratamento de erros durante importação

### **Unificação de Tabelas Cliente**

- **Solução**: Usar apenas tabela `cliente` existente
- Adicionados campos SPED: `cod_part`, `cpf`, `codigo_municipio`, `suframa`, `complemento`
- Atualizada referência em `nota_entrada`: `cliente_id` em vez de `cliente_entrada_id`
- Busca inteligente: primeiro por CNPJ/CPF, depois por `cod_part`

### **WebSocket para Importação SPED**

- Adicionados métodos específicos no `WebSocketService`
- Notificações de progresso em tempo real (20%, 40%, 60%, 80%)
- Listeners JavaScript para acompanhar importação
- Fallback para quando WebSocket não está disponível

## Scripts de Atualização

### **Banco de Dados**

```sql
-- Executar: db/update_sped_tables.sql
-- Adiciona campos na tabela cliente
-- Atualiza referências em nota_entrada
-- Adiciona campos de percentual de redução em item_nota_entrada
```

## Observações Importantes

- **Compatibilidade**: Todos os novos campos são opcionais (nullable)
- **Performance**: API CNPJ só é chamada para novos fornecedores com CNPJ válido
- **Cenários**: Criados automaticamente apenas se houver valores de tributos
- **WebSocket**: Funciona para importação SPED com progresso em tempo real
- **Logs**: Detalhados para facilitar debugging e monitoramento
- **Transações**: Controle adequado de rollback em caso de erro
