-- =====================================================
-- RECRIAÇÃO COMPLETA DO BANCO DE DADOS
-- Sistema de Auditoria Fiscal - SEM CHATBOT
-- =====================================================

-- Definir encoding correto
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;

-- Remover todas as tabelas existentes (ordem inversa das dependências)
DROP TABLE IF EXISTS auditoria_resultado CASCADE;
DROP TABLE IF EXISTS auditoria_sumario CASCADE;
DROP TABLE IF EXISTS tributo_historico CASCADE;
DROP TABLE IF EXISTS tributo CASCADE;
DROP TABLE IF EXISTS nota_fiscal_item CASCADE;
DROP TABLE IF EXISTS importacao_xml CASCADE;
DROP TABLE IF EXISTS cenario_icms CASCADE;
DROP TABLE IF EXISTS cenario_icms_st CASCADE;
DROP TABLE IF EXISTS cenario_ipi CASCADE;
DROP TABLE IF EXISTS cenario_pis CASCADE;
DROP TABLE IF EXISTS cenario_cofins CASCADE;
DROP TABLE IF EXISTS cenario_difal CASCADE;
DROP TABLE IF EXISTS produto CASCADE;
DROP TABLE IF EXISTS cliente CASCADE;
DROP TABLE IF EXISTS log_atividade CASCADE;
DROP TABLE IF EXISTS usuario CASCADE;
DROP TABLE IF EXISTS empresa CASCADE;
DROP TABLE IF EXISTS escritorio CASCADE;

-- Remover qualquer coisa relacionada ao chatbot
DROP TABLE IF EXISTS chatbot_conversas CASCADE;
DROP TABLE IF EXISTS chatbot_templates CASCADE;
DROP MATERIALIZED VIEW IF EXISTS vw_chatbot_dados_completos CASCADE;
DROP FUNCTION IF EXISTS refresh_chatbot_view() CASCADE;
DROP FUNCTION IF EXISTS trigger_refresh_chatbot_view() CASCADE;

-- =====================================================
-- CRIAÇÃO DAS TABELAS PRINCIPAIS
-- =====================================================

-- Tabela de Escritórios
CREATE TABLE escritorio (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    cnpj VARCHAR(18) NOT NULL UNIQUE,
    endereco TEXT,
    responsavel VARCHAR(255),
    logo_path VARCHAR(500),
    cor_relatorio VARCHAR(7) DEFAULT '#6f42c1'
);

-- Tabela de Empresas
CREATE TABLE empresa (
    id SERIAL PRIMARY KEY,
    escritorio_id INTEGER REFERENCES escritorio(id),
    razao_social VARCHAR(255) NOT NULL,
    cnpj VARCHAR(18) NOT NULL UNIQUE,
    inscricao_estadual VARCHAR(20),
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    nome_fantasia VARCHAR(255),
    email VARCHAR(255),
    responsavel VARCHAR(255),
    cep VARCHAR(10),
    logradouro VARCHAR(255),
    numero VARCHAR(20),
    complemento VARCHAR(255),
    bairro VARCHAR(100),
    cidade VARCHAR(100),
    estado VARCHAR(2),
    cnae VARCHAR(20),
    tributacao VARCHAR(50),
    atividade VARCHAR(50),
    pis_cofins VARCHAR(20),
    observacoes TEXT
);

-- Tabela de Usuários
CREATE TABLE usuario (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    senha_hash VARCHAR(255) NOT NULL,
    empresas_permitidas JSONB,
    escritorio_id INTEGER REFERENCES escritorio(id),
    is_admin BOOLEAN DEFAULT FALSE,
    tipo_usuario VARCHAR(20) DEFAULT 'usuario'
);

-- Tabela para logs de atividades
CREATE TABLE log_atividade (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER REFERENCES usuario(id),
    acao VARCHAR(255) NOT NULL,
    descricao TEXT,
    data_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de Clientes
CREATE TABLE cliente (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cnpj VARCHAR(18) NOT NULL,
    razao_social VARCHAR(255) NOT NULL,
    inscricao_estadual VARCHAR(30),
    logradouro VARCHAR(255),
    numero VARCHAR(20),
    bairro VARCHAR(100),
    municipio VARCHAR(100),
    uf VARCHAR(2),
    cep VARCHAR(10),
    pais VARCHAR(50),
    codigo_pais VARCHAR(10),
    cnae VARCHAR(20),
    atividade VARCHAR(50),
    destinacao VARCHAR(50),
    simples_nacional BOOLEAN DEFAULT FALSE,
    ind_ie_dest VARCHAR(2),
    ind_final VARCHAR(2),
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'novo'
);

-- Tabela de Produtos
CREATE TABLE produto (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    codigo VARCHAR(50) NOT NULL,
    descricao VARCHAR(255) NOT NULL,
    unidade_comercial VARCHAR(10),
    unidade_tributavel VARCHAR(10),
    codigo_ean VARCHAR(50),
    codigo_ean_tributavel VARCHAR(50),
    unidade_tributaria VARCHAR(10),
    tipo_sped VARCHAR(50),
    cest VARCHAR(10),
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'novo'
);

-- Tabela de Notas Fiscais (Itens)
CREATE TABLE nota_fiscal_item (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,
    numero_nf VARCHAR(20) NOT NULL,
    chave_nf VARCHAR(44),
    data_emissao DATE NOT NULL,
    cfop VARCHAR(10),
    ncm VARCHAR(20),
    unidade_comercial VARCHAR(10),
    quantidade NUMERIC(15, 4),
    valor_unitario NUMERIC(15, 4),
    valor_total NUMERIC(15, 2),
    tipo_operacao VARCHAR(1),
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (empresa_id, cliente_id, produto_id, data_emissao, numero_nf)
);

-- Tabela para armazenar as importações realizadas
CREATE TABLE importacao_xml (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    usuario_id INTEGER REFERENCES usuario(id) NOT NULL,
    arquivo_nome VARCHAR(255) NOT NULL,
    chave_nf VARCHAR(50),
    numero_nf VARCHAR(20),
    data_emissao DATE,
    cnpj_emitente VARCHAR(18),
    razao_social_emitente VARCHAR(255),
    data_importacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'concluido',
    mensagem TEXT
);

-- =====================================================
-- TABELAS DE CENÁRIOS
-- =====================================================

-- Tabela de Cenários ICMS
CREATE TABLE cenario_icms (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,
    direcao VARCHAR(10),
    tipo_operacao VARCHAR(1),
    cfop VARCHAR(10),
    ncm VARCHAR(20),
    status VARCHAR(20) DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    origem VARCHAR(2),
    cst VARCHAR(3),
    mod_bc VARCHAR(2),
    p_red_bc NUMERIC(10, 4),
    aliquota NUMERIC(10, 4),
    p_dif NUMERIC(10, 4),
    incluir_frete BOOLEAN DEFAULT TRUE,
    incluir_desconto BOOLEAN DEFAULT TRUE
);

-- Tabela de Cenários ICMS-ST
CREATE TABLE cenario_icms_st (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,
    direcao VARCHAR(10),
    tipo_operacao VARCHAR(1),
    cfop VARCHAR(10),
    ncm VARCHAR(20),
    status VARCHAR(20) DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    origem VARCHAR(2),
    cst VARCHAR(3),
    mod_bc VARCHAR(2),
    p_red_bc NUMERIC(10, 4),
    aliquota NUMERIC(10, 4),
    icms_st_mod_bc VARCHAR(2),
    icms_st_aliquota NUMERIC(10, 4),
    icms_st_p_mva NUMERIC(10, 4),
    incluir_frete BOOLEAN DEFAULT TRUE,
    incluir_desconto BOOLEAN DEFAULT TRUE
);

-- Tabela de Cenários IPI
CREATE TABLE cenario_ipi (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,
    direcao VARCHAR(10),
    tipo_operacao VARCHAR(1),
    cfop VARCHAR(10),
    ncm VARCHAR(20),
    status VARCHAR(20) DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    cst VARCHAR(3),
    aliquota NUMERIC(10, 4),
    ex VARCHAR(3),
    incluir_frete BOOLEAN DEFAULT TRUE,
    incluir_desconto BOOLEAN DEFAULT TRUE
);

-- Tabela de Cenários PIS
CREATE TABLE cenario_pis (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,
    direcao VARCHAR(10),
    tipo_operacao VARCHAR(1),
    cfop VARCHAR(10),
    ncm VARCHAR(20),
    status VARCHAR(20) DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    cst VARCHAR(3),
    aliquota NUMERIC(10, 4),
    p_red_bc NUMERIC(10, 4),
    incluir_frete BOOLEAN DEFAULT TRUE,
    incluir_desconto BOOLEAN DEFAULT TRUE
);

-- Tabela de Cenários COFINS
CREATE TABLE cenario_cofins (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,
    direcao VARCHAR(10),
    tipo_operacao VARCHAR(1),
    cfop VARCHAR(10),
    ncm VARCHAR(20),
    status VARCHAR(20) DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    cst VARCHAR(3),
    aliquota NUMERIC(10, 4),
    p_red_bc NUMERIC(10, 4),
    incluir_frete BOOLEAN DEFAULT TRUE,
    incluir_desconto BOOLEAN DEFAULT TRUE
);

-- Tabela de Cenários DIFAL
CREATE TABLE cenario_difal (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,
    direcao VARCHAR(10),
    tipo_operacao VARCHAR(1),
    cfop VARCHAR(10),
    ncm VARCHAR(20),
    status VARCHAR(20) DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    origem VARCHAR(2),
    cst VARCHAR(3),
    mod_bc VARCHAR(2),
    p_red_bc NUMERIC(10, 4),
    aliquota NUMERIC(10, 4),
    p_fcp_uf_dest NUMERIC(10, 4),
    p_icms_uf_dest NUMERIC(10, 4),
    p_icms_inter NUMERIC(10, 4),
    p_icms_inter_part NUMERIC(10, 4)
);

-- =====================================================
-- TABELA DE TRIBUTOS
-- =====================================================

-- Tabela de Tributos (dados das notas fiscais)
CREATE TABLE tributo (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,
    data_emissao DATE NOT NULL,
    data_saida DATE,
    nota_fiscal_item_id INTEGER REFERENCES nota_fiscal_item(id),
    cenario_id INTEGER,
    cenario_tipo VARCHAR(20),

    -- ICMS
    icms_origem VARCHAR(2),
    icms_cst VARCHAR(3),
    icms_mod_bc VARCHAR(2),
    icms_p_red_bc NUMERIC(10, 4),
    icms_vbc NUMERIC(10, 2),
    icms_aliquota NUMERIC(10, 4),
    icms_valor NUMERIC(10, 2),
    icms_v_op NUMERIC(10, 2),
    icms_p_dif NUMERIC(10, 4),
    icms_v_dif NUMERIC(10, 2),

    -- ICMS-ST
    icms_st_mod_bc VARCHAR(2),
    icms_st_p_mva NUMERIC(10, 4),
    icms_st_vbc NUMERIC(10, 2),
    icms_st_aliquota NUMERIC(10, 4),
    icms_st_valor NUMERIC(10, 2),

    -- IPI
    ipi_cst VARCHAR(3),
    ipi_vbc NUMERIC(10, 2),
    ipi_aliquota NUMERIC(10, 4),
    ipi_valor NUMERIC(10, 2),
    ipi_codigo_enquadramento VARCHAR(3),
    ipi_ex VARCHAR(3),

    -- PIS
    pis_cst VARCHAR(3),
    pis_vbc NUMERIC(10, 2),
    pis_aliquota NUMERIC(10, 4),
    pis_valor NUMERIC(10, 2),
    pis_p_red_bc NUMERIC(10, 4),

    -- COFINS
    cofins_cst VARCHAR(3),
    cofins_vbc NUMERIC(10, 2),
    cofins_aliquota NUMERIC(10, 4),
    cofins_valor NUMERIC(10, 2),
    cofins_p_red_bc NUMERIC(10, 4),

    -- DIFAL
    difal_vbc NUMERIC(10, 2),
    difal_p_fcp_uf_dest NUMERIC(10, 4),
    difal_p_icms_uf_dest NUMERIC(10, 4),
    difal_p_icms_inter NUMERIC(10, 4),
    difal_p_icms_inter_part NUMERIC(10, 4),
    difal_v_fcp_uf_dest NUMERIC(10, 2),
    difal_v_icms_uf_dest NUMERIC(10, 2),
    difal_v_icms_uf_remet NUMERIC(10, 2),

    -- Informações adicionais
    numero_nf VARCHAR(20),
    chave_nf VARCHAR(50),
    tipo_operacao VARCHAR(1),
    status VARCHAR(20) DEFAULT 'novo',

    -- Valores do produto
    quantidade NUMERIC(10, 4),
    valor_unitario NUMERIC(10, 4),
    valor_total NUMERIC(10, 2),
    valor_frete NUMERIC(10, 2),
    valor_desconto NUMERIC(10, 2),

    -- Colunas para valores de auditoria
    cenario_icms_vbc NUMERIC(10, 2),
    cenario_icms_valor NUMERIC(10, 2),
    cenario_icms_st_vbc NUMERIC(10, 2),
    cenario_icms_st_valor NUMERIC(10, 2),
    cenario_ipi_vbc NUMERIC(10, 2),
    cenario_ipi_valor NUMERIC(10, 2),
    cenario_pis_vbc NUMERIC(10, 2),
    cenario_pis_valor NUMERIC(10, 2),
    cenario_cofins_vbc NUMERIC(10, 2),
    cenario_cofins_valor NUMERIC(10, 2),
    cenario_difal_valor NUMERIC(10, 2),

    -- Referências aos cenários utilizados na auditoria
    cenario_icms_id INTEGER REFERENCES cenario_icms(id),
    cenario_icms_st_id INTEGER REFERENCES cenario_icms_st(id),
    cenario_ipi_id INTEGER REFERENCES cenario_ipi(id),
    cenario_pis_id INTEGER REFERENCES cenario_pis(id),
    cenario_cofins_id INTEGER REFERENCES cenario_cofins(id),
    cenario_difal_id INTEGER REFERENCES cenario_difal(id),

    -- Status e data da auditoria geral
    auditoria_status VARCHAR(20) DEFAULT 'pendente',
    auditoria_data TIMESTAMP,

    -- Status e data da auditoria específicos por tipo de tributo
    auditoria_icms_status VARCHAR(20) DEFAULT 'pendente',
    auditoria_icms_st_status VARCHAR(20) DEFAULT 'pendente',
    auditoria_ipi_status VARCHAR(20) DEFAULT 'pendente',
    auditoria_pis_status VARCHAR(20) DEFAULT 'pendente',
    auditoria_cofins_status VARCHAR(20) DEFAULT 'pendente',
    auditoria_difal_status VARCHAR(20) DEFAULT 'pendente',

    auditoria_icms_data TIMESTAMP,
    auditoria_icms_st_data TIMESTAMP,
    auditoria_ipi_data TIMESTAMP,
    auditoria_pis_data TIMESTAMP,
    auditoria_cofins_data TIMESTAMP,
    auditoria_difal_data TIMESTAMP,

    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- TABELAS DE AUDITORIA
-- =====================================================

-- Tabela de Histórico de Tributos
CREATE TABLE tributo_historico (
    id SERIAL PRIMARY KEY,
    tributo_id INTEGER REFERENCES tributo(id) NOT NULL,
    usuario_id INTEGER REFERENCES usuario(id) NOT NULL,
    tipo_tributo VARCHAR(20) NOT NULL,
    status_anterior VARCHAR(20),
    status_novo VARCHAR(20),
    valores_anteriores JSONB,
    valores_novos JSONB,
    data_alteracao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de Resultados de Auditoria
CREATE TABLE auditoria_resultado (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    tributo_id INTEGER REFERENCES tributo(id) NOT NULL,
    nota_fiscal_item_id INTEGER REFERENCES nota_fiscal_item(id) NOT NULL,
    tipo_tributo VARCHAR(20) NOT NULL,
    cenario_id INTEGER NOT NULL,
    valor_nota NUMERIC(15, 2),
    valor_calculado NUMERIC(15, 2),
    base_calculo_nota NUMERIC(15, 2),
    base_calculo_calculada NUMERIC(15, 2),

    -- Campos para comparação de dados fiscais
    cst_nota VARCHAR(3),
    cst_cenario VARCHAR(3),
    origem_nota VARCHAR(2),
    origem_cenario VARCHAR(2),
    aliquota_nota NUMERIC(10, 4),
    aliquota_cenario NUMERIC(10, 4),

    -- Campos para indicar tipos de inconsistências
    inconsistencia_valor BOOLEAN DEFAULT FALSE,
    inconsistencia_cst BOOLEAN DEFAULT FALSE,
    inconsistencia_origem BOOLEAN DEFAULT FALSE,
    inconsistencia_aliquota BOOLEAN DEFAULT FALSE,
    inconsistencia_base_calculo BOOLEAN DEFAULT FALSE,

    status VARCHAR(20) NOT NULL,
    cenario_status VARCHAR(20) DEFAULT 'producao',
    data_auditoria TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Campos para análise do analista
    analista_visualizou BOOLEAN DEFAULT FALSE,
    observacoes_analista TEXT,
    data_visualizacao TIMESTAMP,
    usuario_analista_id INTEGER REFERENCES usuario(id)
);

-- Tabela de Sumário de Auditoria
CREATE TABLE auditoria_sumario (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    ano INTEGER NOT NULL,
    mes INTEGER NOT NULL,
    tipo_tributo VARCHAR(20) NOT NULL,
    total_notas INTEGER NOT NULL DEFAULT 0,
    total_produtos INTEGER NOT NULL DEFAULT 0,
    valor_total_notas NUMERIC(15, 2) NOT NULL DEFAULT 0,
    valor_total_cenarios NUMERIC(15, 2) NOT NULL DEFAULT 0,
    valor_total_produtos NUMERIC(15, 2) NOT NULL DEFAULT 0,
    valor_total_tributo NUMERIC(15, 2) NOT NULL DEFAULT 0,
    total_conforme INTEGER NOT NULL DEFAULT 0,
    total_inconsistente INTEGER NOT NULL DEFAULT 0,
    notas_conformes INTEGER NOT NULL DEFAULT 0,
    notas_inconsistentes INTEGER NOT NULL DEFAULT 0,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notas_contabilizadas TEXT,
    produtos_contabilizados TEXT,
    valor_inconsistente_maior NUMERIC(15, 2) NOT NULL DEFAULT 0,
    valor_inconsistente_menor NUMERIC(15, 2) NOT NULL DEFAULT 0
);

-- =====================================================
-- ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índices para tabela empresa
CREATE INDEX idx_empresa_escritorio ON empresa(escritorio_id);
CREATE INDEX idx_empresa_cnpj ON empresa(cnpj);

-- Índices para tabela usuario
CREATE INDEX idx_usuario_escritorio ON usuario(escritorio_id);
CREATE INDEX idx_usuario_email ON usuario(email);

-- Índices para tabela cliente
CREATE INDEX idx_cliente_empresa ON cliente(empresa_id);
CREATE INDEX idx_cliente_escritorio ON cliente(escritorio_id);
CREATE INDEX idx_cliente_cnpj ON cliente(cnpj);

-- Índices para tabela produto
CREATE INDEX idx_produto_empresa ON produto(empresa_id);
CREATE INDEX idx_produto_escritorio ON produto(escritorio_id);

-- Índices para tabela nota_fiscal_item
CREATE INDEX idx_nota_fiscal_item_empresa ON nota_fiscal_item(empresa_id);
CREATE INDEX idx_nota_fiscal_item_cliente ON nota_fiscal_item(cliente_id);
CREATE INDEX idx_nota_fiscal_item_produto ON nota_fiscal_item(produto_id);
CREATE INDEX idx_nota_fiscal_item_data ON nota_fiscal_item(data_emissao);
CREATE INDEX idx_nota_fiscal_item_numero ON nota_fiscal_item(numero_nf);

-- Índices para tabela tributo
CREATE INDEX idx_tributo_empresa ON tributo(empresa_id);
CREATE INDEX idx_tributo_cliente ON tributo(cliente_id);
CREATE INDEX idx_tributo_produto ON tributo(produto_id);
CREATE INDEX idx_tributo_data ON tributo(data_emissao);
CREATE INDEX idx_tributo_nota_item ON tributo(nota_fiscal_item_id);

-- Índices para tabelas de cenários
CREATE INDEX idx_cenario_icms_empresa ON cenario_icms(empresa_id);
CREATE INDEX idx_cenario_icms_cliente ON cenario_icms(cliente_id);
CREATE INDEX idx_cenario_icms_produto ON cenario_icms(produto_id);
CREATE INDEX idx_cenario_icms_status ON cenario_icms(status);

CREATE INDEX idx_cenario_icms_st_empresa ON cenario_icms_st(empresa_id);
CREATE INDEX idx_cenario_icms_st_cliente ON cenario_icms_st(cliente_id);
CREATE INDEX idx_cenario_icms_st_produto ON cenario_icms_st(produto_id);
CREATE INDEX idx_cenario_icms_st_status ON cenario_icms_st(status);

CREATE INDEX idx_cenario_ipi_empresa ON cenario_ipi(empresa_id);
CREATE INDEX idx_cenario_ipi_cliente ON cenario_ipi(cliente_id);
CREATE INDEX idx_cenario_ipi_produto ON cenario_ipi(produto_id);
CREATE INDEX idx_cenario_ipi_status ON cenario_ipi(status);

CREATE INDEX idx_cenario_pis_empresa ON cenario_pis(empresa_id);
CREATE INDEX idx_cenario_pis_cliente ON cenario_pis(cliente_id);
CREATE INDEX idx_cenario_pis_produto ON cenario_pis(produto_id);
CREATE INDEX idx_cenario_pis_status ON cenario_pis(status);

CREATE INDEX idx_cenario_cofins_empresa ON cenario_cofins(empresa_id);
CREATE INDEX idx_cenario_cofins_cliente ON cenario_cofins(cliente_id);
CREATE INDEX idx_cenario_cofins_produto ON cenario_cofins(produto_id);
CREATE INDEX idx_cenario_cofins_status ON cenario_cofins(status);

CREATE INDEX idx_cenario_difal_empresa ON cenario_difal(empresa_id);
CREATE INDEX idx_cenario_difal_cliente ON cenario_difal(cliente_id);
CREATE INDEX idx_cenario_difal_produto ON cenario_difal(produto_id);
CREATE INDEX idx_cenario_difal_status ON cenario_difal(status);

-- Índices para tabela auditoria_resultado
CREATE INDEX idx_auditoria_resultado_empresa ON auditoria_resultado(empresa_id);
CREATE INDEX idx_auditoria_resultado_tributo ON auditoria_resultado(tributo_id);
CREATE INDEX idx_auditoria_resultado_tipo ON auditoria_resultado(tipo_tributo);
CREATE INDEX idx_auditoria_resultado_status ON auditoria_resultado(status);

-- Índices para tabela auditoria_sumario
CREATE INDEX idx_auditoria_sumario_empresa ON auditoria_sumario(empresa_id);
CREATE INDEX idx_auditoria_sumario_ano_mes ON auditoria_sumario(ano, mes);
CREATE INDEX idx_auditoria_sumario_tipo ON auditoria_sumario(tipo_tributo);

-- =====================================================
-- DADOS INICIAIS
-- =====================================================

-- Criar usuário administrador padrão (senha: admin123)
INSERT INTO escritorio (nome, cnpj, endereco, responsavel) VALUES
('Escritório Principal', '12345678000100', 'Endereço do Escritório', 'Administrador');

INSERT INTO usuario (nome, email, senha_hash, is_admin, tipo_usuario, escritorio_id) VALUES
('Administrador', '<EMAIL>', '$2b$12$LQv3c1yqBwlVHpPjrCeyL.rh/sVDdBnYHP9B22jMP2allHwtfFuBG', TRUE, 'admin', 1);

-- =====================================================
-- COMENTÁRIOS E FINALIZAÇÃO
-- =====================================================

COMMENT ON TABLE escritorio IS 'Tabela de escritórios de contabilidade';
COMMENT ON TABLE empresa IS 'Tabela de empresas clientes dos escritórios';
COMMENT ON TABLE usuario IS 'Tabela de usuários do sistema';
COMMENT ON TABLE cliente IS 'Tabela de clientes das empresas (destinatários das notas)';
COMMENT ON TABLE produto IS 'Tabela de produtos das empresas';
COMMENT ON TABLE nota_fiscal_item IS 'Tabela de itens de notas fiscais importadas';
COMMENT ON TABLE tributo IS 'Tabela de dados tributários das notas fiscais';
COMMENT ON TABLE auditoria_resultado IS 'Tabela de resultados das auditorias tributárias';
COMMENT ON TABLE auditoria_sumario IS 'Tabela de sumários das auditorias por período';

-- Commit das alterações
COMMIT;

-- Mensagem de confirmação
DO $$
BEGIN
    RAISE NOTICE '=== BANCO DE DADOS RECRIADO COM SUCESSO ===';
    RAISE NOTICE 'Todas as tabelas foram criadas corretamente.';
    RAISE NOTICE 'Usuário administrador criado: <EMAIL> / admin123';
    RAISE NOTICE 'Sistema pronto para uso!';
END $$;
