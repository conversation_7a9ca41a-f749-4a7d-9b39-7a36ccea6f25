from flask import Blueprint, request, jsonify, Response
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, Usuario, Empresa, Escritorio, AuditoriaResultado, AuditoriaSumario, NotaFiscalItem, Tributo, Cliente, <PERSON>duto
from sqlalchemy import and_, or_
from io import BytesIO

relatorio_bp = Blueprint('relatorio', __name__)

@relatorio_bp.route('/api/relatorios/tributo/<tipo_tributo>', methods=['GET'])
@jwt_required()
def gerar_relatorio_tributo(tipo_tributo):
    """
    Gera relatório PDF específico para um tipo de tributo
    """
    try:
        from reportlab.lib.pagesizes import A4, landscape
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch, mm
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
        import os

        # Obter parâmetros
        empresa_id = request.args.get('empresa_id', type=int)
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        status = request.args.get('status', 'inconsistente')  # 'todos', 'conforme', 'inconsistente'

        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        if not empresa_id:
            return jsonify({"message": "ID da empresa é obrigatório"}), 400

        # Verificar permissões
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa:
            return jsonify({"message": "Empresa não encontrada"}), 404

        # Verificar se o usuário tem permissão para visualizar a empresa
        if not (usuario.is_admin or usuario.tipo_usuario == 'admin' or
                (usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id) or
                (usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas)):
            return jsonify({"message": "Você não tem permissão para gerar relatórios desta empresa"}), 403

        # Buscar dados do escritório
        escritorio = db.session.get(Escritorio, empresa.escritorio_id)

        # Buscar resultados de auditoria

        query = AuditoriaResultado.query.filter(
            AuditoriaResultado.empresa_id == empresa_id,
            AuditoriaResultado.tipo_tributo == tipo_tributo
        )

        # Filtrar por período se especificado
        if year and month:
            query = query.join(Tributo, AuditoriaResultado.tributo_id == Tributo.id).filter(
                db.extract('year', Tributo.data_emissao) == year,
                db.extract('month', Tributo.data_emissao) == month
            )

        # Filtrar por status se especificado
        if status != 'todos':
            query = query.filter(AuditoriaResultado.status == status)

        resultados = query.all()

        if not resultados:
            return jsonify({"message": "Nenhum resultado encontrado para os filtros especificados"}), 404

        # Criar PDF
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=landscape(A4),
                              leftMargin=20*mm, rightMargin=20*mm,
                              topMargin=20*mm, bottomMargin=20*mm)
        styles = getSampleStyleSheet()
        story = []

        # Estilo personalizado para título
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=20,
            textColor=colors.darkblue
        )

        # Estilo para subtítulo
        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=15,
            alignment=TA_CENTER
        )

        # Cabeçalho com logo e informações do escritório
        header_data = []

        # Verificar se existe logo
        logo_path = None
        if escritorio and escritorio.logo_path:
            logo_full_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                        'front', escritorio.logo_path.lstrip('/'))
            if os.path.exists(logo_full_path):
                logo_path = logo_full_path

        # Adiciona uma célula vazia para criar margem à esquerda
        if logo_path:
            try:
                logo = Image(logo_path, width=50*mm, height=30*mm)  # Reduzi um pouco o tamanho do logo
                # Adiciona uma célula vazia à esquerda para criar margem
                header_data.append(["", logo, f"<b>{escritorio.nome}</b><br/>Responsável: {escritorio.responsavel or 'N/A'}<br/>CNPJ: {escritorio.cnpj}"])
            except:
                header_data.append(["", "", f"<b>{escritorio.nome}</b><br/>Responsável: {escritorio.responsavel or 'N/A'}<br/>CNPJ: {escritorio.cnpj}"])
        else:
            header_data.append(["", "", f"<b>{escritorio.nome}</b><br/>Responsável: {escritorio.responsavel or 'N/A'}<br/>CNPJ: {escritorio.cnpj}"])

        # Ajusta as larguras das colunas (célula vazia + logo + texto)
        header_table = Table(header_data, colWidths=[10*mm, 50*mm, 140*mm])  # Redistribuí as larguras
        header_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),  # Centraliza verticalmente
            ('LEFTPADDING', (0, 0), (0, -1), 0),  # Remove padding da primeira célula
            ('BOTTOMPADDING', (0, 0), (-1, -1), 5)  # Adiciona um pequeno espaço abaixo da linha
        ]))
        story.append(header_table)

        # Título do relatório
        titulo = f"RELATÓRIO DE AUDITORIA FISCAL - {tipo_tributo.upper().replace('_', '-')}"
        story.append(Paragraph(titulo, title_style))

        # Informações da empresa e período
        info_text = f"<b>Empresa:</b> {empresa.razao_social}<br/><b>CNPJ:</b> {empresa.cnpj}"
        if year and month:
            info_text += f"<br/><b>Período:</b> {month:02d}/{year}"
        if status != 'todos':
            info_text += f"<br/><b>Status:</b> {status.title()}"

        story.append(Paragraph(info_text, subtitle_style))
        story.append(Spacer(1, 20))

        # Continuar na próxima parte...
        return _continuar_relatorio_tributo(story, resultados, doc, buffer, tipo_tributo, empresa_id)

    except ImportError:
        return jsonify({
            'success': False,
            'message': 'Biblioteca reportlab não instalada. Execute: pip install reportlab'
        }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao gerar relatório: {str(e)}'
        }), 500

def _continuar_relatorio_tributo(story, resultados, doc, buffer, tipo_tributo, empresa_id):
    """
    Continua a geração do relatório de tributo
    """
    from reportlab.platypus import Table, TableStyle
    from reportlab.lib import colors
    from reportlab.lib.units import mm

    # Cabeçalho da tabela
    headers = ['Nota Fiscal', 'Cliente', 'Produto', 'NCM', 'CFOP', 'CST',
               'Base Cálculo Nota', 'Alíquota Nota', 'Valor Nota',
               'Base Cálculo Cenário', 'Alíquota Cenário', 'Valor Cenário', 'Diferença']

    table_data = [headers]

    # Dados da tabela
    for resultado in resultados:
        # Buscar dados relacionados
        tributo = db.session.get(Tributo, resultado.tributo_id)
        nota_item = db.session.get(NotaFiscalItem, resultado.nota_fiscal_item_id)
        cliente = db.session.get(Cliente, tributo.cliente_id) if tributo else None
        produto = db.session.get(Produto, tributo.produto_id) if tributo else None

        # Calcular diferença
        diferenca = 0
        if resultado.valor_calculado and resultado.valor_nota:
            diferenca = float(resultado.valor_calculado) - float(resultado.valor_nota)

        row = [
            tributo.numero_nf if tributo else 'N/A',
            cliente.razao_social[:30] + '...' if cliente and len(cliente.razao_social) > 30 else (cliente.razao_social if cliente else 'N/A'),
            produto.descricao[:25] + '...' if produto and len(produto.descricao) > 25 else (produto.descricao if produto else 'N/A'),
            nota_item.ncm if nota_item else 'N/A',
            nota_item.cfop if nota_item else 'N/A',
            getattr(resultado, f'cst_nota', 'N/A'),
            f"R$ {float(resultado.base_calculo_nota):,.2f}" if resultado.base_calculo_nota else 'R$ 0,00',
            f"{float(resultado.aliquota_nota):,.2f}%" if resultado.aliquota_nota else '0,00%',
            f"R$ {float(resultado.valor_nota):,.2f}" if resultado.valor_nota else 'R$ 0,00',
            f"R$ {float(resultado.base_calculo_calculada):,.2f}" if resultado.base_calculo_calculada else 'R$ 0,00',
            f"{float(resultado.aliquota_cenario):,.2f}%" if resultado.aliquota_cenario else '0,00%',
            f"R$ {float(resultado.valor_calculado):,.2f}" if resultado.valor_calculado else 'R$ 0,00',
            f"R$ {diferenca:,.2f}"
        ]
        table_data.append(row)

    # Criar tabela
    table = Table(table_data, repeatRows=1)
    table.setStyle(TableStyle([
        # Cabeçalho
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 8),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

        # Dados
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 7),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),

        # Zebra striping
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
    ]))

    story.append(table)

    # Gerar PDF
    doc.build(story)
    buffer.seek(0)

    return Response(
        buffer.getvalue(),
        mimetype='application/pdf',
        headers={
            'Content-Disposition': f'attachment; filename=relatorio_{tipo_tributo}_{empresa_id}.pdf'
        }
    )

@relatorio_bp.route('/api/relatorios/geral', methods=['GET'])
@jwt_required()
def gerar_relatorio_geral():
    """
    Gera relatório PDF geral com todos os tipos de tributos
    """
    try:
        from reportlab.lib.pagesizes import A4, landscape
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image, PageBreak
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch, mm
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
        import os

        # Obter parâmetros
        empresa_id = request.args.get('empresa_id', type=int)
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        status = request.args.get('status', 'inconsistente')  # 'todos', 'conforme', 'inconsistente'

        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        if not empresa_id:
            return jsonify({"message": "ID da empresa é obrigatório"}), 400

        # Verificar permissões
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa:
            return jsonify({"message": "Empresa não encontrada"}), 404

        # Verificar se o usuário tem permissão para visualizar a empresa
        if not (usuario.is_admin or usuario.tipo_usuario == 'admin' or
                (usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id) or
                (usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas)):
            return jsonify({"message": "Você não tem permissão para gerar relatórios desta empresa"}), 403

        # Buscar dados do escritório
        escritorio = db.session.get(Escritorio, empresa.escritorio_id)

        # Buscar dados do usuário
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        usuario_nome = usuario.nome if usuario else "Usuário Desconhecido"

        # Tipos de tributos
        tipos_tributos = ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal']

        # Buscar resultados de auditoria para todos os tributos
        query = AuditoriaResultado.query.filter(
            AuditoriaResultado.empresa_id == empresa_id,
            AuditoriaResultado.tipo_tributo.in_(tipos_tributos)
        )

        # Filtrar por período se especificado
        if year and month:
            query = query.join(Tributo, AuditoriaResultado.tributo_id == Tributo.id).filter(
                db.extract('year', Tributo.data_emissao) == year,
                db.extract('month', Tributo.data_emissao) == month
            )

        # Filtrar por status se especificado
        if status != 'todos':
            query = query.filter(AuditoriaResultado.status == status)

        resultados = query.all()

        # Comentando a verificação para permitir relatórios mesmo sem dados de auditoria
        # if not resultados:
        #     return jsonify({"message": "Nenhum resultado encontrado para os filtros especificados"}), 404

        # Agrupar resultados por tipo de tributo
        resultados_por_tributo = {}
        for resultado in resultados:
            tipo = resultado.tipo_tributo
            if tipo not in resultados_por_tributo:
                resultados_por_tributo[tipo] = []
            resultados_por_tributo[tipo].append(resultado)

        # Criar PDF
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4,
                              leftMargin=20*mm, rightMargin=20*mm,
                              topMargin=20*mm, bottomMargin=20*mm)

        story = _criar_relatorio_geral_content(escritorio, empresa, resultados_por_tributo, year, month, status, usuario_nome)

        # Gerar PDF
        doc.build(story)
        buffer.seek(0)

        return Response(
            buffer.getvalue(),
            mimetype='application/pdf',
            headers={
                'Content-Disposition': f'attachment; filename=relatorio_geral_{empresa_id}.pdf'
            }
        )

    except ImportError:
        return jsonify({
            'success': False,
            'message': 'Biblioteca reportlab não instalada. Execute: pip install reportlab'
        }), 500
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        print(f"ERRO DETALHADO: {error_trace}")  # Para debug
        return jsonify({
            'success': False,
            'message': f'Erro ao gerar relatório geral: {str(e)}',
            'trace': error_trace
        }), 500

def _criar_relatorio_geral_content(escritorio, empresa, resultados_por_tributo, year, month, status, usuario_nome):
    """
    Cria o conteúdo do relatório geral reformulado
    """
    from reportlab.platypus import Paragraph, Spacer, Table, TableStyle, Image, PageBreak
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import mm, inch
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
    from reportlab.lib.colors import HexColor
    from services.auditoria_service import AuditoriaService
    from models import AuditoriaSumario
    import os
    import calendar

    styles = getSampleStyleSheet()
    story = []

    # Obter cor personalizada do escritório
    try:
        cor_preferencia = HexColor(escritorio.cor_relatorio) if escritorio.cor_relatorio else HexColor('#6f42c1')
        cor_preferencia_hex = escritorio.cor_relatorio if escritorio.cor_relatorio else '#6f42c1'
    except:
        cor_preferencia = HexColor('#6f42c1')
        cor_preferencia_hex = '#6f42c1'

    # ===== PRIMEIRA PÁGINA =====

    # Logo do escritório alinhado à esquerda com margem
    logo_path = None
    if escritorio and escritorio.logo_path:
        logo_full_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                    'front', escritorio.logo_path.lstrip('/'))
        if os.path.exists(logo_full_path):
            logo_path = logo_full_path

    if logo_path:
        try:
            # Reduzindo o tamanho do logo para melhor proporção
            logo = Image(logo_path, width=110*mm, height=70*mm)
            
            # Criando uma tabela com duas colunas: uma vazia para margem e outra para o logo
            logo_table = Table([
                [logo]  # Primeira célula vazia para margem, segunda para o logo
            ], colWidths=[180*mm])  # 10mm de margem esquerda (reduzida de 20mm)
            
            logo_table.setStyle(TableStyle([
                ('VALIGN', (0, 0), (-10, -10), 'TOP'),  # Alinha o conteúdo no topo
                ('LEFTPADDING', (0, 0), (0, 0), -40),  # Remove padding da célula vazia
                ('BOTTOMPADDING', (0, 0), (-1, -1), -70),  # Remove padding inferior
                ('TOPPADDING', (0, 0), (-1, -1), -80),  # Remove padding superior
                ('ALIGN', (1, 0), (1, 0), 'LEFT')  # Alinha o logo à esquerda
            ]))
            
            # Adiciona a tabela do logo ao story
            story.append(logo_table)
            # Adiciona um pequeno espaço após o logo
            story.append(Spacer(1, 15))
        except Exception as e:
            print(f"Erro ao carregar o logo: {str(e)}")
            pass

    story.append(Spacer(1, 30))

    # Título principal alinhado à esquerda
    titulo_style = ParagraphStyle(
        'TituloStyle',
        parent=styles['Heading1'],
        fontSize=34,
        leading=42,  # Espaçamento entre linhas (120% do tamanho da fonte)
        spaceAfter=50,  # Espaço após o parágrafo
        alignment=TA_LEFT,
        textColor=colors.black,
        fontName='Helvetica-Bold',
        textTransform='uppercase',  # Garante que o texto fique em maiúsculas
        letterSpacing=0.5,  # Espaçamento entre letras (em pontos)
        wordWrap='LTR'  # Quebra de palavras da esquerda para direita
    )
    story.append(Paragraph("RELATÓRIO DE <br/> AUDITORIA FISCAL", titulo_style))

    # Subtítulo na cor de preferência
    subtitulo_style = ParagraphStyle(
        'SubtituloStyle',
        parent=styles['Normal'],
        fontSize=18,
        spaceAfter=70,
        alignment=TA_LEFT,
        textColor=cor_preferencia
    )
    story.append(Paragraph("Movimentação de Entradas e Saídas de Notas Fiscais", subtitulo_style))

    # Dicionário com os nomes dos meses em português
    MESES_PT_BR = {
        1: 'JANEIRO',
        2: 'FEVEREIRO',
        3: 'MARÇO',
        4: 'ABRIL',
        5: 'MAIO',
        6: 'JUNHO',
        7: 'JULHO',
        8: 'AGOSTO',
        9: 'SETEMBRO',
        10: 'OUTUBRO',
        11: 'NOVEMBRO',
        12: 'DEZEMBRO'
    }

    # Mês e ano na cor de preferência
    if year and month:
        mes_nome = MESES_PT_BR.get(month, '').upper()
        periodo_style = ParagraphStyle(
            'PeriodoStyle',
            parent=styles['Normal'],
            fontSize=16,
            spaceAfter=70,
            alignment=TA_LEFT,
            textColor=cor_preferencia
        )
        story.append(Paragraph(f"{mes_nome} DE {year}", periodo_style))

    # Nome da empresa (maiúsculo e preto)
    empresa_style = ParagraphStyle(
        'EmpresaStyle',
        parent=styles['Heading2'],
        fontSize=18,
        spaceAfter=100,
        alignment=TA_LEFT,
        textColor=colors.black,
        fontName='Helvetica-Bold'
    )
    story.append(Paragraph(empresa.razao_social.upper(), empresa_style))

    # Preparado por (perto do fim da página)
    story.append(Spacer(1, 100))

    preparado_style = ParagraphStyle(
        'PreparadoStyle',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=5,
        alignment=TA_LEFT,
        textColor=cor_preferencia
    )
    story.append(Paragraph("Preparado por", preparado_style))

    nome_style = ParagraphStyle(
        'NomeStyle',
        parent=styles['Normal'],
        fontSize=14,
        spaceAfter=20,
        alignment=TA_LEFT,
        textColor=colors.black,
        fontName='Helvetica-Bold'
    )
    story.append(Paragraph(usuario_nome.upper(), nome_style))

    # Quebra de página
    story.append(PageBreak())

    # ===== SEGUNDA PÁGINA =====

    # Logo do escritório alinhado à esquerda com margem (segunda página)
    logo_path = None
    if escritorio and escritorio.logo_path:
        logo_full_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                    'front', escritorio.logo_path.lstrip('/'))
        if os.path.exists(logo_full_path):
            logo_path = logo_full_path

    if logo_path:
        try:
            # Reduzindo o tamanho do logo para melhor proporção
            logo = Image(logo_path, width=110*mm, height=70*mm)
            
            # Criando uma tabela com duas colunas: uma vazia para margem e outra para o logo
            logo_table = Table([
                [logo]  # Primeira célula vazia para margem, segunda para o logo
            ], colWidths=[180*mm])  # 10mm de margem esquerda (reduzida de 20mm)
            
            logo_table.setStyle(TableStyle([
                ('VALIGN', (0, 0), (-10, -10), 'TOP'),  # Alinha o conteúdo no topo
                ('LEFTPADDING', (0, 0), (0, 0), -40),  # Remove padding da célula vazia
                ('BOTTOMPADDING', (0, 0), (-1, -1), -50),  # Remove padding inferior
                ('TOPPADDING', (0, 0), (-1, -1), -80),  # Remove padding superior
                ('ALIGN', (1, 0), (1, 0), 'LEFT')  # Alinha o logo à esquerda
            ]))
            
            # Adiciona a tabela do logo ao story com espaçamento reduzido do topo
            story.append(logo_table)
        except Exception as e:
            print(f"Erro ao carregar o logo na segunda página: {str(e)}")
            pass

    # Título "RESUMO GERAL" (preto e maiúsculo)
    resumo_titulo_style = ParagraphStyle(
        'ResumoTituloStyle',
        parent=styles['Heading1'],
        fontSize=26,
        spaceAfter=40,
        alignment=TA_LEFT,
        textColor=colors.black,
        fontName='Helvetica-Bold'
    )
    story.append(Paragraph("RESUMO GERAL", resumo_titulo_style))

    # Validar se empresa está 100% auditada
    try:
        from services.auditoria_service import AuditoriaService
        validacao = AuditoriaService.validar_empresa_auditoria_completa(empresa.id, year, month)
    except Exception as e:
        # Se houver erro na validação, continuar sem validar (modo compatibilidade)
        print(f"Erro na validação de auditoria: {e}")
        validacao = {
            'auditoria_completa': True,  # Assumir completa para não bloquear
            'tributos_aplicaveis': ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal'],
            'tributos_auditados': ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal'],
            'tributos_pendentes': [],
            'total_operacoes_tributarias': 0,
            'detalhes': {}
        }

    # Comentando a validação temporariamente para debug
    # if not validacao['auditoria_completa']:
    #     # Se não está 100% auditada, mostrar mensagem de erro
    #     erro_style = ParagraphStyle(
    #         'ErroStyle',
    #         parent=styles['Normal'],
    #         fontSize=14,
    #         spaceAfter=20,
    #         alignment=TA_CENTER,
    #         textColor=colors.red,
    #         fontName='Helvetica-Bold'
    #     )
    #     story.append(Paragraph("ERRO: A empresa não está 100% auditada.", erro_style))
    #     story.append(Paragraph("Todos os tributos aplicáveis devem ser auditados antes de gerar o relatório geral.", erro_style))
    #     return story

    # Buscar dados consolidados da empresa
    from models import AuditoriaSumario
    try:
        sumarios = AuditoriaSumario.query.filter_by(empresa_id=empresa.id)
        if year and month:
            sumarios = sumarios.filter_by(ano=year, mes=month)
        sumarios = sumarios.all()

        # Calcular totais com proteção contra valores None
        total_notas = sum(s.total_notas or 0 for s in sumarios)
        total_produtos = sum(s.total_produtos or 0 for s in sumarios)
        total_operacoes = validacao.get('total_operacoes_tributarias', 0) or total_produtos
    except Exception as e:
        print(f"Erro ao buscar sumários: {e}")
        sumarios = []
        total_notas = 0
        total_produtos = 0
        total_operacoes = 0

    # Seção "DADOS ANALISADOS" (cor de preferência e maiúsculo)
    dados_titulo_style = ParagraphStyle(
        'DadosTituloStyle',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=0,
        alignment=TA_LEFT,
        textColor=cor_preferencia,
        fontName='Helvetica-Bold'
    )
    story.append(Paragraph("DADOS ANALISADOS", dados_titulo_style))

    # Cards dos dados analisados (3 cards lado a lado)
    card_value_style = ParagraphStyle(
        'CardValueStyle',
        parent=styles['Normal'],
        fontSize=32,
        spaceAfter=5,
        alignment=TA_CENTER,
        textColor=colors.black,
        fontName='Helvetica-Bold'
    )

    card_label_style = ParagraphStyle(
        'CardLabelStyle',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=10,
        alignment=TA_CENTER,
        textColor=colors.black,
        fontName='Helvetica-Bold'
    )

    # Criar cards individuais com maior espaçamento entre valor e texto
    card1 = f"<para align=center><font size=32 name=Helvetica-Bold>{total_notas}</font><br/><br/><font size=12 name=Helvetica>NOTAS FISCAIS</font></para>"
    card2 = f"<para align=center><font size=32 name=Helvetica-Bold>{total_produtos}</font><br/><br/><font size=12 name=Helvetica>PRODUTOS</font></para>"
    card3 = f"<para align=center><font size=32 name=Helvetica-Bold>{total_operacoes}</font><br/><br/><font size=12 name=Helvetica>OPERAÇÕES TRIBUTÁRIAS</font></para>"

    cards_data = [[
        Paragraph(card1, styles['Normal']),
        Paragraph(card2, styles['Normal']),
        Paragraph(card3, styles['Normal'])
    ]]

    cards_table = Table(cards_data, colWidths=[70*mm, 70*mm, 70*mm])
    cards_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('LEFTPADDING', (0, 0), (-1, -1), 10),
        ('RIGHTPADDING', (0, 0), (-1, -1), 10),
        ('TOPPADDING', (0, 0), (-1, -1), 20),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 20),
    ]))
    story.append(cards_table)
    story.append(Spacer(1, 40))

    # Seção "OPERAÇÕES TRIBUTÁRIAS" (cor de preferência e maiúsculo)
    operacoes_titulo_style = ParagraphStyle(
        'OperacoesTituloStyle',
        parent=styles['Heading2'],
        fontSize=16,
        alignment=TA_LEFT,
        textColor=cor_preferencia,
        fontName='Helvetica-Bold'
    )
    story.append(Paragraph("OPERAÇÕES TRIBUTÁRIAS", operacoes_titulo_style))

    # Cards dos tributos (6 cards, 3 por linha, 2 linhas)
    tipos_tributos = ['icms', 'icms_st', 'difal', 'ipi', 'pis', 'cofins']
    tributos_cards = []

    for i in range(0, 6, 3):  # Processar 3 cards por vez (2 linhas)
        linha_cards = []
        for j in range(3):
            if i + j < len(tipos_tributos):
                tipo_tributo = tipos_tributos[i + j]

                # Buscar sumário para este tributo
                sumario = next((s for s in sumarios if s.tipo_tributo == tipo_tributo), None)

                if sumario:
                    conforme = sumario.total_conforme or 0
                    inconsistente = sumario.total_inconsistente or 0
                    valor_inconsistente = float(sumario.valor_inconsistente_maior or 0) + float(sumario.valor_inconsistente_menor or 0)
                    cor_valor = colors.red if valor_inconsistente > 0 else colors.black
                else:
                    # Verificar se é não aplicável
                    if tipo_tributo in validacao['detalhes'] and validacao['detalhes'][tipo_tributo]['status'] == 'nao_aplicavel':
                        conforme = 0
                        inconsistente = 0
                        valor_inconsistente = 0
                        cor_valor = colors.black
                    else:
                        conforme = 0
                        inconsistente = 0
                        valor_inconsistente = 0
                        cor_valor = colors.black

                # Criar conteúdo do card com nome alinhado à esquerda
                nome_tributo = tipo_tributo.upper().replace('_', '-')
                cor_hex = cor_preferencia_hex if 'cor_preferencia_hex' in locals() else '#6f42c1'
                
                # Criar estilos de parágrafo
                estilo_titulo = ParagraphStyle(
                    'CardTituloStyle',
                    parent=styles['Normal'],
                    fontSize=14,
                    spaceAfter=0,  # Reduzido o espaço após o título
                    alignment=TA_LEFT,  # Alinha o título à esquerda
                    leftIndent=10,  # Pequeno recuo à esquerda
                    textColor=colors.HexColor(cor_hex),
                    fontName='Helvetica-Bold',
                    leading=14  # Altura da linha igual ao tamanho da fonte
                )
                
                estilo_conteudo = ParagraphStyle(
                    'CardConteudoStyle',
                    parent=styles['Normal'],
                    fontSize=12,
                    spaceAfter=0,  # Removido espaço após
                    alignment=TA_CENTER,
                    fontName='Helvetica-Bold',
                    leading=14  # Altura da linha igual ao tamanho da fonte
                )
                
                estilo_legenda = ParagraphStyle(
                    'CardLegendaStyle',
                    parent=styles['Normal'],
                    fontSize=10,
                    spaceAfter=0,  # Removido espaço após
                    alignment=TA_CENTER,
                    fontName='Helvetica',
                    leading=12  # Altura da linha igual ao tamanho da fonte
                )
                
                # Criar conteúdo do card
                from reportlab.lib.styles import getSampleStyleSheet
                
                # Título alinhado à esquerda
                titulo = Paragraph(f"{nome_tributo}", estilo_titulo)
                
                # Criar tabela para os valores conforme/inconsistente
                dados_table = Table([
                    [
                        Paragraph(f"<b>{conforme}</b>", estilo_conteudo), 
                        Paragraph(f"<b>{inconsistente}</b>", estilo_conteudo)
                    ],
                    [
                        Paragraph("Conforme", estilo_legenda), 
                        Paragraph("Inconsistente", estilo_legenda)
                    ]
                ], colWidths=[95, 95])  # Divide a largura do card em 2 colunas
                
                # Estilo para a tabela de dados
                dados_table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 0),  # Reduzido o padding inferior
                    ('TOPPADDING', (0, 0), (-1, -1), 0),      # Removido o padding superior
                    ('LEFTPADDING', (0, 0), (-1, -1), 0),     # Removido o padding esquerdo
                    ('RIGHTPADDING', (0, 0), (-1, -1), 0),    # Removido o padding direito
                ]))
                
                # Valor inconsistente
                valor_inconsistente_para = Paragraph(
                    f"<font color={'red' if valor_inconsistente > 0 else 'black'}>R$ {valor_inconsistente:,.2f}</font>", 
                    estilo_conteudo
                )
                
                # Criar tabela para o card com valor e legenda juntos
                valor_com_legenda = Table([
                    [valor_inconsistente_para],
                    [Paragraph("Valor Inconsistente", estilo_legenda)]
                ], colWidths=[190], hAlign='CENTER')  # Centraliza a tabela
                
                # Ajustar espaçamento entre valor e legenda
                valor_com_legenda.setStyle(TableStyle([
                    ('TOPPADDING', (0, 0), (-1, -1), 0),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
                    ('LEFTPADDING', (0, 0), (-1, -1), 0),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 0),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ]))
                
                # Criar tabela para o card
                card_table = Table([
                    [titulo],
                    [dados_table],  # Tabela com conforme/inconsistente
                    [valor_com_legenda]  # Valor e legenda juntos
                ], colWidths=[190], hAlign='CENTER')  # Centraliza a tabela
                
                # Estilo para a tabela do card
                card_table.setStyle(TableStyle([
                    ('LEFTPADDING', (0, 0), (-1, -1), 0),     # Remove padding interno
                    ('RIGHTPADDING', (0, 0), (-1, -1), 0),    # Remove padding interno
                    ('TOPPADDING', (0, 0), (0, 0), 8),        # Padding superior apenas no título
                    ('BOTTOMPADDING', (0, 0), (0, 0), 5),     # Espaço após o título
                    ('TOPPADDING', (1, 0), (-1, -1), 8),      # Espaçamento superior do conteúdo
                    ('BOTTOMPADDING', (1, 0), (-1, -1), 8),   # Espaçamento inferior do conteúdo
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),   # Centraliza verticalmente
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),    # Centraliza horizontalmente
                    ('BOX', (0, 0), (-1, -1), 0.5, colors.black),  # Borda externa
                    ('ROUNDEDCORNERS', [10, 10, 10, 10]),     # Cantos arredondados
                ]))
                
                # Adicionar um contêiner com margem para criar espaçamento entre os cards
                container = Table([
                    [card_table]
                ], colWidths=[190])  # Largura do card
                
                container.setStyle(TableStyle([
                    ('LEFTPADDING', (0, 0), (-1, -1), 0),    # Reduzido espaçamento horizontal entre cards
                    ('RIGHTPADDING', (0, 0), (-1, -1), 0),     # Reduzido espaçamento horizontal entre cards
                    ('TOPPADDING', (0, 0), (-1, -1), 0),       # Reduzido espaçamento vertical entre cards
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 0),    # Reduzido espaçamento vertical entre cards
                ]))
                
                linha_cards.append(container)
            else:
                linha_cards.append("")

        tributos_cards.append(linha_cards)

    # Criar tabela dos cards de tributos
    tributos_table = Table(tributos_cards, colWidths=[70*mm, 70*mm, 70*mm])
    tributos_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('LEFTPADDING', (0, 0), (-1, -1), 10),
        ('RIGHTPADDING', (0, 0), (-1, -1), 10),
        ('TOPPADDING', (0, 0), (-1, -1), 15),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 15),
    ]))
    story.append(tributos_table)
    story.append(Spacer(1, 40))

    # Calcular totais de inconsistências com proteção contra erros
    try:
        total_inconsistencias = sum(
            float(s.valor_inconsistente_maior or 0) + float(s.valor_inconsistente_menor or 0)
            for s in sumarios
        )
        total_a_maior = sum(float(s.valor_inconsistente_maior or 0) for s in sumarios)
        total_a_menor = sum(float(s.valor_inconsistente_menor or 0) for s in sumarios)
    except Exception as e:
        print(f"Erro ao calcular totais de inconsistências: {e}")
        total_inconsistencias = 0
        total_a_maior = 0
        total_a_menor = 0

    # Total das inconsistências apuradas (centralizado)
    total_style = ParagraphStyle(
        'TotalStyle',
        parent=styles['Normal'],
        fontSize=24,
        spaceAfter=15,
        alignment=TA_CENTER,
        textColor=colors.black,
        fontName='Helvetica-Bold'
    )

    total_label_style = ParagraphStyle(
        'TotalLabelStyle',
        parent=styles['Normal'],
        fontSize=14,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.red,
        fontName='Helvetica-Bold'
    )

    story.append(Paragraph(f"R$ {total_inconsistencias:,.2f}", total_style))
    story.append(Paragraph("TOTAL DAS INCONSISTÊNCIAS APURADAS", total_label_style))

    # Valores a maior e a menor (lado a lado)
    valor_style = ParagraphStyle(
        'ValorStyle',
        parent=styles['Normal'],
        fontSize=18,
        spaceAfter=5,
        alignment=TA_CENTER,
        textColor=colors.black,
        fontName='Helvetica-Bold'
    )

    valor_label_style = ParagraphStyle(
        'ValorLabelStyle',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=40,
        alignment=TA_CENTER,
        textColor=colors.red,
        fontName='Helvetica-Bold'
    )

    # Criar tabela com os dois valores
    valor1_content = f"<para align=center><font size=18 name=Helvetica-Bold>R$ {total_a_maior:,.2f}</font><br/><font size=12 name=Helvetica-Bold color=red>PAGANDO MAIS IMPOSTO</font></para>"
    valor2_content = f"<para align=center><font size=18 name=Helvetica-Bold>R$ {total_a_menor:,.2f}</font><br/><font size=12 name=Helvetica-Bold color=red>PAGANDO MENOS IMPOSTO</font></para>"

    valores_data = [[
        Paragraph(valor1_content, styles['Normal']),
        Paragraph(valor2_content, styles['Normal'])
    ]]

    valores_table = Table(valores_data, colWidths=[105*mm, 105*mm])
    valores_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('TOPPADDING', (0, 0), (-1, -1), 20),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 20),
    ]))
    story.append(valores_table)
    
    # Adicionar detalhamento de inconsistências
    story = _adicionar_detalhamento_inconsistencias(story, empresa.id, year, month, status)
    
    return story


def _adicionar_detalhamento_inconsistencias(story, empresa_id, year=None, month=None, status='inconsistente'):
    """
    Adiciona uma nova seção ao relatório com o detalhamento das inconsistências
    por nota fiscal e produto.
    """
    from reportlab.platypus import Paragraph, Spacer, Table, TableStyle, PageBreak, KeepTogether, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib import colors
    from reportlab.lib.units import mm, inch
    from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT
    from sqlalchemy import func, or_
    from models import AuditoriaResultado, Tributo, NotaFiscalItem, Produto, Cliente, Empresa, Escritorio
    import os
    
    styles = getSampleStyleSheet()
    
    # Estilo para o título da seção
    titulo_style = ParagraphStyle(
        'TituloDetalhamento',
        parent=styles['Heading1'],
        fontSize=20,
        spaceAfter=20,
        alignment=TA_LEFT,
        textColor=colors.HexColor('#6f42c1'),  # Mesma cor do tema
        fontName='Helvetica-Bold'
    )
    
    # Estilo para o número da nota fiscal
    nf_style = ParagraphStyle(
        'NFStyle',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=10,
        alignment=TA_LEFT,
        textColor=colors.HexColor('#6f42c1'),
        fontName='Helvetica-Bold'
    )
    
    # Estilo para o produto
    produto_style = ParagraphStyle(
        'ProdutoStyle',
        parent=styles['Heading3'],
        fontSize=14,
        spaceAfter=8,
        alignment=TA_LEFT,
        textColor=colors.black,
        fontName='Helvetica-Bold'
    )
    
    # Estilo para o tributo
    tributo_style = ParagraphStyle(
        'TributoStyle',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=5,
        alignment=TA_LEFT,
        textColor=colors.darkblue,
        fontName='Helvetica-Bold'
    )
    
    # Estilo para os detalhes
    detalhe_style = ParagraphStyle(
        'DetalheStyle',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=5,
        alignment=TA_LEFT,
        textColor=colors.black,
        fontName='Helvetica',
        leading=12
    )
    
    # Estilo para as observações
    obs_style = ParagraphStyle(
        'ObservacaoStyle',
        parent=styles['Italic'],
        fontSize=10,
        spaceAfter=15,
        alignment=TA_LEFT,
        textColor=colors.gray,
        fontName='Helvetica-Oblique',
        leading=12
    )
    
    # Função para adicionar o cabeçalho com logo em uma nova página
    def adicionar_cabecalho():
        # Adicionar quebra de página
        story.append(PageBreak())
        
        # Buscar empresa e escritório para obter o logo (se ainda não estiverem carregados)
        nonlocal empresa, escritorio, logo_path
        if empresa is None or escritorio is None:
            empresa = db.session.get(Empresa, empresa_id)
            escritorio = db.session.get(Escritorio, empresa.escritorio_id) if empresa and empresa.escritorio_id else None
        
        # Configurar o caminho do logo (se ainda não estiver configurado)
        if logo_path is None and escritorio and escritorio.logo_path:
            logo_full_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                        'front', escritorio.logo_path.lstrip('/'))
            if os.path.exists(logo_full_path):
                logo_path = logo_full_path

        # Adicionar logo do escritório
        if logo_path:
            try:
                # Reduzindo o tamanho do logo para melhor proporção
                logo = Image(logo_path, width=110*mm, height=70*mm)
                
                # Criando uma tabela com o logo
                logo_table = Table([
                    [logo]  # Logo alinhado à esquerda
                ], colWidths=[180*mm])
                
                logo_table.setStyle(TableStyle([
                    ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                    ('LEFTPADDING', (0, 0), (0, 0), -40),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), -50),
                    ('TOPPADDING', (0, 0), (-1, -1), -80),
                    ('ALIGN', (0, 0), (0, 0), 'LEFT')
                ]))
                
                story.append(logo_table)
                story.append(Spacer(1, 20))  # Espaço após o logo
            except Exception as e:
                print(f"Erro ao carregar o logo: {str(e)}")
        
        # Adicionar título da seção em preto
        titulo_style.textColor = colors.black
        story.append(Paragraph("DETALHAMENTO DAS INCONSISTÊNCIAS", titulo_style))
        story.append(Spacer(1, 30))  # Espaço após o título
    
    # Inicializar variáveis para o cabeçalho
    empresa = None
    escritorio = None
    logo_path = None
    
    # Adicionar o primeiro cabeçalho
    adicionar_cabecalho()
    
    # Buscar notas fiscais com inconsistências
    try:
        # Primeiro, verificar se existem inconsistências para evitar erros
        tem_inconsistencias = db.session.query(
            db.exists().where(
                AuditoriaResultado.status == 'inconsistente',
                Tributo.empresa_id == empresa_id
            )
        ).scalar()
        
        if not tem_inconsistencias:
            story.append(Paragraph("Nenhuma inconsistência encontrada para o período selecionado.", styles['Normal']))
            return story
            
        # Primeiro, buscar as notas fiscais com inconsistências
        subquery = db.session.query(
            AuditoriaResultado.tributo_id
        ).filter(
            AuditoriaResultado.status == 'inconsistente'
        ).distinct().subquery()
        
        # Primeiro, obter os IDs dos itens com inconsistências
        subq = db.session.query(
            NotaFiscalItem.id.label('item_id'),
            Tributo.id.label('tributo_id'),
            Tributo.data_emissao,
            Tributo.numero_nf,
            Cliente.razao_social.label('cliente_razao_social')
        ).join(
            Tributo,
            Tributo.nota_fiscal_item_id == NotaFiscalItem.id
        ).join(
            subquery,
            subquery.c.tributo_id == Tributo.id
        ).join(
            Cliente,
            Cliente.id == Tributo.cliente_id
        ).filter(
            Tributo.empresa_id == empresa_id
        )
        
        # Aplicar filtros de data se fornecidos
        if year:
            subq = subq.filter(db.extract('year', Tributo.data_emissao) == year)
            if month:
                subq = subq.filter(db.extract('month', Tributo.data_emissao) == month)
        
        # Ordenar a subconsulta
        subq = subq.order_by(
            Tributo.data_emissao,
            Tributo.numero_nf
        ).subquery()
        
        # Agora buscar os dados completos baseados nos IDs já filtrados e ordenados
        itens_com_inconsistencias = db.session.query(
            NotaFiscalItem.id.label('item_id'),
            NotaFiscalItem.numero_nf,
            subq.c.cliente_razao_social,
            NotaFiscalItem.data_emissao,
            subq.c.tributo_id,
            subq.c.data_emissao,
            subq.c.numero_nf
        ).join(
            subq,
            subq.c.item_id == NotaFiscalItem.id
        ).all()
        
        # Agrupar itens por nota fiscal
        notas_fiscais = {}
        for item in itens_com_inconsistencias:
            chave = (item.numero_nf, item.cliente_razao_social)
            if chave not in notas_fiscais:
                notas_fiscais[chave] = {
                    'numero_nf': item.numero_nf,
                    'cliente_razao_social': item.cliente_razao_social,
                    'data_emissao': item.data_emissao,
                    'itens': []
                }
            notas_fiscais[chave]['itens'].append(item)
        
        # Processar cada nota fiscal
        for nf_chave, nf in notas_fiscais.items():
            # Criar conteúdo da nota fiscal
            nf_content = []
            
            # Adicionar cabeçalho da nota fiscal
            nf_text = f"NOTA FISCAL: {nf['numero_nf']} - CLIENTE: {nf['cliente_razao_social']} - EMISSÃO: {nf['data_emissao'].strftime('%d/%m/%Y')}"
            nf_content.append(Paragraph(nf_text, nf_style))
            
            # Adicionar cabeçalho da nota fiscal ao story
            story.append(Paragraph(nf_text, nf_style))
            
            # Processar cada item da nota fiscal
            for i, item in enumerate(nf['itens']):
                # Buscar o produto pelo produto_id
                produto = db.session.query(
                    NotaFiscalItem.produto_id,
                    Produto.descricao,
                    NotaFiscalItem.id.label('item_id'),
                    Produto.codigo  # Adicionando o código do produto
                ).join(
                    Produto,
                    Produto.id == NotaFiscalItem.produto_id
                ).filter(
                    NotaFiscalItem.id == item.item_id
                ).first()
                
                if not produto:
                    continue
                    
                # Inicializar conteúdo do produto
                produto_content = []
                
                # Adicionar cabeçalho do produto
                produto_text = f"PRODUTO: {produto.codigo} - {produto.descricao}"
                produto_content.append(Paragraph(produto_text, produto_style))
                
                # Buscar inconsistências para este item
                inconsistencias = db.session.query(AuditoriaResultado).filter(
                    AuditoriaResultado.nota_fiscal_item_id == item.item_id,
                    AuditoriaResultado.status == 'inconsistente',
                    or_(
                        AuditoriaResultado.inconsistencia_valor == True,
                        AuditoriaResultado.inconsistencia_cst == True,
                        AuditoriaResultado.inconsistencia_origem == True,
                        AuditoriaResultado.inconsistencia_aliquota == True,
                        AuditoriaResultado.inconsistencia_base_calculo == True
                    )
                ).all()
                
                if not inconsistencias:
                    continue
                
                # Agrupar por tipo de tributo
                tributos_detalhes = {}
                for item in inconsistencias:
                    tipo_tributo = item.tipo_tributo.upper() if item.tipo_tributo else 'DESCONHECIDO'
                    if tipo_tributo not in tributos_detalhes:
                        tributos_detalhes[tipo_tributo] = []
                    tributos_detalhes[tipo_tributo].append(item)
                
                # Exibir as inconsistências agrupadas por tipo de tributo
                for tipo_tributo, itens in tributos_detalhes.items():
                    # Adicionar nome do tributo
                    tributo_text = f"Tributo: {tipo_tributo}"
                    produto_content.append(Paragraph(tributo_text, tributo_style))
                    
                    # Adicionar detalhes das inconsistências
                    for item in itens:
                        # Verificar qual tipo de inconsistência
                        inconsistencias = []
                        if item.inconsistencia_valor:
                            valor_nota = f"{item.valor_nota:,.2f}" if item.valor_nota is not None else "N/A"
                            valor_calculado = f"{item.valor_calculado:,.2f}" if item.valor_calculado is not None else "N/A"
                            inconsistencias.append(f"Valor: Nota={valor_nota} | Cenário={valor_calculado}")
                            
                        if item.inconsistencia_cst and (item.cst_nota or item.cst_cenario):
                            inconsistencias.append(f"CST: Nota={item.cst_nota or 'N/A'} | Cenário={item.cst_cenario or 'N/A'}")
                            
                        if item.inconsistencia_origem and (item.origem_nota or item.origem_cenario):
                            inconsistencias.append(f"Origem: Nota={item.origem_nota or 'N/A'} | Cenário={item.origem_cenario or 'N/A'}")
                            
                        if item.inconsistencia_aliquota and (item.aliquota_nota is not None or item.aliquota_cenario is not None):
                            aliquota_nota = f"{item.aliquota_nota:.2f}%" if item.aliquota_nota is not None else "N/A"
                            aliquota_cenario = f"{item.aliquota_cenario:.2f}%" if item.aliquota_cenario is not None else "N/A"
                            inconsistencias.append(f"Alíquota: Nota={aliquota_nota} | Cenário={aliquota_cenario}")
                            
                        if item.inconsistencia_base_calculo and (item.base_calculo_nota is not None or item.base_calculo_calculada is not None):
                            base_nota = f"{item.base_calculo_nota:,.2f}" if item.base_calculo_nota is not None else "N/A"
                            base_calculada = f"{item.base_calculo_calculada:,.2f}" if item.base_calculo_calculada is not None else "N/A"
                            inconsistencias.append(f"Base de Cálculo: Nota={base_nota} | Cenário={base_calculada}")
                        
                        # Adicionar as inconsistências ao conteúdo
                        for inconsistencia in inconsistencias:
                            produto_content.append(Paragraph(f"- {inconsistencia}", detalhe_style))
                        
                        # Adicionar observações do analista se existirem
                        if item.observacoes_analista:
                            obs_text = f"<i>Observação: {item.observacoes_analista}</i>"
                            produto_content.append(Paragraph(obs_text, obs_style))
                
                # Adicionar espaço entre produtos
                produto_content.append(Spacer(1, 10))
                
                # Adicionar conteúdo do produto ao story
                story.extend(produto_content)
                
                # Adicionar quebra de página com cabeçalho para o próximo produto
                # A verificação se há mais itens é feita no loop principal
                if i < len(nf['itens']) - 1:  # Se não for o último item
                    adicionar_cabecalho()
                
        return story
        
    except Exception as e:
        import traceback
        print(f"Erro ao gerar detalhamento de inconsistências: {str(e)}")
        traceback.print_exc()  # Isso vai ajudar a identificar a linha exata do erro
        
        # Adicionar mensagem de erro ao relatório
        error_style = ParagraphStyle(
            'ErrorStyle',
            parent=styles['Normal'],
            fontSize=10,
            textColor=colors.red,
            spaceAfter=20
        )
        
        # Mensagem de erro mais detalhada
        error_msg = f"Ocorreu um erro ao gerar o detalhamento de inconsistências: {str(e)}. Verifique os logs para mais detalhes."
        story.append(Paragraph(error_msg, error_style))
        
        return story