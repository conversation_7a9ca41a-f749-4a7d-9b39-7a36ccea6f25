from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, Usuario, Empresa, AuditoriaSumario
from models.auditoria_status_manual import AuditoriaStatusManual
from sqlalchemy import and_, func, distinct
from datetime import datetime

# Criar blueprint
dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/api/dashboard/estatisticas', methods=['GET'])
@jwt_required()
def obter_estatisticas_dashboard():
    """
    Obtém estatísticas para o dashboard principal
    """
    try:
        # Obter parâmetros de filtro
        year = request.args.get('year', type=int, default=datetime.now().year)
        month = request.args.get('month', type=int, default=datetime.now().month)

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Obter empresas do usuário baseado na hierarquia
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores veem todas as empresas
            empresas_query = Empresa.query
        elif usuario.tipo_usuario == 'escritorio':
            # Usuários do tipo escritório veem empresas do seu escritório
            empresas_query = Empresa.query.filter_by(escritorio_id=usuario.escritorio_id)
        else:
            # Usuários comuns veem apenas empresas permitidas
            empresas_permitidas = usuario.empresas_permitidas or []
            if not empresas_permitidas:
                return jsonify({
                    "success": True,
                    "estatisticas": {
                        "total_empresas": 0,
                        "empresas_auditadas": 0,
                        "empresas_pendentes": 0
                    }
                }), 200
            empresas_query = Empresa.query.filter(Empresa.id.in_(empresas_permitidas))

        # Contar total de empresas
        total_empresas = empresas_query.count()

        # Obter IDs das empresas
        empresa_ids = [e.id for e in empresas_query.all()]

        if not empresa_ids:
            return jsonify({
                "success": True,
                "estatisticas": {
                    "total_empresas": 0,
                    "empresas_auditadas": 0,
                    "empresas_pendentes": 0
                }
            }), 200

        # Verificar quais empresas têm auditoria completa (todos os 6 tributos)
        # Uma empresa é considerada auditada se tem sumários OU status manual para todos os 6 tributos
        tributos_obrigatorios = ['icms', 'icms_st', 'difal', 'ipi', 'pis', 'cofins']

        # Buscar empresas que têm sumários para tributos no período
        empresas_com_sumarios = db.session.query(
            AuditoriaSumario.empresa_id,
            func.count(distinct(AuditoriaSumario.tipo_tributo)).label('tributos_count')
        ).filter(
            AuditoriaSumario.empresa_id.in_(empresa_ids),
            AuditoriaSumario.ano == year,
            AuditoriaSumario.mes == month,
            AuditoriaSumario.tipo_tributo.in_(tributos_obrigatorios)
        ).group_by(AuditoriaSumario.empresa_id).all()

        # Buscar empresas com status manuais
        empresas_com_status_manual = db.session.query(
            AuditoriaStatusManual.empresa_id,
            func.count(distinct(AuditoriaStatusManual.tipo_tributo)).label('tributos_manuais_count')
        ).filter(
            AuditoriaStatusManual.empresa_id.in_(empresa_ids),
            AuditoriaStatusManual.status == 'nao_aplicavel'
        ).group_by(AuditoriaStatusManual.empresa_id).all()

        # Criar mapas para facilitar contagem
        sumarios_map = {e.empresa_id: e.tributos_count for e in empresas_com_sumarios}
        status_manual_map = {e.empresa_id: e.tributos_manuais_count for e in empresas_com_status_manual}

        # Contar empresas que têm todos os 6 tributos auditados (sumários + status manual)
        empresas_auditadas = 0
        for empresa_id in empresa_ids:
            tributos_com_sumario = sumarios_map.get(empresa_id, 0)
            tributos_com_status_manual = status_manual_map.get(empresa_id, 0)
            total_tributos_auditados = tributos_com_sumario + tributos_com_status_manual

            if total_tributos_auditados >= 6:
                empresas_auditadas += 1

        empresas_pendentes = total_empresas - empresas_auditadas

        return jsonify({
            "success": True,
            "estatisticas": {
                "total_empresas": total_empresas,
                "empresas_auditadas": empresas_auditadas,
                "empresas_pendentes": empresas_pendentes
            }
        }), 200

    except Exception as e:
        print(f"Erro ao obter estatísticas do dashboard: {str(e)}")
        return jsonify({
            "success": False,
            "message": "Erro interno do servidor"
        }), 500


@dashboard_bp.route('/api/dashboard/empresas', methods=['GET'])
@jwt_required()
def listar_empresas_dashboard():
    """
    Lista empresas para o dashboard com status de auditoria
    """
    try:
        # Obter parâmetros de filtro
        year = request.args.get('year', type=int, default=datetime.now().year)
        month = request.args.get('month', type=int, default=datetime.now().month)

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Obter empresas do usuário baseado na hierarquia
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores veem todas as empresas
            empresas = Empresa.query.all()
        elif usuario.tipo_usuario == 'escritorio':
            # Usuários do tipo escritório veem empresas do seu escritório
            empresas = Empresa.query.filter_by(escritorio_id=usuario.escritorio_id).all()
        else:
            # Usuários comuns veem apenas empresas permitidas
            empresas_permitidas = usuario.empresas_permitidas or []
            if not empresas_permitidas:
                return jsonify({
                    "success": True,
                    "empresas": []
                }), 200
            empresas = Empresa.query.filter(Empresa.id.in_(empresas_permitidas)).all()

        # Preparar lista de empresas com status de auditoria
        empresas_resultado = []
        tributos_obrigatorios = ['icms', 'icms_st', 'difal', 'ipi', 'pis', 'cofins']

        for empresa in empresas:
            # Buscar sumários de auditoria para a empresa no período
            sumarios = AuditoriaSumario.query.filter(
                AuditoriaSumario.empresa_id == empresa.id,
                AuditoriaSumario.ano == year,
                AuditoriaSumario.mes == month,
                AuditoriaSumario.tipo_tributo.in_(tributos_obrigatorios)
            ).all()

            # Buscar status manuais da empresa
            status_manuais = AuditoriaStatusManual.obter_status_empresa(empresa.id)
            tributos_nao_aplicaveis = {s.tipo_tributo for s in status_manuais if s.status == 'nao_aplicavel'}

            # Criar mapa de tributos auditados (incluindo os marcados como não aplicáveis)
            tributos_auditados = {s.tipo_tributo for s in sumarios}
            tributos_auditados.update(tributos_nao_aplicaveis)  # Adicionar os não aplicáveis
            tributos_pendentes = set(tributos_obrigatorios) - tributos_auditados

            # Determinar status geral
            if len(tributos_auditados) == 6:
                status_auditoria = 'completa'
            elif len(tributos_auditados) > 0:
                status_auditoria = 'parcial'
            else:
                status_auditoria = 'pendente'

            # Calcular totais de inconsistências
            total_inconsistencias = sum(s.total_inconsistente for s in sumarios)
            total_valor_inconsistente = sum(
                float(s.valor_inconsistente_maior or 0) + float(s.valor_inconsistente_menor or 0)
                for s in sumarios
            )

            empresa_data = {
                'id': empresa.id,
                'razao_social': empresa.razao_social or empresa.nome_fantasia or empresa.nome,
                'cnpj': empresa.cnpj,
                'status_auditoria': status_auditoria,
                'tributos_auditados': list(tributos_auditados),
                'tributos_pendentes': list(tributos_pendentes),
                'total_inconsistencias': total_inconsistencias,
                'total_valor_inconsistente': total_valor_inconsistente,
                'progresso_auditoria': len(tributos_auditados) / 6 * 100  # Percentual de conclusão
            }

            empresas_resultado.append(empresa_data)

        # Ordenar por status (completa, parcial, pendente) e depois por nome
        def sort_key(empresa):
            status_order = {'completa': 0, 'parcial': 1, 'pendente': 2}
            return (status_order.get(empresa['status_auditoria'], 3), empresa['razao_social'])

        empresas_resultado.sort(key=sort_key)

        return jsonify({
            "success": True,
            "empresas": empresas_resultado
        }), 200

    except Exception as e:
        print(f"Erro ao listar empresas do dashboard: {str(e)}")
        return jsonify({
            "success": False,
            "message": "Erro interno do servidor"
        }), 500


@dashboard_bp.route('/api/dashboard/empresa/<int:empresa_id>', methods=['GET'])
@jwt_required()
def obter_dashboard_empresa(empresa_id):
    """
    Obtém dashboard específico de uma empresa com estatísticas por tributo
    """
    try:
        # Obter parâmetros de filtro
        year = request.args.get('year', type=int, default=datetime.now().year)
        month = request.args.get('month', type=int, default=datetime.now().month)

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Verificar se o usuário tem acesso à empresa
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa:
            return jsonify({"message": "Empresa não encontrada"}), 404

        # Verificar permissões para visualizar a empresa
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores podem ver qualquer empresa
            pass
        elif usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id:
            # Usuários do tipo escritório só podem ver empresas do seu próprio escritório
            pass
        elif usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas:
            # Usuários comuns só podem ver empresas permitidas
            pass
        else:
            return jsonify({"message": "Você não tem permissão para visualizar esta empresa"}), 403

        # Buscar sumários de auditoria para todos os tributos
        tributos_obrigatorios = ['icms', 'icms_st', 'difal', 'ipi', 'pis', 'cofins']
        sumarios = AuditoriaSumario.query.filter(
            AuditoriaSumario.empresa_id == empresa_id,
            AuditoriaSumario.ano == year,
            AuditoriaSumario.mes == month,
            AuditoriaSumario.tipo_tributo.in_(tributos_obrigatorios)
        ).all()

        # Criar mapa de sumários por tipo de tributo
        sumarios_map = {s.tipo_tributo: s for s in sumarios}

        # Buscar status manuais da empresa
        status_manuais = AuditoriaStatusManual.obter_status_empresa(empresa_id)
        status_manuais_map = {s.tipo_tributo: s for s in status_manuais}

        # Preparar dados dos cards por tributo
        cards_tributos = []

        for tributo in tributos_obrigatorios:
            sumario = sumarios_map.get(tributo)
            status_manual = status_manuais_map.get(tributo)

            if sumario:
                # Tributo auditado normalmente
                card_data = {
                    'tipo_tributo': tributo,
                    'nome_tributo': tributo.upper().replace('_', '-'),
                    'auditado': True,
                    'status_tipo': 'auditado',
                    'total_notas': sumario.total_notas,
                    'total_produtos': sumario.total_produtos,
                    'total_conforme': sumario.total_conforme,
                    'total_inconsistente': sumario.total_inconsistente,
                    'valor_total_notas': float(sumario.valor_total_notas or 0),
                    'valor_total_cenarios': float(sumario.valor_total_cenarios or 0),
                    'valor_inconsistente_maior': float(sumario.valor_inconsistente_maior or 0),
                    'valor_inconsistente_menor': float(sumario.valor_inconsistente_menor or 0),
                    'notas_conformes': sumario.notas_conformes,
                    'notas_inconsistentes': sumario.notas_inconsistentes,
                    'data_atualizacao': sumario.data_atualizacao.isoformat() if sumario.data_atualizacao else None,
                    'status_manual': None
                }
            elif status_manual and status_manual.status == 'nao_aplicavel':
                # Tributo marcado como não aplicável
                card_data = {
                    'tipo_tributo': tributo,
                    'nome_tributo': tributo.upper().replace('_', '-'),
                    'auditado': True,
                    'status_tipo': 'nao_aplicavel',
                    'total_notas': 0,
                    'total_produtos': 0,
                    'total_conforme': 0,
                    'total_inconsistente': 0,
                    'valor_total_notas': 0,
                    'valor_total_cenarios': 0,
                    'valor_inconsistente_maior': 0,
                    'valor_inconsistente_menor': 0,
                    'notas_conformes': 0,
                    'notas_inconsistentes': 0,
                    'data_atualizacao': None,
                    'status_manual': {
                        'motivo': status_manual.motivo,
                        'data_marcacao': status_manual.data_marcacao.isoformat(),
                        'usuario_nome': status_manual.usuario.nome if status_manual.usuario else None
                    }
                }
            else:
                # Tributo não auditado
                card_data = {
                    'tipo_tributo': tributo,
                    'nome_tributo': tributo.upper().replace('_', '-'),
                    'auditado': False,
                    'status_tipo': 'pendente',
                    'total_notas': 0,
                    'total_produtos': 0,
                    'total_conforme': 0,
                    'total_inconsistente': 0,
                    'valor_total_notas': 0,
                    'valor_total_cenarios': 0,
                    'valor_inconsistente_maior': 0,
                    'valor_inconsistente_menor': 0,
                    'notas_conformes': 0,
                    'notas_inconsistentes': 0,
                    'data_atualizacao': None,
                    'status_manual': None
                }

            cards_tributos.append(card_data)

        # Calcular estatísticas gerais da empresa
        total_geral = {
            'total_notas': sum(card['total_notas'] for card in cards_tributos),
            'total_produtos': sum(card['total_produtos'] for card in cards_tributos),
            'total_conforme': sum(card['total_conforme'] for card in cards_tributos),
            'total_inconsistente': sum(card['total_inconsistente'] for card in cards_tributos),
            'valor_total_inconsistente': sum(
                card['valor_inconsistente_maior'] + card['valor_inconsistente_menor']
                for card in cards_tributos
            ),
            'tributos_auditados': len([card for card in cards_tributos if card['auditado']]),
            'tributos_pendentes': len([card for card in cards_tributos if not card['auditado']])
        }

        return jsonify({
            "success": True,
            "empresa": {
                "id": empresa.id,
                "razao_social": empresa.razao_social or empresa.nome_fantasia or empresa.nome,
                "cnpj": empresa.cnpj
            },
            "periodo": {
                "ano": year,
                "mes": month
            },
            "cards_tributos": cards_tributos,
            "total_geral": total_geral
        }), 200

    except Exception as e:
        print(f"Erro ao obter dashboard da empresa: {str(e)}")
        return jsonify({
            "success": False,
            "message": "Erro interno do servidor"
        }), 500


@dashboard_bp.route('/api/dashboard/empresa/<int:empresa_id>/graficos', methods=['GET'])
@jwt_required()
def obter_graficos_empresa(empresa_id):
    """
    Obtém dados para gráficos da empresa com histórico dos últimos 12 meses
    """
    try:
        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Verificar se o usuário tem acesso à empresa
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa:
            return jsonify({"message": "Empresa não encontrada"}), 404

        # Verificar permissões de acesso à empresa
        if not usuario.is_admin and usuario.tipo_usuario != 'admin':
            if usuario.tipo_usuario == 'escritorio':
                if empresa.escritorio_id != usuario.escritorio_id:
                    return jsonify({"message": "Acesso negado"}), 403
            else:
                empresas_permitidas = usuario.empresas_permitidas or []
                if empresa_id not in empresas_permitidas:
                    return jsonify({"message": "Acesso negado"}), 403

        # Calcular período dos últimos 12 meses (mês atual + 11 anteriores)
        data_atual = datetime.now()
        meses_dados = []

        # Função auxiliar para calcular mês anterior
        def mes_anterior(ano, mes):
            if mes == 1:
                return ano - 1, 12
            return ano, mes - 1

        ano_atual, mes_atual = data_atual.year, data_atual.month

        for i in range(12):
            if i == 0:
                ano, mes = ano_atual, mes_atual
            else:
                ano, mes = mes_anterior(ano, mes)

            # Criar label do mês
            meses_nomes = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
                          'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']
            label = f"{meses_nomes[mes-1]} {ano}"

            meses_dados.append({
                'ano': ano,
                'mes': mes,
                'label': label
            })

        # Inverter para ordem cronológica (mais antigo primeiro)
        meses_dados.reverse()

        # Buscar dados de auditoria_sumario para todos os meses
        tipos_tributos = ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal']
        dados_graficos = {
            'labels': [mes['label'] for mes in meses_dados],
            'geral': {
                'valores_inconsistentes': [0] * 12,
                'total_inconsistencias': [0] * 12
            },
            'tributos': {}
        }

        # Inicializar dados dos tributos
        for tributo in tipos_tributos:
            dados_graficos['tributos'][tributo] = {
                'valores_inconsistentes': [0] * 12,
                'total_inconsistencias': [0] * 12
            }

        # Buscar dados do banco para cada mês
        for idx, mes_data in enumerate(meses_dados):
            sumarios = AuditoriaSumario.query.filter_by(
                empresa_id=empresa_id,
                ano=mes_data['ano'],
                mes=mes_data['mes']
            ).all()

            valor_total_mes = 0
            inconsistencias_total_mes = 0

            for sumario in sumarios:
                valor_inconsistente = float(sumario.valor_inconsistente_maior or 0) + float(sumario.valor_inconsistente_menor or 0)
                total_inconsistente = sumario.total_inconsistente or 0

                # Adicionar ao total geral
                valor_total_mes += valor_inconsistente
                inconsistencias_total_mes += total_inconsistente

                # Adicionar aos dados específicos do tributo
                if sumario.tipo_tributo in dados_graficos['tributos']:
                    dados_graficos['tributos'][sumario.tipo_tributo]['valores_inconsistentes'][idx] = valor_inconsistente
                    dados_graficos['tributos'][sumario.tipo_tributo]['total_inconsistencias'][idx] = total_inconsistente

            # Definir totais gerais para o mês
            dados_graficos['geral']['valores_inconsistentes'][idx] = valor_total_mes
            dados_graficos['geral']['total_inconsistencias'][idx] = inconsistencias_total_mes

        return jsonify({
            "success": True,
            "empresa": {
                "id": empresa.id,
                "razao_social": empresa.razao_social or empresa.nome_fantasia or empresa.nome,
                "cnpj": empresa.cnpj
            },
            "periodo": {
                "inicio": meses_dados[0]['label'],
                "fim": meses_dados[-1]['label']
            },
            "dados": dados_graficos
        }), 200

    except Exception as e:
        print(f"Erro ao obter gráficos da empresa: {str(e)}")
        return jsonify({
            "success": False,
            "message": "Erro interno do servidor"
        }), 500


@dashboard_bp.route('/api/dashboard/empresa/<int:empresa_id>/status-manual', methods=['POST'])
@jwt_required()
def marcar_status_manual(empresa_id):
    """
    Marca um tributo como não aplicável ou aplicável
    """
    try:
        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Verificar se o usuário tem acesso à empresa
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa:
            return jsonify({"message": "Empresa não encontrada"}), 404

        # Verificar permissões para modificar a empresa
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            pass
        elif usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id:
            pass
        elif usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas:
            pass
        else:
            return jsonify({"message": "Você não tem permissão para modificar esta empresa"}), 403

        # Obter dados da requisição
        data = request.get_json()
        tipo_tributo = data.get('tipo_tributo')
        status = data.get('status')  # 'nao_aplicavel' ou 'aplicavel'
        motivo = data.get('motivo', '')

        # Validar dados
        tributos_validos = ['icms', 'icms_st', 'difal', 'ipi', 'pis', 'cofins']
        if tipo_tributo not in tributos_validos:
            return jsonify({"message": "Tipo de tributo inválido"}), 400

        if status not in ['nao_aplicavel', 'aplicavel']:
            return jsonify({"message": "Status inválido"}), 400

        if status == 'aplicavel':
            # Remover status manual (volta ao padrão)
            AuditoriaStatusManual.remover_status(empresa_id, tipo_tributo)
            return jsonify({
                "success": True,
                "message": f"Tributo {tipo_tributo.upper()} voltou ao status padrão"
            }), 200
        else:
            # Marcar como não aplicável
            status_obj = AuditoriaStatusManual.marcar_status(
                empresa_id, tipo_tributo, status, motivo, usuario_id
            )

            return jsonify({
                "success": True,
                "message": f"Tributo {tipo_tributo.upper()} marcado como não aplicável",
                "status": status_obj.to_dict()
            }), 200

    except Exception as e:
        print(f"Erro ao marcar status manual: {str(e)}")
        return jsonify({
            "success": False,
            "message": "Erro interno do servidor"
        }), 500


@dashboard_bp.route('/api/dashboard/empresa/<int:empresa_id>/status-manual', methods=['GET'])
@jwt_required()
def obter_status_manuais(empresa_id):
    """
    Obtém todos os status manuais de uma empresa
    """
    try:
        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Verificar se o usuário tem acesso à empresa
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa:
            return jsonify({"message": "Empresa não encontrada"}), 404

        # Verificar permissões para visualizar a empresa
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            pass
        elif usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id:
            pass
        elif usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas:
            pass
        else:
            return jsonify({"message": "Você não tem permissão para visualizar esta empresa"}), 403

        # Buscar status manuais
        status_manuais = AuditoriaStatusManual.obter_status_empresa(empresa_id)

        return jsonify({
            "success": True,
            "status_manuais": [s.to_dict() for s in status_manuais]
        }), 200

    except Exception as e:
        print(f"Erro ao obter status manuais: {str(e)}")
        return jsonify({
            "success": False,
            "message": "Erro interno do servidor"
        }), 500
