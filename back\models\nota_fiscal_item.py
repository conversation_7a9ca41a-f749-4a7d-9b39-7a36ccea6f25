from .escritorio import db
from sqlalchemy.sql import func

class NotaFiscalItem(db.Model):
    __tablename__ = 'nota_fiscal_item'

    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.<PERSON>ger, db.<PERSON><PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.<PERSON>umn(db.Integer, db.Foreign<PERSON>ey('escritorio.id'), nullable=False)
    cliente_id = db.Column(db.Integer, db.<PERSON>ey('cliente.id'), nullable=False)
    num_item = db.Column(db.Integer)  # Número sequencial do item na nota
    produto_id = db.Column(db.Integer, db.Foreign<PERSON>ey('produto.id'), nullable=False)
    numero_nf = db.Column(db.String(20), nullable=False)
    chave_nf = db.Column(db.String(44))
    data_emissao = db.Column(db.Date, nullable=False)
    cfop = db.Column(db.String(10))
    ncm = db.Column(db.String(20))
    unidade_comercial = db.Column(db.String(10))
    quantidade = db.Column(db.Numeric(15, 4))
    valor_unitario = db.Column(db.Numeric(15, 4))
    valor_total = db.Column(db.Numeric(15, 2))
    tipo_nota = db.Column(db.String(1), default='1')  # 0=entrada, 1=saída
    data_entrada = db.Column(db.Date)  # Para notas de entrada
    data_criacao = db.Column(db.DateTime, server_default=func.now())
    data_atualizacao = db.Column(db.DateTime, server_default=func.now(), onupdate=func.now())

    # Relacionamentos
    empresa = db.relationship('Empresa', backref='nota_fiscal_itens', lazy=True)
    escritorio = db.relationship('Escritorio', backref='nota_fiscal_itens', lazy=True)
    cliente = db.relationship('Cliente', backref='nota_fiscal_itens', lazy=True)
    produto = db.relationship('Produto', backref='nota_fiscal_itens', lazy=True)
    tributos = db.relationship('Tributo', backref='nota_fiscal_item', lazy=True)

    def __repr__(self):
        return f"<NotaFiscalItem {self.id} - NF {self.numero_nf} - Produto {self.produto_id}>"

    def to_dict(self):
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'cliente_id': self.cliente_id,
            'produto_id': self.produto_id,
            'num_item': self.num_item,
            'numero_nf': self.numero_nf,
            'chave_nf': self.chave_nf,
            'data_emissao': self.data_emissao.isoformat() if self.data_emissao else None,
            'cfop': self.cfop,
            'ncm': self.ncm,
            'unidade_comercial': self.unidade_comercial,
            'quantidade': float(self.quantidade) if self.quantidade else None,
            'valor_unitario': float(self.valor_unitario) if self.valor_unitario else None,
            'valor_total': float(self.valor_total) if self.valor_total else None,
            'tipo_nota': self.tipo_nota,
            'data_entrada': self.data_entrada.isoformat() if self.data_entrada else None,
            'data_criacao': self.data_criacao.isoformat() if self.data_criacao else None,
            'data_atualizacao': self.data_atualizacao.isoformat() if self.data_atualizacao else None
        }