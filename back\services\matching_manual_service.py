"""
Serviço para Matching Manual de Itens
Gerencia o processo de matching manual entre itens SPED e XML
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from sqlalchemy import and_, or_, text
from models import db
from models.matching_manual_temporario import MatchingManualTemporario
from models.auditoria_comparativa_impostos import AuditoriaComparativaImpostos
from models.nota_fiscal_item import NotaFiscalItem
from models.item_nota_entrada import ItemNotaEntrada
from models.produto_entrada import ProdutoEntrada
from models.nota_entrada import NotaEntrada
from models.cliente import Cliente


class MatchingManualService:
    """
    Serviço para gerenciar matching manual entre itens SPED e XML
    """
    
    def __init__(self, empresa_id: int, escritorio_id: int, usuario_id: int):
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id
        self.usuario_id = usuario_id
    
    def buscar_candidatos_para_matching(self, sped_item_id: int, mes: int, ano: int) -> Dict[str, Any]:
        """
        Busca itens XML candidatos para fazer matching manual com um item SPED

        Args:
            sped_item_id: ID do item SPED
            mes: Mês para filtrar
            ano: Ano para filtrar

        Returns:
            Dict com item SPED e candidatos XML
        """
        try:
            # Buscar item SPED
            sped_item = self._get_sped_item_details(sped_item_id)
            if not sped_item:
                return {
                    'success': False,
                    'message': 'Item SPED não encontrado'
                }

            # Buscar itens XML DA MESMA NOTA FISCAL (incluindo já pareados da mesma nota)
            xml_candidatos = self._get_xml_items_from_same_note_including_paired(sped_item)

            # Filtrar candidatos por similaridade básica
            xml_candidatos_filtrados = self._filtrar_candidatos_por_similaridade(
                sped_item, xml_candidatos
            )

            return {
                'success': True,
                'sped_item': sped_item,
                'xml_candidatos': xml_candidatos_filtrados,
                'total_candidatos': len(xml_candidatos_filtrados),
                'nota_fiscal': sped_item.get('numero_nf'),
                'chave_nf': sped_item.get('chave_nf')
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'Erro ao buscar candidatos: {str(e)}'
            }
    
    def salvar_matching_manual(self, sped_item_id: int, xml_items_ids: List[int], 
                              justificativa: str = None, confianca: int = 5) -> Dict[str, Any]:
        """
        Salva um matching manual temporário
        
        Args:
            sped_item_id: ID do item SPED
            xml_items_ids: Lista de IDs dos itens XML
            justificativa: Justificativa do usuário
            confianca: Nível de confiança (1-5)
            
        Returns:
            Dict com resultado da operação
        """
        try:
            # Buscar dados do item SPED
            sped_item = self._get_sped_item_details(sped_item_id)
            if not sped_item:
                return {
                    'success': False,
                    'message': 'Item SPED não encontrado'
                }
            
            # Buscar dados dos itens XML
            xml_items_data = []
            for xml_id in xml_items_ids:
                xml_item = self._get_xml_item_details(xml_id)
                if xml_item:
                    xml_items_data.append(xml_item)
            
            if not xml_items_data:
                return {
                    'success': False,
                    'message': 'Nenhum item XML válido encontrado'
                }
            
            # Criar match temporário
            match_temporario = MatchingManualTemporario.criar_match_temporario(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                usuario_id=self.usuario_id,
                sped_item_id=sped_item_id,
                sped_descricao=sped_item['descricao'],
                sped_codigo=sped_item['codigo'],
                xml_items_data=xml_items_data,
                justificativa=justificativa,
                confianca=confianca
            )
            
            db.session.commit()
            
            return {
                'success': True,
                'message': f'Match manual salvo temporariamente. {len(xml_items_data)} itens XML pareados.',
                'match_temporario_id': match_temporario.id,
                'sped_item': sped_item,
                'xml_items': xml_items_data
            }
            
        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'message': f'Erro ao salvar matching manual: {str(e)}'
            }
    
    def processar_matching_temporario(self, match_temporario_id: int) -> Dict[str, Any]:
        """
        Processa um matching temporário, criando os registros definitivos
        
        Args:
            match_temporario_id: ID do match temporário
            
        Returns:
            Dict com resultado do processamento
        """
        try:
            # Buscar match temporário
            match_temporario = MatchingManualTemporario.query.filter_by(
                id=match_temporario_id,
                usuario_id=self.usuario_id,
                status='pendente'
            ).first()
            
            if not match_temporario:
                return {
                    'success': False,
                    'message': 'Match temporário não encontrado ou já processado'
                }
            
            # Processar o match
            sucesso = match_temporario.processar_match()
            
            if sucesso:
                return {
                    'success': True,
                    'message': f'Match manual processado com sucesso. {len(match_temporario.xml_items_ids)} itens pareados.',
                    'itens_processados': len(match_temporario.xml_items_ids)
                }
            else:
                return {
                    'success': False,
                    'message': 'Erro ao processar match temporário'
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'Erro ao processar matching: {str(e)}'
            }
    
    def _get_sped_item_details(self, sped_item_id: int) -> Optional[Dict]:
        """Busca detalhes de um item SPED"""
        try:
            query = text("""
                SELECT 
                    ine.id,
                    ine.cod_item as codigo,
                    ine.descricao_complementar,
                    pe.descricao,
                    pe.unidade,
                    pe.ncm,
                    ine.quantidade,
                    ine.valor_item,
                    ine.cfop,
                    ne.numero_nf,
                    ne.chave_nf,
                    c.razao_social as cliente_nome
                FROM item_nota_entrada ine
                JOIN produto_entrada pe ON ine.produto_entrada_id = pe.id
                JOIN nota_entrada ne ON ine.nota_entrada_id = ne.id
                JOIN cliente c ON ne.cliente_id = c.id
                WHERE ine.id = :sped_item_id 
                AND ine.empresa_id = :empresa_id
            """)
            
            result = db.session.execute(query, {
                'sped_item_id': sped_item_id,
                'empresa_id': self.empresa_id
            }).fetchone()
            
            if result:
                return {
                    'id': result.id,
                    'codigo': result.codigo,
                    'descricao': result.descricao_complementar or result.descricao,
                    'descricao_produto': result.descricao,
                    'unidade': result.unidade,
                    'ncm': result.ncm,
                    'quantidade': float(result.quantidade) if result.quantidade else 0,
                    'valor_item': float(result.valor_item) if result.valor_item else 0,
                    'cfop': result.cfop,
                    'numero_nf': result.numero_nf,
                    'chave_nf': result.chave_nf,
                    'cliente_nome': result.cliente_nome
                }
            
            return None
            
        except Exception as e:
            print(f"Erro ao buscar item SPED {sped_item_id}: {str(e)}")
            return None
    
    def _get_xml_item_details(self, xml_item_id: int) -> Optional[Dict]:
        """Busca detalhes de um item XML"""
        try:
            query = text("""
                SELECT
                    nfi.id,
                    p.codigo as codigo_produto,
                    p.descricao as descricao_produto,
                    nfi.unidade_comercial,
                    nfi.ncm,
                    nfi.quantidade,
                    nfi.valor_unitario,
                    nfi.valor_total,
                    nfi.cfop,
                    nfi.numero_nf,
                    nfi.chave_nf,
                    c.razao_social as cliente_nome
                FROM nota_fiscal_item nfi
                JOIN produto p ON nfi.produto_id = p.id
                JOIN cliente c ON nfi.cliente_id = c.id
                WHERE nfi.id = :xml_item_id
                AND nfi.empresa_id = :empresa_id
            """)

            result = db.session.execute(query, {
                'xml_item_id': xml_item_id,
                'empresa_id': self.empresa_id
            }).fetchone()

            if result:
                return {
                    'id': result.id,
                    'codigo': result.codigo_produto,
                    'descricao': result.descricao_produto,
                    'unidade': result.unidade_comercial,
                    'ncm': result.ncm,
                    'quantidade': float(result.quantidade) if result.quantidade else 0,
                    'valor_unitario': float(result.valor_unitario) if result.valor_unitario else 0,
                    'valor_total': float(result.valor_total) if result.valor_total else 0,
                    'cfop': result.cfop,
                    'numero_nf': result.numero_nf,
                    'chave_nf': result.chave_nf,
                    'cliente_nome': result.cliente_nome
                }

            return None

        except Exception as e:
            print(f"Erro ao buscar item XML {xml_item_id}: {str(e)}")
            return None

    def _get_unmatched_xml_items(self, mes: int, ano: int) -> List[Dict]:
        """Busca itens XML não pareados do período"""
        try:
            query = text("""
                SELECT DISTINCT ON (nfi.id)
                    nfi.id,
                    p.codigo as codigo_produto,
                    p.descricao as descricao_produto,
                    nfi.unidade_comercial,
                    nfi.ncm,
                    nfi.quantidade,
                    nfi.valor_unitario,
                    nfi.valor_total,
                    nfi.cfop,
                    nfi.numero_nf,
                    nfi.chave_nf,
                    c.razao_social as cliente_nome
                FROM nota_fiscal_item nfi
                JOIN produto p ON nfi.produto_id = p.id
                JOIN cliente c ON nfi.cliente_id = c.id
                LEFT JOIN auditoria_comparativa_impostos aci ON nfi.id = aci.xml_item_id
                WHERE nfi.empresa_id = :empresa_id
                AND EXTRACT(MONTH FROM nfi.data_emissao) = :mes
                AND EXTRACT(YEAR FROM nfi.data_emissao) = :ano
                AND nfi.tipo_nota = '0'  -- Apenas notas de entrada
                AND (aci.id IS NULL OR aci.match_type = 'unmatched_xml')  -- Não pareados
                ORDER BY nfi.id, p.descricao
            """)

            results = db.session.execute(query, {
                'empresa_id': self.empresa_id,
                'mes': mes,
                'ano': ano
            }).fetchall()

            xml_items = []
            for result in results:
                xml_items.append({
                    'id': result.id,
                    'codigo': result.codigo_produto,
                    'descricao': result.descricao_produto,
                    'unidade': result.unidade_comercial,
                    'ncm': result.ncm,
                    'quantidade': float(result.quantidade) if result.quantidade else 0,
                    'valor_unitario': float(result.valor_unitario) if result.valor_unitario else 0,
                    'valor_total': float(result.valor_total) if result.valor_total else 0,
                    'cfop': result.cfop,
                    'numero_nf': result.numero_nf,
                    'chave_nf': result.chave_nf,
                    'cliente_nome': result.cliente_nome
                })

            return xml_items

        except Exception as e:
            print(f"Erro ao buscar itens XML não pareados: {str(e)}")
            return []

    def _get_xml_items_from_same_note(self, sped_item: Dict) -> List[Dict]:
        """
        Busca itens XML não pareados DA MESMA NOTA FISCAL que o item SPED

        Args:
            sped_item: Dados do item SPED

        Returns:
            Lista de itens XML da mesma nota
        """
        try:
            # Usar chave_nf ou numero_nf para encontrar a mesma nota
            chave_nf = sped_item.get('chave_nf')
            numero_nf = sped_item.get('numero_nf')

            if not chave_nf and not numero_nf:
                print(f"Item SPED {sped_item.get('id')} não possui chave_nf nem numero_nf")
                return []

            # Construir query baseada nos dados disponíveis
            where_conditions = ["nfi.empresa_id = :empresa_id"]
            params = {'empresa_id': self.empresa_id}

            if chave_nf:
                where_conditions.append("nfi.chave_nf = :chave_nf")
                params['chave_nf'] = chave_nf
            elif numero_nf:
                where_conditions.append("nfi.numero_nf = :numero_nf")
                params['numero_nf'] = numero_nf

            where_clause = " AND ".join(where_conditions)

            query = text(f"""
                SELECT DISTINCT ON (nfi.id)
                    nfi.id,
                    p.codigo as codigo_produto,
                    p.descricao as descricao_produto,
                    nfi.unidade_comercial,
                    nfi.ncm,
                    nfi.quantidade,
                    nfi.valor_unitario,
                    nfi.valor_total,
                    nfi.cfop,
                    nfi.numero_nf,
                    nfi.chave_nf,
                    c.razao_social as cliente_nome
                FROM nota_fiscal_item nfi
                JOIN produto p ON nfi.produto_id = p.id
                JOIN cliente c ON nfi.cliente_id = c.id
                LEFT JOIN auditoria_comparativa_impostos aci ON nfi.id = aci.xml_item_id
                WHERE {where_clause}
                AND nfi.tipo_nota = '0'  -- Apenas notas de entrada
                AND (aci.id IS NULL OR aci.match_type = 'unmatched_xml')  -- Não pareados
                ORDER BY nfi.id, p.descricao
            """)

            results = db.session.execute(query, params).fetchall()

            xml_items = []
            for result in results:
                xml_items.append({
                    'id': result.id,
                    'codigo': result.codigo_produto,
                    'descricao': result.descricao_produto,
                    'unidade': result.unidade_comercial,
                    'ncm': result.ncm,
                    'quantidade': float(result.quantidade) if result.quantidade else 0,
                    'valor_unitario': float(result.valor_unitario) if result.valor_unitario else 0,
                    'valor_total': float(result.valor_total) if result.valor_total else 0,
                    'cfop': result.cfop,
                    'numero_nf': result.numero_nf,
                    'chave_nf': result.chave_nf,
                    'cliente_nome': result.cliente_nome
                })

            print(f"Encontrados {len(xml_items)} itens XML na nota {numero_nf or chave_nf}")
            return xml_items

        except Exception as e:
            print(f"Erro ao buscar itens XML da mesma nota: {str(e)}")
            return []

    def _get_xml_items_from_same_note_including_paired(self, sped_item: Dict) -> List[Dict]:
        """
        Busca itens XML DA MESMA NOTA FISCAL que o item SPED
        INCLUINDO itens já pareados da mesma nota (para permitir reutilização)

        Args:
            sped_item: Dados do item SPED

        Returns:
            Lista de itens XML da mesma nota (incluindo já pareados)
        """
        try:
            # Usar chave_nf ou numero_nf para encontrar a mesma nota
            chave_nf = sped_item.get('chave_nf')
            numero_nf = sped_item.get('numero_nf')

            if not chave_nf and not numero_nf:
                print(f"Item SPED {sped_item.get('id')} não possui chave_nf nem numero_nf")
                return []

            # Construir query baseada nos dados disponíveis
            where_conditions = ["nfi.empresa_id = :empresa_id"]
            params = {'empresa_id': self.empresa_id}

            if chave_nf:
                where_conditions.append("nfi.chave_nf = :chave_nf")
                params['chave_nf'] = chave_nf
            elif numero_nf:
                where_conditions.append("nfi.numero_nf = :numero_nf")
                params['numero_nf'] = numero_nf

            where_clause = " AND ".join(where_conditions)

            query = text(f"""
                SELECT DISTINCT ON (nfi.id)
                    nfi.id,
                    p.codigo as codigo_produto,
                    p.descricao as descricao_produto,
                    nfi.unidade_comercial,
                    nfi.ncm,
                    nfi.quantidade,
                    nfi.valor_unitario,
                    nfi.valor_total,
                    nfi.cfop,
                    nfi.numero_nf,
                    nfi.chave_nf,
                    c.razao_social as cliente_nome,
                    -- Verificar se já está pareado
                    CASE
                        WHEN aci.id IS NOT NULL THEN 'paired'
                        ELSE 'unpaired'
                    END as status_pareamento,
                    aci.match_type
                FROM nota_fiscal_item nfi
                JOIN produto p ON nfi.produto_id = p.id
                JOIN cliente c ON nfi.cliente_id = c.id
                LEFT JOIN auditoria_comparativa_impostos aci ON nfi.id = aci.xml_item_id
                WHERE {where_clause}
                AND nfi.tipo_nota = '0'  -- Apenas notas de entrada
                -- INCLUIR todos os itens da mesma nota (pareados e não pareados)
                ORDER BY nfi.id, p.descricao
            """)

            results = db.session.execute(query, params).fetchall()

            xml_items = []
            for result in results:
                xml_items.append({
                    'id': result.id,
                    'codigo': result.codigo_produto,
                    'descricao': result.descricao_produto,
                    'unidade': result.unidade_comercial,
                    'ncm': result.ncm,
                    'quantidade': float(result.quantidade) if result.quantidade else 0,
                    'valor_unitario': float(result.valor_unitario) if result.valor_unitario else 0,
                    'valor_total': float(result.valor_total) if result.valor_total else 0,
                    'cfop': result.cfop,
                    'numero_nf': result.numero_nf,
                    'chave_nf': result.chave_nf,
                    'cliente_nome': result.cliente_nome,
                    'status_pareamento': result.status_pareamento,
                    'match_type': result.match_type,
                    'ja_pareado': result.status_pareamento == 'paired'
                })

            print(f"Encontrados {len(xml_items)} itens XML na nota {numero_nf or chave_nf} (incluindo já pareados)")
            return xml_items

        except Exception as e:
            print(f"Erro ao buscar itens XML da mesma nota (incluindo pareados): {str(e)}")
            return []

    def _filtrar_candidatos_por_similaridade(self, sped_item: Dict, xml_items: List[Dict]) -> List[Dict]:
        """
        Filtra candidatos XML por similaridade básica com o item SPED
        Aplica filtros de NCM, cliente, valor, etc.
        """
        candidatos_filtrados = []

        for xml_item in xml_items:
            score_similaridade = 0
            detalhes_similaridade = []

            # Verificar NCM (peso alto)
            if sped_item.get('ncm') and xml_item.get('ncm'):
                if sped_item['ncm'] == xml_item['ncm']:
                    score_similaridade += 30
                    detalhes_similaridade.append('NCM idêntico')
                elif sped_item['ncm'][:4] == xml_item['ncm'][:4]:
                    score_similaridade += 15
                    detalhes_similaridade.append('NCM similar (4 dígitos)')

            # Verificar cliente (peso médio)
            if sped_item.get('cliente_nome') and xml_item.get('cliente_nome'):
                if sped_item['cliente_nome'].lower() in xml_item['cliente_nome'].lower() or \
                   xml_item['cliente_nome'].lower() in sped_item['cliente_nome'].lower():
                    score_similaridade += 20
                    detalhes_similaridade.append('Cliente relacionado')

            # Verificar unidade (peso baixo)
            if sped_item.get('unidade') and xml_item.get('unidade'):
                if sped_item['unidade'].upper() == xml_item['unidade'].upper():
                    score_similaridade += 10
                    detalhes_similaridade.append('Unidade idêntica')

            # Verificar CFOP (peso baixo)
            if sped_item.get('cfop') and xml_item.get('cfop'):
                if sped_item['cfop'] == xml_item['cfop']:
                    score_similaridade += 10
                    detalhes_similaridade.append('CFOP idêntico')

            # Verificar similaridade de descrição (básica)
            if sped_item.get('descricao') and xml_item.get('descricao'):
                desc_sped = sped_item['descricao'].lower()
                desc_xml = xml_item['descricao'].lower()

                # Palavras em comum
                palavras_sped = set(desc_sped.split())
                palavras_xml = set(desc_xml.split())
                palavras_comuns = palavras_sped.intersection(palavras_xml)

                if len(palavras_comuns) > 0:
                    score_similaridade += min(len(palavras_comuns) * 5, 20)
                    detalhes_similaridade.append(f'{len(palavras_comuns)} palavras em comum')

            # Adicionar item se tiver alguma similaridade
            if score_similaridade > 0:
                xml_item_com_score = xml_item.copy()
                xml_item_com_score['score_similaridade'] = score_similaridade
                xml_item_com_score['detalhes_similaridade'] = detalhes_similaridade
                candidatos_filtrados.append(xml_item_com_score)

        # Ordenar por score de similaridade (maior primeiro)
        candidatos_filtrados.sort(key=lambda x: x['score_similaridade'], reverse=True)

        # Limitar a 50 candidatos para performance
        return candidatos_filtrados[:50]
