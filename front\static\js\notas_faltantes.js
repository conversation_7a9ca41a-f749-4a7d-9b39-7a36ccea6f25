/**
 * JavaScript para funcionalidades de notas faltantes
 */

/**
 * Renderiza lista de notas faltantes com cards informativos
 */
function renderNotasFaltantesNovo(container, notas, tipo) {
    const tipoTexto = tipo === 'faltantes-entrada' ? 'Entrada' : 'Saída';
    const tipoIcon = tipo === 'faltantes-entrada' ? 'fa-arrow-down text-success' : 'fa-arrow-up text-danger';
    
    let html = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5><i class="fas ${tipoIcon}"></i> Notas Faltantes - ${tipoTexto}</h5>
            <div class="btn-group">
                <button class="btn btn-info btn-sm" id="btn-identificar-faltantes" data-tipo="${tipo}">
                    <i class="fas fa-search"></i> Identificar Faltantes
                </button>
                <button class="btn btn-warning btn-sm" id="btn-alterar-data-modal" disabled>
                    <i class="fas fa-calendar-alt"></i> Alterar Data
                </button>
                <button class="btn btn-danger btn-sm" id="btn-excluir-completa" disabled>
                    <i class="fas fa-trash"></i> Excluir do Sistema
                </button>
            </div>
        </div>
    `;

    // Cards informativos
    if (notas && notas.length > 0) {
        // Separar por tipo de faltante
        const xmlVsSped = notas.filter(n => n.tipo === 'xml_sem_sped' || n.tipo === 'sped_sem_xml');
        const pulosNumeracao = notas.filter(n => n.tipo === 'pulo_numeracao');
        
        html += `
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0"><i class="fas fa-exchange-alt"></i> XML vs SPED</h6>
                        </div>
                        <div class="card-body">
                            <h4 class="text-warning">${xmlVsSped.length}</h4>
                            <p class="mb-0">Notas com divergência entre XML e SPED</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0"><i class="fas fa-sort-numeric-up"></i> Pulos de Numeração</h6>
                        </div>
                        <div class="card-body">
                            <h4 class="text-danger">${pulosNumeracao.length}</h4>
                            <p class="mb-0">Notas faltantes na sequência numérica</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    html += `
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="faltantes-table">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="select-all-faltantes"></th>
                        <th>Tipo</th>
                        <th>Número/Chave NF</th>
                        <th>Data Emissão</th>
                        <th>Data Entrada</th>
                        <th>Origem</th>
                        <th>Observação</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
    `;

    if (notas && notas.length > 0) {
        notas.forEach((nota, index) => {
            const dataEmissao = nota.data_emissao ? new Date(nota.data_emissao).toLocaleDateString('pt-BR') : '-';
            const dataEntrada = nota.data_entrada ? new Date(nota.data_entrada).toLocaleDateString('pt-BR') : '-';
            
            let tipoDisplay = '';
            let numeroDisplay = '';
            
            if (nota.tipo === 'pulo_numeracao') {
                tipoDisplay = '<span class="badge bg-danger">Pulo Numeração</span>';
                numeroDisplay = nota.numero_faltante || (nota.numeros_faltantes ? nota.numeros_faltantes.join(', ') : '-');
            } else {
                tipoDisplay = '<span class="badge bg-warning">XML vs SPED</span>';
                numeroDisplay = nota.numero_nf || '-';
            }
            
            html += `
                <tr>
                    <td><input type="checkbox" class="select-faltante" value="${nota.chave_nf || index}" data-nota='${JSON.stringify(nota)}'></td>
                    <td>${tipoDisplay}</td>
                    <td>${numeroDisplay}</td>
                    <td>${dataEmissao}</td>
                    <td>${dataEntrada}</td>
                    <td><span class="badge bg-${nota.origem === 'XML' ? 'primary' : nota.origem === 'SPED' ? 'secondary' : 'info'}">${nota.origem || 'Sistema'}</span></td>
                    <td class="text-truncate" style="max-width: 200px;" title="${nota.observacao || nota.observacoes || '-'}">${nota.observacao || nota.observacoes || '-'}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            ${nota.chave_nf ? `
                                <button class="btn btn-outline-primary" onclick="editarDataNota('${nota.chave_nf}')" title="Alterar Data">
                                    <i class="fas fa-calendar-alt"></i>
                                </button>
                                <button class="btn btn-outline-success" onclick="marcarComoEncontrada('${nota.chave_nf}')" title="Marcar como Encontrada">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="excluirNotaCompleta('${nota.id || nota.chave_nf}')" title="Excluir do Sistema">
                                    <i class="fas fa-trash"></i>
                                </button>
                            ` : `
                                <span class="text-muted">Pulo de numeração</span>
                            `}
                        </div>
                    </td>
                </tr>
            `;
        });
    } else {
        html += `
            <tr>
                <td colspan="8" class="text-center text-muted">
                    <i class="fas fa-info-circle"></i> Nenhuma nota faltante encontrada
                </td>
            </tr>
        `;
    }

    html += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = html;

    // Configurar eventos
    setupNotasFaltantesEventosNovos();
}

/**
 * Configura eventos para notas faltantes
 */
function setupNotasFaltantesEventosNovos() {
    // Botão identificar faltantes
    const btnIdentificar = document.getElementById('btn-identificar-faltantes');
    if (btnIdentificar) {
        btnIdentificar.addEventListener('click', function() {
            const tipo = this.getAttribute('data-tipo');
            identificarNotasFaltantesNovo(tipo);
        });
    }

    // Checkbox selecionar todos
    const selectAll = document.getElementById('select-all-faltantes');
    if (selectAll) {
        selectAll.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.select-faltante');
            checkboxes.forEach(cb => cb.checked = this.checked);
            updateBulkButtonsNotas();
        });
    }

    // Checkboxes individuais
    const checkboxes = document.querySelectorAll('.select-faltante');
    checkboxes.forEach(cb => {
        cb.addEventListener('change', updateBulkButtonsNotas);
    });

    // Botões de ação em massa
    const btnAlterarData = document.getElementById('btn-alterar-data-modal');
    if (btnAlterarData) {
        btnAlterarData.addEventListener('click', abrirModalAlterarDataMassa);
    }

    const btnExcluirCompleta = document.getElementById('btn-excluir-completa');
    if (btnExcluirCompleta) {
        btnExcluirCompleta.addEventListener('click', excluirNotasCompletaMassa);
    }
}

/**
 * Atualiza estado dos botões de ação em massa
 */
function updateBulkButtonsNotas() {
    const selected = document.querySelectorAll('.select-faltante:checked');
    const btnAlterarData = document.getElementById('btn-alterar-data-modal');
    const btnExcluirCompleta = document.getElementById('btn-excluir-completa');

    const hasSelection = selected.length > 0;

    if (btnAlterarData) btnAlterarData.disabled = !hasSelection;
    if (btnExcluirCompleta) btnExcluirCompleta.disabled = !hasSelection;
}

/**
 * Identifica notas faltantes com novo serviço
 */
function identificarNotasFaltantesNovo(tipo) {
    let empresaId = document.getElementById('empresa-select')?.value || localStorage.getItem('selectedCompany');
    let ano = document.getElementById('year-select')?.value || localStorage.getItem('selectedYear');
    let mes = document.getElementById('month-select')?.value || localStorage.getItem('selectedMonth');

    if (!empresaId || !ano || !mes) {
        showMessage('Selecione empresa, ano e mês para identificar notas faltantes', 'warning');
        return;
    }

    const tipoNota = tipo === 'faltantes-entrada' ? '0' : '1';

    showLoading();

    fetch('/fiscal/api/auditoria-entrada/identificar-faltantes', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            empresa_id: parseInt(empresaId),
            mes: parseInt(mes),
            ano: parseInt(ano),
            tipo_nota: tipoNota
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage(`Identificação concluída! ${data.total_faltantes} notas faltantes encontradas.`, 'success');
            loadTabData(tipo);
        } else {
            showMessage(data.message || 'Erro ao identificar notas faltantes', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao identificar notas faltantes:', error);
        showMessage('Erro ao identificar notas faltantes', 'error');
    });
}

/**
 * Abre modal para alterar data de notas (individual)
 */
function editarDataNota(chaveNf) {
    abrirModalAlterarDataIndividual([chaveNf]);
}

/**
 * Marca nota como encontrada
 */
function marcarComoEncontrada(chaveNf) {
    if (!confirm('Marcar esta nota como encontrada?')) {
        return;
    }

    // Implementar chamada para API
    showMessage('Funcionalidade em desenvolvimento', 'info');
}

/**
 * Exclui nota completamente do sistema
 */
function excluirNotaCompleta(identificador) {
    if (!confirm('Tem certeza que deseja excluir esta nota completamente do sistema? Esta ação não pode ser desfeita.')) {
        return;
    }

    let notaId;
    let notaFaltante;

    // Verificar se o identificador é um ID numérico ou uma chave
    if (!isNaN(identificador) && Number.isInteger(Number(identificador))) {
        // É um ID
        notaId = identificador;
        notaFaltante = currentData.find(nota => nota.id == identificador);
    } else {
        // É uma chave, buscar o ID
        notaFaltante = currentData.find(nota => nota.chave_nf === identificador);
        if (notaFaltante) {
            notaId = notaFaltante.id;
        }
    }

    if (!notaId || !notaFaltante) {
        const chave = notaFaltante?.chave_nf || identificador;
        if (chave) {
            showLoading();
            excluirNotasPorChave([chave], (excluidas) => {
                hideLoading();
                if (excluidas > 0) {
                    showMessage('Nota excluída completamente do sistema', 'success');
                    loadTabData(currentTab);
                } else {
                    showMessage('Nenhuma nota foi excluída', 'error');
                }
            }, () => {
                hideLoading();
                showMessage('Erro ao excluir nota', 'error');
            });
        } else {
            showMessage('Nota não encontrada', 'error');
            console.error('Nota não encontrada:', { identificador, currentData });
        }
        return;
    }

    showLoading();

    fetch(`/fiscal/api/auditoria-entrada/notas-faltantes/${notaId}`, {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage('Nota excluída completamente do sistema', 'success');
            // Recarregar dados
            loadTabData(currentTab);
        } else {
            showMessage(data.message || 'Erro ao excluir nota', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao excluir nota:', error);
        showMessage('Erro ao excluir nota', 'error');
    });
}

/**
 * Exclui notas reais utilizando a chave NF
 */
function excluirNotasPorChave(chaves, onSuccess, onError) {
    fetch('/fiscal/api/auditoria-entrada/excluir-nota-real', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ chaves_nf: chaves })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (onSuccess) onSuccess(data.total_excluidas || chaves.length);
        } else {
            if (onError) onError(data);
        }
    })
    .catch(error => {
        console.error('Erro ao excluir notas:', error);
        if (onError) onError(error);
    });
}

/**
 * Abre modal para alteração de data em massa
 */
function abrirModalAlterarDataMassa() {
    const selected = document.querySelectorAll('.select-faltante:checked');
    const chaves = Array.from(selected).map(cb => cb.value).filter(v => v !== 'undefined');

    if (chaves.length === 0) {
        showMessage('Selecione pelo menos uma nota para alterar', 'warning');
        return;
    }

    abrirModalAlterarDataIndividual(chaves);
}

/**
 * Exclui notas selecionadas completamente do sistema
 */
function excluirNotasCompletaMassa() {
    const selected = document.querySelectorAll('.select-faltante:checked');
    const chaves = Array.from(selected).map(cb => cb.value).filter(v => v !== 'undefined');

    if (chaves.length === 0) {
        showMessage('Selecione pelo menos uma nota para excluir', 'warning');
        return;
    }

    if (!confirm(`Tem certeza que deseja excluir ${chaves.length} notas completamente do sistema? Esta ação não pode ser desfeita.`)) {
        return;
    }

    const idsParaExcluir = [];
    const chavesParaExcluir = [];
    chaves.forEach(chave => {
        const nota = currentData.find(n => n.chave_nf === chave);
        if (nota) {
            if (nota.id) {
                idsParaExcluir.push(nota.id);
            } else {
                chavesParaExcluir.push(chave);
            }
        }
    });

    if (idsParaExcluir.length === 0 && chavesParaExcluir.length === 0) {
        showMessage('Nenhuma nota válida encontrada para exclusão', 'error');
        return;
    }

    showLoading();

    // Excluir uma por uma (pode ser otimizado para exclusão em massa no backend)
    let exclusoesRealizadas = 0;
    let erros = 0;

    const finalizar = () => {
        hideLoading();
        if (exclusoesRealizadas > 0) {
            showMessage(`${exclusoesRealizadas} notas excluídas com sucesso${erros > 0 ? ` (${erros} erros)` : ''}`, 'success');
            loadTabData(currentTab);
        } else {
            showMessage('Nenhuma nota foi excluída', 'error');
        }
    };

    const excluirProxima = (index) => {
        if (index >= idsParaExcluir.length) {
            if (chavesParaExcluir.length > 0) {
                excluirNotasPorChave(chavesParaExcluir, exclusoes => {
                    exclusoesRealizadas += exclusoes;
                    finalizar();
                }, () => {
                    erros += chavesParaExcluir.length;
                    finalizar();
                });
            } else {
                finalizar();
            }
            return;
        }

        const notaId = idsParaExcluir[index];

        fetch(`/fiscal/api/auditoria-entrada/notas-faltantes/${notaId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${getToken()}`,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                exclusoesRealizadas++;
            } else {
                erros++;
            }
            excluirProxima(index + 1);
        })
        .catch(error => {
            console.error('Erro ao excluir nota:', error);
            erros++;
            excluirProxima(index + 1);
        });
    };

    excluirProxima(0);
}

/**
 * Abre modal para alterar data de notas
 */
function abrirModalAlterarDataIndividual(chaves) {
    const isMultiple = chaves.length > 1;
    const titulo = isMultiple ? `Alterar Data - ${chaves.length} notas selecionadas` : 'Alterar Data da Nota';

    const modalHtml = `
        <div class="modal fade" id="modalAlterarDataNota" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${titulo}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="formAlterarDataNota">
                            <div class="mb-3">
                                <label for="novaDataEntrada" class="form-label">Nova Data de Entrada *</label>
                                <input type="text" class="form-control" id="novaDataEntrada"
                                       placeholder="dd/mm/aaaa" pattern="\\d{2}/\\d{2}/\\d{4}" required>
                                <div class="form-text">Use o formato brasileiro: dd/mm/aaaa</div>
                            </div>
                            <div class="mb-3">
                                <label for="motivoAlteracao" class="form-label">Motivo da Alteração</label>
                                <textarea class="form-control" id="motivoAlteracao" rows="3"
                                          placeholder="Descreva o motivo da alteração..."></textarea>
                            </div>
                            ${isMultiple ? `
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    Esta alteração será aplicada a ${chaves.length} notas selecionadas.
                                </div>
                            ` : ''}
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-primary" onclick="salvarAlteracaoDataNota(['${chaves.join("','")}'])">
                            <i class="fas fa-save"></i> Salvar Alteração
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remover modal existente se houver
    const existingModal = document.getElementById('modalAlterarDataNota');
    if (existingModal) {
        existingModal.remove();
    }

    // Adicionar modal ao DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Mostrar modal
    const modal = new bootstrap.Modal(document.getElementById('modalAlterarDataNota'));
    modal.show();

    // Configurar máscara de data
    const inputData = document.getElementById('novaDataEntrada');
    inputData.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length >= 2) value = value.substring(0,2) + '/' + value.substring(2);
        if (value.length >= 5) value = value.substring(0,5) + '/' + value.substring(5,9);
        e.target.value = value;
    });
}

/**
 * Salva alteração de data das notas
 */
function salvarAlteracaoDataNota(chaves) {
    const novaDataEntrada = document.getElementById('novaDataEntrada').value;
    const motivo = document.getElementById('motivoAlteracao').value;

    if (!novaDataEntrada) {
        showMessage('Informe a nova data de entrada', 'warning');
        return;
    }

    // Validar formato da data
    const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
    if (!dateRegex.test(novaDataEntrada)) {
        showMessage('Data deve estar no formato dd/mm/aaaa', 'warning');
        return;
    }

    showLoading();

    fetch('/fiscal/api/auditoria-entrada/alterar-data-nota-real', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            chaves_nf: chaves,
            nova_data_entrada: novaDataEntrada,
            motivo: motivo || 'Alteração via gestão de XMLs'
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showMessage(`${data.total_alteradas} notas alteradas com sucesso`, 'success');

            // Fechar modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('modalAlterarDataNota'));
            modal.hide();

            // Recarregar dados
            loadTabData(currentTab);
        } else {
            showMessage(data.message || 'Erro ao alterar datas', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Erro ao alterar datas:', error);
        showMessage('Erro ao alterar datas', 'error');
    });
}
