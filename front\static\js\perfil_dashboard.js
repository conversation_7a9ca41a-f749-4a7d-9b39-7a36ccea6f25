/**
 * Perfil Dashboard - Auditoria Fiscal
 * Funções para gerenciar o perfil do escritório no dashboard
 */

// Variáveis globais
let perfilData = null;

/**
 * Inicializa a página de perfil
 */
function initPerfil() {
  // Verificar se estamos na seção de perfil
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('section') !== 'perfil') {
    return;
  }

  // Criar seção de perfil se não existir
  createPerfilSection();

  // Carregar dados do perfil
  carregarDadosPerfil();

  // Configurar event listeners
  setupPerfilEventListeners();
}

/**
 * Cria a seção de perfil no dashboard
 */
function createPerfilSection() {
  // Verificar se já existe
  if (document.getElementById('page-perfil')) {
    document.querySelectorAll('.page-section').forEach((section) => {
      section.classList.remove('active');
    });
    document.getElementById('page-perfil').classList.add('active');
    return;
  }

  // Encontrar o container principal
  const mainContent = document.querySelector('.dashboard-content');
  if (!mainContent) {
    return;
  }

  // Criar seção de perfil
  const perfilSection = document.createElement('div');
  perfilSection.id = 'page-perfil';
  perfilSection.className = 'page-section';

  perfilSection.innerHTML = `
    <div class="section-header">
      <h2><i class="fas fa-user-cog"></i> Meu Perfil</h2>
      <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">
        <i class="fas fa-arrow-left"></i> Voltar
      </button>
    </div>

    <!-- Alert container -->
    <div id="perfil-alert-container"></div>

    <!-- Perfil Form -->
    <div class="row">
      <div class="col-lg-8">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fas fa-building"></i>
              Informações do Escritório
            </h5>
          </div>
          <div class="card-body">
            <form id="perfil-form">
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="perfil-nome" class="form-label">Nome do Escritório *</label>
                    <input type="text" class="form-control" id="perfil-nome" required>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="perfil-cnpj" class="form-label">CNPJ *</label>
                    <input type="text" class="form-control" id="perfil-cnpj" required>
                  </div>
                </div>
              </div>

              <div class="mb-3">
                <label for="perfil-responsavel" class="form-label">Responsável</label>
                <input type="text" class="form-control" id="perfil-responsavel"
                       placeholder="Nome do responsável pelo escritório">
              </div>

              <div class="mb-3">
                <label for="perfil-endereco" class="form-label">Endereço</label>
                <textarea class="form-control" id="perfil-endereco" rows="3"
                          placeholder="Endereço completo do escritório"></textarea>
              </div>

              <div class="mb-3">
                <label for="perfil-cor-relatorio" class="form-label">Cor dos Relatórios</label>
                <div class="input-group">
                  <input type="color" class="form-control form-control-color" id="perfil-cor-relatorio"
                         value="#6f42c1" title="Escolha a cor padrão para os relatórios">
                  <span class="input-group-text">
                    <i class="fas fa-palette"></i>
                  </span>
                </div>
                <div class="form-text">Esta cor será usada nos títulos e elementos destacados dos relatórios PDF.</div>
              </div>

              <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save"></i> Salvar Alterações
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div class="col-lg-4">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fas fa-image"></i>
              Logo/Emblema
            </h5>
          </div>
          <div class="card-body text-center">
            <div id="perfil-logo-preview" class="mb-3">
              <p class="text-muted">Carregando...</p>
            </div>

            <div class="mb-3">
              <label for="perfil-logo-input" class="form-label">Enviar novo logo</label>
              <input type="file" class="form-control" id="perfil-logo-input"
                     accept="image/png,image/jpeg,image/jpg,image/gif,image/svg+xml">
              <div class="form-text">
                Formatos aceitos: PNG, JPG, JPEG, GIF, SVG<br>
                Tamanho máximo: 5MB
              </div>
            </div>

            <button type="button" class="btn btn-outline-primary" id="perfil-upload-logo-btn">
              <i class="fas fa-upload"></i> Selecionar Arquivo
            </button>
          </div>
        </div>

        <div class="card mt-3">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fas fa-info-circle"></i>
              Informações
            </h5>
          </div>
          <div class="card-body">
            <p class="card-text">
              <small class="text-muted">
                O logo do escritório será utilizado nos relatórios de auditoria fiscal.
                Recomendamos usar uma imagem com boa qualidade e fundo transparente.
              </small>
            </p>

            <p class="card-text">
              <small class="text-muted">
                As informações do responsável também aparecerão nos relatórios,
                identificando o profissional responsável pela auditoria.
              </small>
            </p>
          </div>
        </div>
      </div>
    </div>
  `;

  // Adicionar ao container principal
  mainContent.appendChild(perfilSection);

  // Mostrar a seção
  document.querySelectorAll('.page-section').forEach((section) => {
    section.classList.remove('active');
  });
  perfilSection.classList.add('active');
}

/**
 * Carrega os dados do perfil
 */
function carregarDadosPerfil() {
  const token = localStorage.getItem('token');
  if (!token) {
    mostrarAlertPerfil(
      'Token de autenticação não encontrado. Faça login novamente.',
      'danger',
    );
    return;
  }

  fetch('/fiscal/api/perfil', {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`Erro ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then((data) => {
      if (data.success) {
        perfilData = data.escritorio;
        preencherFormularioPerfil(data.escritorio);
        carregarLogoPerfil(data.escritorio.logo_path);
      } else {
        throw new Error(data.message || 'Erro ao carregar dados do perfil');
      }
    })
    .catch((error) => {
      mostrarAlertPerfil(`Erro ao carregar perfil: ${error.message}`, 'danger');
    });
}

/**
 * Preenche o formulário com os dados do perfil
 */
function preencherFormularioPerfil(escritorio) {
  document.getElementById('perfil-nome').value = escritorio.nome || '';
  document.getElementById('perfil-cnpj').value = escritorio.cnpj || '';
  document.getElementById('perfil-responsavel').value =
    escritorio.responsavel || '';
  document.getElementById('perfil-endereco').value = escritorio.endereco || '';
  document.getElementById('perfil-cor-relatorio').value =
    escritorio.cor_relatorio || '#6f42c1';
}

/**
 * Carrega e exibe o logo do escritório
 */
function carregarLogoPerfil(logoPath) {
  const logoPreview = document.getElementById('perfil-logo-preview');

  if (logoPath) {
    logoPreview.innerHTML = `
      <img src="${logoPath}" alt="Logo do Escritório"
           style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;">
    `;
  } else {
    logoPreview.innerHTML = '<p class="text-muted">Nenhum logo cadastrado</p>';
  }
}

/**
 * Configura os event listeners da página de perfil
 */
function setupPerfilEventListeners() {
  // Formulário de perfil
  const perfilForm = document.getElementById('perfil-form');
  if (perfilForm) {
    perfilForm.addEventListener('submit', salvarPerfil);
  }

  // Botão de upload de logo
  const uploadBtn = document.getElementById('perfil-upload-logo-btn');
  const logoInput = document.getElementById('perfil-logo-input');

  if (uploadBtn && logoInput) {
    uploadBtn.addEventListener('click', () => logoInput.click());
    logoInput.addEventListener('change', uploadLogo);
  }
}

/**
 * Salva as alterações do perfil
 */
function salvarPerfil(event) {
  event.preventDefault();

  const token = localStorage.getItem('token');
  if (!token) {
    mostrarAlertPerfil(
      'Token de autenticação não encontrado. Faça login novamente.',
      'danger',
    );
    return;
  }

  const dados = {
    nome: document.getElementById('perfil-nome').value,
    cnpj: document.getElementById('perfil-cnpj').value,
    responsavel: document.getElementById('perfil-responsavel').value,
    endereco: document.getElementById('perfil-endereco').value,
    cor_relatorio: document.getElementById('perfil-cor-relatorio').value,
  };

  fetch('/fiscal/api/perfil', {
    method: 'PUT',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(dados),
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`Erro ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then((data) => {
      if (data.success) {
        mostrarAlertPerfil('Perfil atualizado com sucesso!', 'success');
        perfilData = data.escritorio;
      } else {
        throw new Error(data.message || 'Erro ao salvar perfil');
      }
    })
    .catch((error) => {
      mostrarAlertPerfil(`Erro ao salvar perfil: ${error.message}`, 'danger');
    });
}

/**
 * Faz upload do logo
 */
function uploadLogo(event) {
  const file = event.target.files[0];
  if (!file) return;

  // Validar tamanho (5MB)
  if (file.size > 5 * 1024 * 1024) {
    mostrarAlertPerfil('Arquivo muito grande. Tamanho máximo: 5MB', 'danger');
    return;
  }

  const token = localStorage.getItem('token');
  if (!token) {
    mostrarAlertPerfil(
      'Token de autenticação não encontrado. Faça login novamente.',
      'danger',
    );
    return;
  }

  const formData = new FormData();
  formData.append('logo', file);

  fetch('/fiscal/api/perfil/logo', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: formData,
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`Erro ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then((data) => {
      if (data.success) {
        mostrarAlertPerfil('Logo enviado com sucesso!', 'success');
        carregarLogoPerfil(data.logo_path);
      } else {
        throw new Error(data.message || 'Erro ao enviar logo');
      }
    })
    .catch((error) => {
      mostrarAlertPerfil(`Erro ao enviar logo: ${error.message}`, 'danger');
    });
}

/**
 * Mostra alerta na página de perfil
 */
function mostrarAlertPerfil(message, type = 'info') {
  const alertContainer = document.getElementById('perfil-alert-container');
  if (!alertContainer) return;

  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;

  alertContainer.innerHTML = '';
  alertContainer.appendChild(alertDiv);

  // Auto-remover após 5 segundos
  setTimeout(() => {
    if (alertDiv.parentNode) {
      alertDiv.remove();
    }
  }, 5000);
}

// Inicializar quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function () {
  setTimeout(initPerfil, 500);
});

// Verificar mudanças na URL
window.addEventListener('popstate', function () {
  setTimeout(initPerfil, 100);
});

// Exportar funções para uso global
window.initPerfil = initPerfil;
